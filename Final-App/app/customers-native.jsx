import React, { useState, useEffect } from 'react';
import { View, FlatList, StyleSheet, RefreshControl, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import MainLayout from '../src/components/layout/MainLayout';
import { ListCard, FilterChips } from '../src/components/ui';
import { useTheme } from '../src/context/ThemeContext';

const CustomersNative = () => {
  const router = useRouter();
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState('customers');
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  console.log('Customers Native screen rendering...');

  // Mock customers data
  const mockCustomers = [
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'active',
      totalOrders: 12,
      totalSpent: 1250.00,
      lastOrder: '2024-01-20',
      avatar: null,
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'vip',
      totalOrders: 25,
      totalSpent: 3200.00,
      lastOrder: '2024-01-22',
      avatar: null,
    },
    {
      id: '3',
      name: 'Michael Brown',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'active',
      totalOrders: 8,
      totalSpent: 680.00,
      lastOrder: '2024-01-18',
      avatar: null,
    },
    {
      id: '4',
      name: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'inactive',
      totalOrders: 3,
      totalSpent: 150.00,
      lastOrder: '2023-12-15',
      avatar: null,
    },
  ];

  const filterOptions = [
    { id: 'all', label: 'All' },
    { id: 'active', label: 'Active' },
    { id: 'vip', label: 'VIP' },
    { id: 'inactive', label: 'Inactive' },
  ];

  useEffect(() => {
    loadCustomers();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [searchQuery, selectedFilter, customers]);

  const loadCustomers = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCustomers(mockCustomers);
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCustomers(mockCustomers);
    } catch (error) {
      console.error('Error refreshing customers:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const filterCustomers = () => {
    let filtered = customers;

    // Filter by status
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(customer => customer.status === selectedFilter);
    }

    // Filter by search query
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(customer =>
        customer.name.toLowerCase().includes(query) ||
        customer.email.toLowerCase().includes(query) ||
        customer.phone.includes(query)
      );
    }

    setFilteredCustomers(filtered);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'vip': return '#9c27b0';
      case 'active': return '#4caf50';
      case 'inactive': return '#f44336';
      default: return '#666666';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'vip': return 'VIP';
      case 'active': return 'Active';
      case 'inactive': return 'Inactive';
      default: return status;
    }
  };

  const handleCustomerPress = (customer) => {
    console.log('Customer pressed:', customer.id);
    // Navigate to customer details
  };

  const handleTabPress = (tabId) => {
    console.log('Tab pressed:', tabId);
    setActiveTab(tabId);

    switch (tabId) {
      case 'dashboard':
        router.push('/dashboard-native');
        break;
      case 'orders':
        router.push('/orders-simple');
        break;
      case 'inventory':
        console.log('Navigate to Inventory');
        break;
      case 'more':
        console.log('Navigate to More');
        break;
      default:
        break;
    }
  };

  const handleAddCustomer = () => {
    console.log('Add customer pressed');
    Alert.alert('Add Customer', 'Navigate to add customer screen');
  };

  const handleSearch = (query) => {
    console.log('Search customers:', query);
    setSearchQuery(query);
  };

  const handleFilterPress = () => {
    console.log('Filter customers');
    Alert.alert('Filters', 'Customer filter options');
  };

  const renderCustomerItem = ({ item }) => (
    <View style={[
      styles.customerCard,
      {
        backgroundColor: theme.colors.surface,
        borderColor: theme.colors.outlineVariant,
        ...theme.shadows.small,
      }
    ]}>
      <TouchableOpacity
        style={styles.customerContent}
        onPress={() => handleCustomerPress(item)}
        activeOpacity={0.7}
      >
        {/* Left Section - Avatar */}
        <View style={[
          styles.avatar,
          {
            backgroundColor: getStatusColor(item.status) + '20', // 20% opacity
            borderColor: getStatusColor(item.status),
          }
        ]}>
          <Ionicons
            name="person"
            size={24}
            color={getStatusColor(item.status)}
          />
        </View>

        {/* Center Section - Customer Info */}
        <View style={styles.customerInfo}>
          <Text style={[
            styles.customerName,
            {
              color: theme.colors.onSurface,
              ...theme.typography.titleMedium,
            }
          ]}>
            {item.name}
          </Text>

          <Text style={[
            styles.customerEmail,
            {
              color: theme.colors.onSurfaceVariant,
              ...theme.typography.bodyMedium,
            }
          ]}>
            {item.email}
          </Text>

          <View style={styles.customerStats}>
            <View style={styles.statItem}>
              <Ionicons
                name="receipt-outline"
                size={14}
                color={theme.colors.onSurfaceVariant}
              />
              <Text style={[
                styles.statText,
                {
                  color: theme.colors.onSurfaceVariant,
                  ...theme.typography.bodySmall,
                }
              ]}>
                {item.totalOrders} orders
              </Text>
            </View>

            <View style={styles.statItem}>
              <Ionicons
                name="card-outline"
                size={14}
                color={theme.colors.onSurfaceVariant}
              />
              <Text style={[
                styles.statText,
                {
                  color: theme.colors.onSurfaceVariant,
                  ...theme.typography.bodySmall,
                }
              ]}>
                ${item.totalSpent.toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        {/* Right Section - Status & Arrow */}
        <View style={styles.rightSection}>
          <View style={[
            styles.statusBadge,
            {
              backgroundColor: getStatusColor(item.status) + '15', // 15% opacity
              borderColor: getStatusColor(item.status) + '40', // 40% opacity
            }
          ]}>
            <Text style={[
              styles.statusText,
              {
                color: getStatusColor(item.status),
                ...theme.typography.labelSmall,
              }
            ]}>
              {getStatusLabel(item.status)}
            </Text>
          </View>

          <Ionicons
            name="chevron-forward"
            size={20}
            color={theme.colors.onSurfaceVariant}
            style={styles.chevron}
          />
        </View>
      </TouchableOpacity>
    </View>
  );

  return (
    <MainLayout
      title="Customers"
      subtitle={`${filteredCustomers.length} customers`}
      activeTab={activeTab}
      onTabPress={handleTabPress}
      showSearchBar={true}
      showFAB={true}
      topBarProps={{
        showBackButton: true,
        onBackPress: () => router.back(),
        rightIcon: 'ellipsis-vertical',
        onRightPress: () => console.log('More options'),
      }}
      searchBarProps={{
        placeholder: "Search customers...",
        value: searchQuery,
        onChangeText: handleSearch,
        showFilter: true,
        onFilterPress: handleFilterPress,
      }}
      fabProps={{
        icon: 'person-add',
        onPress: handleAddCustomer,
        variant: 'primary',
      }}
    >
      <View style={styles.container}>
        {/* Filter Chips */}
        <FilterChips
          filters={filterOptions}
          selectedFilter={selectedFilter}
          onFilterSelect={setSelectedFilter}
        />

        {/* Customer List */}
        <FlatList
          data={filteredCustomers}
          renderItem={renderCustomerItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      </View>
    </MainLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 100, // Space for FAB
    paddingTop: 8,
  },
  customerCard: {
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
  },
  customerContent: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    marginRight: 16,
  },
  customerInfo: {
    flex: 1,
  },
  customerName: {
    marginBottom: 4,
  },
  customerEmail: {
    marginBottom: 8,
  },
  customerStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    marginLeft: 4,
  },
  rightSection: {
    alignItems: 'flex-end',
    gap: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  statusText: {
    fontWeight: '500',
  },
  chevron: {
    marginTop: 4,
  },
});

export default CustomersNative;
