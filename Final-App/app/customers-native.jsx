import React, { useState, useEffect } from 'react';
import { View, FlatList, StyleSheet, RefreshControl, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import MainLayout from '../src/components/layout/MainLayout';
import { ListCard, FilterChips } from '../src/components/ui';
import { useTheme } from '../src/context/ThemeContext';

const CustomersNative = () => {
  const router = useRouter();
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState('customers');
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  console.log('Customers Native screen rendering...');

  // Mock customers data
  const mockCustomers = [
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'active',
      totalOrders: 12,
      totalSpent: 1250.00,
      lastOrder: '2024-01-20',
      avatar: null,
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'vip',
      totalOrders: 25,
      totalSpent: 3200.00,
      lastOrder: '2024-01-22',
      avatar: null,
    },
    {
      id: '3',
      name: 'Michael Brown',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'active',
      totalOrders: 8,
      totalSpent: 680.00,
      lastOrder: '2024-01-18',
      avatar: null,
    },
    {
      id: '4',
      name: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'inactive',
      totalOrders: 3,
      totalSpent: 150.00,
      lastOrder: '2023-12-15',
      avatar: null,
    },
  ];

  const filterOptions = [
    { id: 'all', label: 'All' },
    { id: 'active', label: 'Active' },
    { id: 'vip', label: 'VIP' },
    { id: 'inactive', label: 'Inactive' },
  ];

  useEffect(() => {
    loadCustomers();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [searchQuery, selectedFilter, customers]);

  const loadCustomers = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCustomers(mockCustomers);
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCustomers(mockCustomers);
    } catch (error) {
      console.error('Error refreshing customers:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const filterCustomers = () => {
    let filtered = customers;

    // Filter by status
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(customer => customer.status === selectedFilter);
    }

    // Filter by search query
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(customer =>
        customer.name.toLowerCase().includes(query) ||
        customer.email.toLowerCase().includes(query) ||
        customer.phone.includes(query)
      );
    }

    setFilteredCustomers(filtered);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'vip': return '#9c27b0';
      case 'active': return '#4caf50';
      case 'inactive': return '#f44336';
      default: return '#666666';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'vip': return 'VIP';
      case 'active': return 'Active';
      case 'inactive': return 'Inactive';
      default: return status;
    }
  };

  const handleCustomerPress = (customer) => {
    console.log('Customer pressed:', customer.id);
    // Navigate to customer details
  };

  const handleTabPress = (tabId) => {
    console.log('Tab pressed:', tabId);
    setActiveTab(tabId);

    switch (tabId) {
      case 'dashboard':
        router.push('/dashboard-native');
        break;
      case 'orders':
        router.push('/orders-simple');
        break;
      case 'inventory':
        console.log('Navigate to Inventory');
        break;
      case 'more':
        console.log('Navigate to More');
        break;
      default:
        break;
    }
  };

  const handleAddCustomer = () => {
    console.log('Add customer pressed');
    Alert.alert('Add Customer', 'Navigate to add customer screen');
  };

  const handleSearch = (query) => {
    console.log('Search customers:', query);
    setSearchQuery(query);
  };

  const handleFilterPress = () => {
    console.log('Filter customers');
    Alert.alert('Filters', 'Customer filter options');
  };

  const renderCustomerItem = ({ item }) => (
    <ListCard
      title={item.name}
      subtitle={item.email}
      description={`${item.totalOrders} orders • $${item.totalSpent.toFixed(2)} spent`}
      leftIcon={
        <View style={[styles.avatar, { backgroundColor: getStatusColor(item.status) }]}>
          <Ionicons name="person" size={20} color="#ffffff" />
        </View>
      }
      badge={{
        text: getStatusLabel(item.status),
        color: getStatusColor(item.status),
      }}
      rightIcon={
        <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
      }
      onPress={() => handleCustomerPress(item)}
    />
  );

  return (
    <MainLayout
      title="Customers"
      subtitle={`${filteredCustomers.length} customers`}
      activeTab={activeTab}
      onTabPress={handleTabPress}
      showSearchBar={true}
      showFAB={true}
      topBarProps={{
        showBackButton: true,
        onBackPress: () => router.back(),
        rightIcon: 'ellipsis-vertical',
        onRightPress: () => console.log('More options'),
      }}
      searchBarProps={{
        placeholder: "Search customers...",
        value: searchQuery,
        onChangeText: handleSearch,
        showFilter: true,
        onFilterPress: handleFilterPress,
      }}
      fabProps={{
        icon: 'person-add',
        onPress: handleAddCustomer,
        variant: 'primary',
      }}
    >
      <View style={styles.container}>
        {/* Filter Chips */}
        <FilterChips
          filters={filterOptions}
          selectedFilter={selectedFilter}
          onFilterSelect={setSelectedFilter}
        />

        {/* Customer List */}
        <FlatList
          data={filteredCustomers}
          renderItem={renderCustomerItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      </View>
    </MainLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 100, // Space for FAB
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CustomersNative;
