import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, RefreshControl, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import MainLayout from '../src/components/layout/MainLayout';
import { StatCard } from '../src/components/ui';
import { useTheme } from '../src/context/ThemeContext';

const DashboardNative = () => {
  const router = useRouter();
  const { theme, toggleTheme } = useTheme();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  console.log('Dashboard Native screen rendering...');

  // Mock dashboard data
  const [dashboardData, setDashboardData] = useState({
    totalOrders: 25,
    revenue: 1250,
    pendingOrders: 8,
    completedOrders: 17,
    totalCustomers: 45,
    activeCustomers: 38,
    lowStockItems: 5,
    todayRevenue: 320,
  });

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Dashboard data refreshed');
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleTabPress = (tabId) => {
    console.log('Tab pressed:', tabId);
    setActiveTab(tabId);

    switch (tabId) {
      case 'customers':
        router.push('/customers');
        break;
      case 'orders':
        router.push('/orders-simple');
        break;
      case 'inventory':
        console.log('Navigate to Inventory');
        break;
      case 'more':
        console.log('Navigate to More');
        break;
      default:
        break;
    }
  };

  const handleAddAction = () => {
    console.log('Add action pressed');
    Alert.alert(
      'Quick Add',
      'What would you like to add?',
      [
        { text: 'New Order', onPress: () => console.log('Add Order') },
        { text: 'New Customer', onPress: () => console.log('Add Customer') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleSearch = (query) => {
    console.log('Search query:', query);
    setSearchQuery(query);
  };

  const handleFilterPress = () => {
    console.log('Filter pressed');
    Alert.alert('Filters', 'Filter options coming soon!');
  };

  const statsData = [
    {
      title: 'Total Orders',
      value: dashboardData.totalOrders.toString(),
      subtitle: '+3 from yesterday',
      color: '#6200ee',
      icon: <Ionicons name="receipt" size={24} color="#6200ee" />,
      onPress: () => router.push('/orders-simple'),
    },
    {
      title: 'Revenue',
      value: `$${dashboardData.revenue}`,
      subtitle: `Today: $${dashboardData.todayRevenue}`,
      color: '#4caf50',
      icon: <Ionicons name="trending-up" size={24} color="#4caf50" />,
    },
    {
      title: 'Customers',
      value: dashboardData.totalCustomers.toString(),
      subtitle: `${dashboardData.activeCustomers} active`,
      color: '#ff9800',
      icon: <Ionicons name="people" size={24} color="#ff9800" />,
      onPress: () => router.push('/customers'),
    },
    {
      title: 'Pending',
      value: dashboardData.pendingOrders.toString(),
      subtitle: 'Orders to process',
      color: '#f44336',
      icon: <Ionicons name="time" size={24} color="#f44336" />,
    },
  ];

  const quickActions = [
    {
      title: 'New Order',
      icon: 'add-circle',
      color: '#6200ee',
      onPress: () => console.log('New Order'),
    },
    {
      title: 'Add Customer',
      icon: 'person-add',
      color: '#4caf50',
      onPress: () => console.log('Add Customer'),
    },
    {
      title: 'Inventory',
      icon: 'cube',
      color: '#ff9800',
      onPress: () => console.log('Inventory'),
    },
    {
      title: 'Reports',
      icon: 'bar-chart',
      color: '#9c27b0',
      onPress: () => console.log('Reports'),
    },
  ];

  return (
    <MainLayout
      title="Tailor Manager"
      subtitle="Welcome back!"
      activeTab={activeTab}
      onTabPress={handleTabPress}
      showSearchBar={true}
      showFAB={true}
      topBarProps={{
        rightIcon: theme.mode === 'dark' ? 'sunny' : 'moon',
        onRightPress: toggleTheme,
      }}
      searchBarProps={{
        placeholder: "Search orders, customers...",
        value: searchQuery,
        onChangeText: handleSearch,
        showFilter: true,
        onFilterPress: handleFilterPress,
      }}
      fabProps={{
        icon: 'add',
        onPress: handleAddAction,
        variant: 'primary',
      }}
    >
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          {statsData.map((stat, index) => (
            <View key={index} style={styles.statItem}>
              <StatCard
                title={stat.title}
                value={stat.value}
                subtitle={stat.subtitle}
                icon={stat.icon}
                color={stat.color}
                onPress={stat.onPress}
              />
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <View key={index} style={styles.quickActionItem}>
                <StatCard
                  title={action.title}
                  value=""
                  icon={<Ionicons name={action.icon} size={32} color={action.color} />}
                  color={action.color}
                  onPress={action.onPress}
                  style={styles.quickActionCard}
                />
              </View>
            ))}
          </View>
        </View>

        {/* Recent Activity Section */}
        <View style={styles.section}>
          {/* This will be expanded with recent orders, notifications, etc. */}
        </View>
      </ScrollView>
    </MainLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    paddingBottom: 100, // Space for FAB
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 8,
    marginTop: 8,
  },
  statItem: {
    width: '50%',
    paddingHorizontal: 8,
    marginBottom: 16,
  },
  section: {
    marginTop: 16,
    paddingHorizontal: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  quickActionItem: {
    width: '50%',
    paddingHorizontal: 8,
    marginBottom: 16,
  },
  quickActionCard: {
    minHeight: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default DashboardNative;
