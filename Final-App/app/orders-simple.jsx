import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import MainLayout from '../src/components/layout/MainLayout';
import { FilterChips } from '../src/components/ui';
import { useTheme } from '../src/context/ThemeContext';

// Enhanced mock orders data
const mockOrders = [
  {
    id: '1',
    orderNumber: 'ORD-2024-001',
    customerName: '<PERSON>',
    customerPhone: '+****************',
    status: 'in-progress',
    priority: 'high',
    totalAmount: 450.00,
    paidAmount: 200.00,
    dueDate: '2024-01-25',
    orderDate: '2024-01-15',
    items: [
      { name: 'Custom Suit', quantity: 1, status: 'cutting' },
      { name: 'Shirt Alterations', quantity: 2, status: 'completed' },
    ],
  },
  {
    id: '2',
    orderNumber: 'ORD-2024-002',
    customerName: '<PERSON>',
    customerPhone: '+****************',
    status: 'pending',
    priority: 'medium',
    totalAmount: 280.00,
    paidAmount: 0.00,
    dueDate: '2024-02-01',
    orderDate: '2024-01-18',
    items: [
      { name: 'Dress Hemming', quantity: 1, status: 'pending' },
      { name: 'Jacket Tailoring', quantity: 1, status: 'pending' },
    ],
  },
  {
    id: '3',
    orderNumber: 'ORD-2024-003',
    customerName: 'Michael Brown',
    customerPhone: '+****************',
    status: 'completed',
    priority: 'low',
    totalAmount: 150.00,
    paidAmount: 150.00,
    dueDate: '2024-01-20',
    orderDate: '2024-01-10',
    items: [
      { name: 'Trouser Hemming', quantity: 3, status: 'completed' },
    ],
  },
  {
    id: '4',
    orderNumber: 'ORD-2024-004',
    customerName: 'Emily Davis',
    customerPhone: '+****************',
    status: 'cancelled',
    priority: 'medium',
    totalAmount: 320.00,
    paidAmount: 0.00,
    dueDate: '2024-02-05',
    orderDate: '2024-01-22',
    items: [
      { name: 'Wedding Dress Alterations', quantity: 1, status: 'cancelled' },
    ],
  },
];

export default function OrdersSimple() {
  const router = useRouter();
  const { theme } = useTheme();
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('orders');

  console.log('Orders screen rendering...');

  const filterOptions = [
    { id: 'all', label: 'All' },
    { id: 'pending', label: 'Pending' },
    { id: 'in-progress', label: 'In Progress' },
    { id: 'completed', label: 'Completed' },
    { id: 'cancelled', label: 'Cancelled' },
  ];

  useEffect(() => {
    loadOrders();
  }, []);

  useEffect(() => {
    filterOrders();
  }, [searchQuery, selectedFilter, orders]);

  const loadOrders = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setOrders(mockOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
      Alert.alert('Error', 'Failed to load orders');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setOrders(mockOrders);
    } catch (error) {
      console.error('Error refreshing orders:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const filterOrders = () => {
    let filtered = orders;

    // Filter by status
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(order => order.status === selectedFilter);
    }

    // Filter by search query
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(query) ||
        order.customerName.toLowerCase().includes(query) ||
        order.customerPhone.includes(query)
      );
    }

    setFilteredOrders(filtered);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return '#4caf50';
      case 'in-progress': return '#ff9800';
      case 'pending': return '#2196f3';
      default: return '#666666';
    }
  };

  const handleOrderPress = (order) => {
    console.log('Order pressed:', order.id);
    Alert.alert('Order Details', `Order: ${order.orderNumber}\nCustomer: ${order.customerName}\nStatus: ${order.status}`);
  };

  const getPaymentStatus = (order) => {
    const { totalAmount, paidAmount } = order;
    if (paidAmount === 0) return 'Unpaid';
    if (paidAmount >= totalAmount) return 'Paid';
    return 'Partial';
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'Paid': return theme.colors.success;
      case 'Partial': return theme.colors.warning;
      case 'Unpaid': return theme.colors.error;
      default: return theme.colors.onSurfaceVariant;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return theme.colors.error;
      case 'medium': return theme.colors.warning;
      case 'low': return theme.colors.success;
      default: return theme.colors.onSurfaceVariant;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const isOverdue = (dueDate, status) => {
    return new Date(dueDate) < new Date() && status !== 'completed' && status !== 'cancelled';
  };

  const renderOrderItem = ({ item }) => {
    const paymentStatus = getPaymentStatus(item);
    const overdue = isOverdue(item.dueDate, item.status);

    return (
      <View style={[
        styles.orderCard,
        {
          backgroundColor: theme.colors.surface,
          borderColor: overdue ? theme.colors.error : theme.colors.outlineVariant,
          ...theme.shadows.small,
        }
      ]}>
        <TouchableOpacity
          style={styles.orderContent}
          onPress={() => handleOrderPress(item)}
          activeOpacity={0.7}
        >
          {/* Header Section */}
          <View style={styles.orderHeader}>
            <View style={styles.orderInfo}>
              <Text style={[
                styles.orderNumber,
                {
                  color: theme.colors.onSurface,
                  ...theme.typography.titleMedium,
                }
              ]}>
                {item.orderNumber}
              </Text>

              <View style={styles.priorityRow}>
                <View style={[
                  styles.priorityIndicator,
                  { backgroundColor: getPriorityColor(item.priority) }
                ]} />
                <Text style={[
                  styles.priorityText,
                  {
                    color: theme.colors.onSurfaceVariant,
                    ...theme.typography.bodySmall,
                  }
                ]}>
                  {item.priority.toUpperCase()} PRIORITY
                </Text>
              </View>
            </View>

            <View style={[
              styles.statusBadge,
              {
                backgroundColor: getStatusColor(item.status) + '15',
                borderColor: getStatusColor(item.status) + '40',
              }
            ]}>
              <Text style={[
                styles.statusText,
                {
                  color: getStatusColor(item.status),
                  ...theme.typography.labelSmall,
                }
              ]}>
                {item.status.replace('-', ' ').toUpperCase()}
              </Text>
            </View>
          </View>

          {/* Customer Info */}
          <View style={styles.customerSection}>
            <View style={styles.customerInfo}>
              <Ionicons
                name="person-outline"
                size={16}
                color={theme.colors.onSurfaceVariant}
              />
              <Text style={[
                styles.customerName,
                {
                  color: theme.colors.onSurface,
                  ...theme.typography.bodyMedium,
                }
              ]}>
                {item.customerName}
              </Text>
            </View>

            <View style={styles.itemsInfo}>
              <Ionicons
                name="list-outline"
                size={16}
                color={theme.colors.onSurfaceVariant}
              />
              <Text style={[
                styles.itemsText,
                {
                  color: theme.colors.onSurfaceVariant,
                  ...theme.typography.bodySmall,
                }
              ]}>
                {item.items.length} item{item.items.length !== 1 ? 's' : ''}
              </Text>
            </View>
          </View>

          {/* Footer Section */}
          <View style={styles.orderFooter}>
            <View style={styles.dateSection}>
              <View style={styles.dateInfo}>
                <Ionicons
                  name="calendar-outline"
                  size={16}
                  color={overdue ? theme.colors.error : theme.colors.onSurfaceVariant}
                />
                <Text style={[
                  styles.dueDate,
                  {
                    color: overdue ? theme.colors.error : theme.colors.onSurfaceVariant,
                    ...theme.typography.bodySmall,
                  }
                ]}>
                  Due: {formatDate(item.dueDate)}
                </Text>
              </View>

              {overdue && (
                <View style={[
                  styles.overdueIndicator,
                  { backgroundColor: theme.colors.error }
                ]}>
                  <Text style={[
                    styles.overdueText,
                    { color: theme.colors.onError }
                  ]}>
                    OVERDUE
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.amountSection}>
              <Text style={[
                styles.amount,
                {
                  color: theme.colors.onSurface,
                  ...theme.typography.titleSmall,
                }
              ]}>
                ${item.totalAmount.toFixed(2)}
              </Text>

              <View style={[
                styles.paymentBadge,
                { backgroundColor: getPaymentStatusColor(paymentStatus) + '15' }
              ]}>
                <Text style={[
                  styles.paymentText,
                  {
                    color: getPaymentStatusColor(paymentStatus),
                    ...theme.typography.labelSmall,
                  }
                ]}>
                  {paymentStatus}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const handleTabPress = (tabId) => {
    console.log('Tab pressed:', tabId);
    setActiveTab(tabId);

    switch (tabId) {
      case 'dashboard':
        router.push('/dashboard-native');
        break;
      case 'customers':
        router.push('/customers-native');
        break;
      case 'inventory':
        console.log('Navigate to Inventory');
        break;
      case 'more':
        console.log('Navigate to More');
        break;
      default:
        break;
    }
  };

  const handleAddOrder = () => {
    console.log('Add order pressed');
    Alert.alert('Add Order', 'Navigate to add order screen');
  };

  const handleSearch = (query) => {
    console.log('Search orders:', query);
    setSearchQuery(query);
  };

  const handleFilterPress = () => {
    console.log('Filter orders');
    Alert.alert('Filters', 'Order filter options');
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[
          styles.loadingText,
          {
            color: theme.colors.onSurface,
            ...theme.typography.bodyMedium,
          }
        ]}>
          Loading orders...
        </Text>
      </View>
    );
  }

  return (
    <MainLayout
      title="Orders"
      subtitle={`${filteredOrders.length} orders`}
      activeTab={activeTab}
      onTabPress={handleTabPress}
      showSearchBar={true}
      showFAB={true}
      topBarProps={{
        showBackButton: true,
        onBackPress: () => router.back(),
        rightIcon: 'ellipsis-vertical',
        onRightPress: () => console.log('More options'),
      }}
      searchBarProps={{
        placeholder: "Search orders...",
        value: searchQuery,
        onChangeText: handleSearch,
        showFilter: true,
        onFilterPress: handleFilterPress,
      }}
      fabProps={{
        icon: 'add',
        onPress: handleAddOrder,
        variant: 'primary',
      }}
    >
      <View style={styles.container}>
        {/* Filter Chips */}
        <FilterChips
          filters={filterOptions}
          selectedFilter={selectedFilter}
          onFilterSelect={setSelectedFilter}
        />

        {/* Order List */}
        <FlatList
          data={filteredOrders}
          renderItem={renderOrderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      </View>
    </MainLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 100, // Space for FAB
    paddingTop: 8,
  },
  orderCard: {
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
  },
  orderContent: {
    padding: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderNumber: {
    marginBottom: 4,
  },
  priorityRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  priorityText: {
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  statusText: {
    fontWeight: '500',
  },
  customerSection: {
    marginBottom: 12,
  },
  customerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  customerName: {
    marginLeft: 8,
  },
  itemsInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemsText: {
    marginLeft: 8,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  dateSection: {
    flex: 1,
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  dueDate: {
    marginLeft: 8,
  },
  overdueIndicator: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  overdueText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  amountSection: {
    alignItems: 'flex-end',
  },
  amount: {
    marginBottom: 4,
  },
  paymentBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  paymentText: {
    fontWeight: '500',
  },
});
