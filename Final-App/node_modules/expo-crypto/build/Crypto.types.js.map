{"version": 3, "file": "Crypto.types.js", "sourceRoot": "", "sources": ["../src/Crypto.types.ts"], "names": [], "mappings": "AAAA,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,qBAiCX;AAjCD,WAAY,qBAAqB;IAC/B;;OAEG;IACH,uCAAc,CAAA;IACd;;OAEG;IACH,2CAAkB,CAAA;IAClB;;OAEG;IACH,2CAAkB,CAAA;IAClB;;OAEG;IACH,2CAAkB,CAAA;IAClB;;;OAGG;IACH,oCAAW,CAAA;IACX;;;OAGG;IACH,oCAAW,CAAA;IACX;;;;OAIG;IACH,oCAAW,CAAA;AACb,CAAC,EAjCW,qBAAqB,KAArB,qBAAqB,QAiChC;AAED,cAAc;AACd,MAAM,CAAN,IAAY,cAMX;AAND,WAAY,cAAc;IACxB,6BAAW,CAAA;IACX;;OAEG;IACH,mCAAiB,CAAA;AACnB,CAAC,EANW,cAAc,KAAd,cAAc,QAMzB", "sourcesContent": ["// @needsAudit\n/**\n * [`Cryptographic hash function`](https://developer.mozilla.org/en-US/docs/Glossary/Cryptographic_hash_function)\n */\nexport enum CryptoDigestAlgorithm {\n  /**\n   * `160` bits.\n   */\n  SHA1 = 'SHA-1',\n  /**\n   * `256` bits. Collision Resistant.\n   */\n  SHA256 = 'SHA-256',\n  /**\n   * `384` bits. Collision Resistant.\n   */\n  SHA384 = 'SHA-384',\n  /**\n   * `512` bits. Collision Resistant.\n   */\n  SHA512 = 'SHA-512',\n  /**\n   * `128` bits.\n   * @platform ios\n   */\n  MD2 = 'MD2',\n  /**\n   * `128` bits.\n   * @platform ios\n   */\n  MD4 = 'MD4',\n  /**\n   * `128` bits.\n   * @platform android\n   * @platform ios\n   */\n  MD5 = 'MD5',\n}\n\n// @needsAudit\nexport enum CryptoEncoding {\n  HEX = 'hex',\n  /**\n   * Has trailing padding. Does not wrap lines. Does not have a trailing newline.\n   */\n  BASE64 = 'base64',\n}\n\n// @needsAudit\nexport type CryptoDigestOptions = {\n  /**\n   * Format the digest is returned in.\n   */\n  encoding: CryptoEncoding;\n};\n\n// @docsMissing\nexport type Digest = string;\n"]}