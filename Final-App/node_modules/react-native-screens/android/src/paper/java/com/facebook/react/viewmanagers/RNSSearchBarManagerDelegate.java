/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ColorPropConverter;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class RNSSearchBarManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNSSearchBarManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNSSearchBarManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "hideWhenScrolling":
        mViewManager.setHideWhenScrolling(view, value == null ? false : (boolean) value);
        break;
      case "autoCapitalize":
        mViewManager.setAutoCapitalize(view, (String) value);
        break;
      case "placeholder":
        mViewManager.setPlaceholder(view, value == null ? null : (String) value);
        break;
      case "placement":
        mViewManager.setPlacement(view, (String) value);
        break;
      case "obscureBackground":
        mViewManager.setObscureBackground(view, value == null ? false : (boolean) value);
        break;
      case "hideNavigationBar":
        mViewManager.setHideNavigationBar(view, value == null ? false : (boolean) value);
        break;
      case "cancelButtonText":
        mViewManager.setCancelButtonText(view, value == null ? null : (String) value);
        break;
      case "barTintColor":
        mViewManager.setBarTintColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tintColor":
        mViewManager.setTintColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "textColor":
        mViewManager.setTextColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "disableBackButtonOverride":
        mViewManager.setDisableBackButtonOverride(view, value == null ? false : (boolean) value);
        break;
      case "inputType":
        mViewManager.setInputType(view, value == null ? null : (String) value);
        break;
      case "hintTextColor":
        mViewManager.setHintTextColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "headerIconColor":
        mViewManager.setHeaderIconColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "shouldShowHintSearchIcon":
        mViewManager.setShouldShowHintSearchIcon(view, value == null ? true : (boolean) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }

  @Override
  public void receiveCommand(T view, String commandName, @Nullable ReadableArray args) {
    switch (commandName) {
      case "blur":
        mViewManager.blur(view);
        break;
      case "focus":
        mViewManager.focus(view);
        break;
      case "clearText":
        mViewManager.clearText(view);
        break;
      case "toggleCancelButton":
        mViewManager.toggleCancelButton(view, args.getBoolean(0));
        break;
      case "setText":
        mViewManager.setText(view, args.getString(0));
        break;
      case "cancelSearch":
        mViewManager.cancelSearch(view);
        break;
    }
  }
}
