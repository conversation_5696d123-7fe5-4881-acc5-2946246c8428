{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ScreenStackHeaderSubview", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderConfig", "ScreenStackHeaderCenterView", "ScreenStackHeaderBackButtonImage", "_react", "_interopRequireDefault", "require", "_reactNative", "_ScreenStackHeaderConfigNativeComponent", "_ScreenStackHeaderSubviewNativeComponent", "e", "__esModule", "default", "_extends", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ScreenStackHeaderSubviewNativeComponent", "React", "forwardRef", "props", "ref", "createElement", "style", "styles", "headerConfig", "pointerEvents", "displayName", "type", "headerSubview", "Image", "resizeMode", "fadeDuration", "rest", "headerSubviewCenter", "StyleSheet", "create", "flexDirection", "alignItems", "justifyContent", "flexShrink", "position", "width", "Platform", "OS", "undefined"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,wBAAA,GAAAF,OAAA,CAAAG,8BAAA,GAAAH,OAAA,CAAAI,0BAAA,GAAAJ,OAAA,CAAAK,yBAAA,GAAAL,OAAA,CAAAM,uBAAA,GAAAN,OAAA,CAAAO,2BAAA,GAAAP,OAAA,CAAAQ,gCAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,uCAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,wCAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAwG,SAAAD,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,SAAA,WAAAA,QAAA,GAAApB,MAAA,CAAAqB,MAAA,GAAArB,MAAA,CAAAqB,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAO,SAAA,CAAAC,MAAA,EAAAR,CAAA,UAAAS,CAAA,GAAAF,SAAA,CAAAP,CAAA,YAAAU,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAH,QAAA,CAAAU,KAAA,OAAAN,SAAA,KAFxG;AAIO,MAAMpB,wBAEZ,GAAAF,OAAA,CAAAE,wBAAA,GAAG2B,gDAA8C;AAE3C,MAAMvB,uBAAuB,GAAAN,OAAA,CAAAM,uBAAA,gBAAGwB,cAAK,CAACC,UAAU,CAGrD,CAACC,KAAK,EAAEC,GAAG,kBACXxB,MAAA,CAAAQ,OAAA,CAAAiB,aAAA,CAACrB,uCAAA,CAAAI,OAAsC,EAAAC,QAAA,KACjCc,KAAK;EACTC,GAAG,EAAEA,GAAI;EACTE,KAAK,EAAEC,MAAM,CAACC,YAAa;EAC3BC,aAAa,EAAC;AAAU,EACzB,CACF,CAAC;AAEFhC,uBAAuB,CAACiC,WAAW,GAAG,yBAAyB;AAExD,MAAM/B,gCAAgC,GAC3CwB,KAAiB,iBAEjBvB,MAAA,CAAAQ,OAAA,CAAAiB,aAAA,CAAChC,wBAAwB;EAACsC,IAAI,EAAC,MAAM;EAACL,KAAK,EAAEC,MAAM,CAACK;AAAc,gBAChEhC,MAAA,CAAAQ,OAAA,CAAAiB,aAAA,CAACtB,YAAA,CAAA8B,KAAK,EAAAxB,QAAA;EAACyB,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKZ,KAAK,CAAG,CAChC,CAC3B;AAAChC,OAAA,CAAAQ,gCAAA,GAAAA,gCAAA;AAEK,MAAMJ,0BAA0B,GACrC4B,KAAyC,IACzB;EAChB,MAAM;IAAEG,KAAK;IAAE,GAAGU;EAAK,CAAC,GAAGb,KAAK;EAEhC,oBACEvB,MAAA,CAAAQ,OAAA,CAAAiB,aAAA,CAAChC,wBAAwB,EAAAgB,QAAA,KACnB2B,IAAI;IACRL,IAAI,EAAC,OAAO;IACZL,KAAK,EAAE,CAACC,MAAM,CAACK,aAAa,EAAEN,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAACnC,OAAA,CAAAI,0BAAA,GAAAA,0BAAA;AAEK,MAAMC,yBAAyB,GACpC2B,KAAyC,IACzB;EAChB,MAAM;IAAEG,KAAK;IAAE,GAAGU;EAAK,CAAC,GAAGb,KAAK;EAEhC,oBACEvB,MAAA,CAAAQ,OAAA,CAAAiB,aAAA,CAAChC,wBAAwB,EAAAgB,QAAA,KACnB2B,IAAI;IACRL,IAAI,EAAC,MAAM;IACXL,KAAK,EAAE,CAACC,MAAM,CAACK,aAAa,EAAEN,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAACnC,OAAA,CAAAK,yBAAA,GAAAA,yBAAA;AAEK,MAAME,2BAA2B,GACtCyB,KAAyC,IACzB;EAChB,MAAM;IAAEG,KAAK;IAAE,GAAGU;EAAK,CAAC,GAAGb,KAAK;EAEhC,oBACEvB,MAAA,CAAAQ,OAAA,CAAAiB,aAAA,CAAChC,wBAAwB,EAAAgB,QAAA,KACnB2B,IAAI;IACRL,IAAI,EAAC,QAAQ;IACbL,KAAK,EAAE,CAACC,MAAM,CAACU,mBAAmB,EAAEX,KAAK;EAAE,EAC5C,CAAC;AAEN,CAAC;AAACnC,OAAA,CAAAO,2BAAA,GAAAA,2BAAA;AAEK,MAAMJ,8BAA8B,GACzC6B,KAA8C,iBAE9CvB,MAAA,CAAAQ,OAAA,CAAAiB,aAAA,CAAChC,wBAAwB,EAAAgB,QAAA,KACnBc,KAAK;EACTQ,IAAI,EAAC,WAAW;EAChBL,KAAK,EAAEC,MAAM,CAACK;AAAc,EAC7B,CACF;AAACzC,OAAA,CAAAG,8BAAA,GAAAA,8BAAA;AAEF,MAAMiC,MAAM,GAAGW,uBAAU,CAACC,MAAM,CAAC;EAC/BP,aAAa,EAAE;IACbQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDL,mBAAmB,EAAE;IACnBG,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDf,YAAY,EAAE;IACZgB,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,MAAM;IACbL,aAAa,EAAE,KAAK;IACpBE,cAAc,EAAE,eAAe;IAC/B;IACA;IACAD,UAAU,EAAEK,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,QAAQ,GAAGC;EACjD;AACF,CAAC,CAAC", "ignoreList": []}