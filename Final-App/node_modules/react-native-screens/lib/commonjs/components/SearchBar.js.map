{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_react", "_interopRequireDefault", "require", "_utils", "_reactNative", "_SearchBarNativeComponent", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "NativeSearchBar", "SearchBarNativeComponent", "NativeSearchBarCommands", "SearchBarNativeCommands", "SearchBar", "props", "ref", "searchBarRef", "React", "useRef", "useImperativeHandle", "blur", "_callMethodWithRef", "focus", "toggleCancelButton", "flag", "clearText", "setText", "text", "cancelSearch", "useCallback", "method", "current", "console", "warn", "isSearchBarAvailableForCurrentPlatform", "View", "createElement", "onSearchFocus", "onFocus", "onSearchBlur", "onBlur", "onSearchButtonPress", "onCancelButtonPress", "onChangeText", "_default", "forwardRef"], "sourceRoot": "../../../src", "sources": ["components/SearchBar.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAGA,IAAAG,yBAAA,GAAAC,uBAAA,CAAAJ,OAAA;AAM4C,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAT,OAAA,EAAAS,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAE,GAAA,CAAAL,CAAA,UAAAG,CAAA,CAAAG,GAAA,CAAAN,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAtB,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAuB,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAW,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAAtB,MAAA,CAAAuB,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAR,GAAA,IAAAQ,CAAA,CAAAC,GAAA,IAAA5B,MAAA,CAAAC,cAAA,CAAAmB,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAhB,OAAA,GAAAS,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAY,GAAA,CAAAf,CAAA,EAAAO,CAAA,GAAAA,CAAA;AAAA,SAAAd,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAT,OAAA,EAAAS,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAA7B,MAAA,CAAA8B,MAAA,GAAA9B,MAAA,CAAA8B,MAAA,CAAAC,IAAA,eAAAX,CAAA,aAAAP,CAAA,MAAAA,CAAA,GAAAmB,SAAA,CAAAC,MAAA,EAAApB,CAAA,UAAAG,CAAA,GAAAgB,SAAA,CAAAnB,CAAA,YAAAE,CAAA,IAAAC,CAAA,OAAAS,cAAA,CAAAC,IAAA,CAAAV,CAAA,EAAAD,CAAA,MAAAK,CAAA,CAAAL,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAK,CAAA,KAAAS,QAAA,CAAAK,KAAA,OAAAF,SAAA,KAP5C;AAUA,MAAMG,eAG0B,GAC9BC,iCACuB;AACzB,MAAMC,uBAA8C,GAClDC,kCAAgD;AAalD,SAASC,SAASA,CAACC,KAAqB,EAAEC,GAAiC,EAAE;EAC3E,MAAMC,YAAY,GAAGC,cAAK,CAACC,MAAM,CAA2B,IAAI,CAAC;EAEjED,cAAK,CAACE,mBAAmB,CAACJ,GAAG,EAAE,OAAO;IACpCK,IAAI,EAAEA,CAAA,KAAM;MACVC,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACS,IAAI,CAACL,GAAG,CAAC,CAAC;IAC9D,CAAC;IACDO,KAAK,EAAEA,CAAA,KAAM;MACXD,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACW,KAAK,CAACP,GAAG,CAAC,CAAC;IAC/D,CAAC;IACDQ,kBAAkB,EAAGC,IAAa,IAAK;MACrCH,kBAAkB,CAACN,GAAG,IACpBJ,uBAAuB,CAACY,kBAAkB,CAACR,GAAG,EAAES,IAAI,CACtD,CAAC;IACH,CAAC;IACDC,SAAS,EAAEA,CAAA,KAAM;MACfJ,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACc,SAAS,CAACV,GAAG,CAAC,CAAC;IACnE,CAAC;IACDW,OAAO,EAAGC,IAAY,IAAK;MACzBN,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACe,OAAO,CAACX,GAAG,EAAEY,IAAI,CAAC,CAAC;IACvE,CAAC;IACDC,YAAY,EAAEA,CAAA,KAAM;MAClBP,kBAAkB,CAACN,GAAG,IAAIJ,uBAAuB,CAACiB,YAAY,CAACb,GAAG,CAAC,CAAC;IACtE;EACF,CAAC,CAAC,CAAC;EAEH,MAAMM,kBAAkB,GAAGJ,cAAK,CAACY,WAAW,CACzCC,MAAwC,IAAK;IAC5C,MAAMf,GAAG,GAAGC,YAAY,CAACe,OAAO;IAChC,IAAIhB,GAAG,EAAE;MACPe,MAAM,CAACf,GAAG,CAAC;IACb,CAAC,MAAM;MACLiB,OAAO,CAACC,IAAI,CACV,mEACF,CAAC;IACH;EACF,CAAC,EACD,CAACjB,YAAY,CACf,CAAC;EAED,IAAI,CAACkB,6CAAsC,EAAE;IAC3CF,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;IACD,OAAOE,iBAAI;EACb;EAEA,oBACExD,MAAA,CAAAD,OAAA,CAAA0D,aAAA,CAAC3B,eAAe,EAAAN,QAAA;IACdY,GAAG,EAAEC;EAAa,GACdF,KAAK;IACTuB,aAAa,EAAEvB,KAAK,CAACwB,OAA8C;IACnEC,YAAY,EAAEzB,KAAK,CAAC0B,MAA6C;IACjEC,mBAAmB,EACjB3B,KAAK,CAAC2B,mBACP;IACDC,mBAAmB,EACjB5B,KAAK,CAAC4B,mBACP;IACDC,YAAY,EAAE7B,KAAK,CAAC6B;EAAoD,EACzE,CAAC;AAEN;AAAC,IAAAC,QAAA,GAAApE,OAAA,CAAAE,OAAA,gBAEcuC,cAAK,CAAC4B,UAAU,CAAoChC,SAAS,CAAC", "ignoreList": []}