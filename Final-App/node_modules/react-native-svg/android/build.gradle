buildscript {
    // The Android Gradle plugin is only required when opening the android folder stand-alone.
    // This avoids unnecessary downloads and potential conflicts when the library is included as a
    // module dependency in an application project.
    if (project == rootProject) {
        repositories {
            mavenCentral()
            google()
        }

        dependencies {
            classpath("com.android.tools.build:gradle:7.4.2")
            classpath "com.diffplug.spotless:spotless-plugin-gradle:6.17.0"
        }
    }
}

def isNewArchitectureEnabled() {
    // To opt-in for the New Architecture, you can either:
    // - Set `newArchEnabled` to true inside the `gradle.properties` file
    // - Invoke gradle with `-newArchEnabled=true`
    // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

if (isNewArchitectureEnabled()) {
    apply plugin: "com.facebook.react"
}

if (project == rootProject) {
    apply from: 'spotless.gradle'
}

apply plugin: 'com.android.library'

def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

def resolveReactNativeDirectory() {
  def reactNativeLocation = safeExtGet("REACT_NATIVE_NODE_MODULES_DIR", null)
  if (reactNativeLocation != null) {
    return file(reactNativeLocation)
  }

  // Fallback to node resolver for custom directory structures like monorepos.
  def reactNativePackage = file(["node", "--print", "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim())
  if (reactNativePackage.exists()) {
    return reactNativePackage.parentFile
  }

  throw new GradleException("[react-native-svg] Unable to resolve react-native location in node_modules. Your app should define `REACT_NATIVE_NODE_MODULES_DIR` extension property in `app/build.gradle` with a path to react-native in node_modules.")
}

def reactNativeRootDir = resolveReactNativeDirectory()
def reactNativeProperties = new Properties()
file("$reactNativeRootDir/ReactAndroid/gradle.properties").withInputStream { reactNativeProperties.load(it) }

def REACT_NATIVE_VERSION = reactNativeProperties.getProperty("VERSION_NAME")
def REACT_NATIVE_MINOR_VERSION = REACT_NATIVE_VERSION.startsWith("0.0.0-") ? 1000 : REACT_NATIVE_VERSION.split("\\.")[1].toInteger()

def getFrescoVersion() {
    def reactNativeRootDir = resolveReactNativeDirectory()
    def frescoVersion = null
    file("$reactNativeRootDir/gradle/libs.versions.toml").withInputStream { stream ->
        stream.eachLine { line ->
            if (line.contains("fresco") && !line.contains("ref")) {
                def keyValue = line.split("=")
                if (keyValue.size() == 2) {
                    frescoVersion = keyValue[1].trim().replaceAll(/["']/, "")
                }
            }
        }
    }
    if (!frescoVersion) {
        return "3.2.0"
    }
    return frescoVersion
}
def FRESCO_VERSION = getFrescoVersion()

android {
    compileSdkVersion safeExtGet('compileSdkVersion', 28)
    namespace "com.horcrux.svg"
    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
    if (agpVersion.tokenize('.')[0].toInteger() >= 8) {
        buildFeatures {
            buildConfig = true
        }
    }
    // Used to override the NDK path/version on internal CI or by allowing
    // users to customize the NDK path/version from their root project (e.g. for M1 support)
    if (rootProject.hasProperty("ndkPath")) {
        ndkPath rootProject.ext.ndkPath
    }
    if (rootProject.hasProperty("ndkVersion")) {
        ndkVersion rootProject.ext.ndkVersion
    }

    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 16)
        //noinspection OldTargetApi
        targetSdkVersion safeExtGet('targetSdkVersion', 28)
        buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()

        consumerProguardFiles 'proguard-rules.pro'
    }
    lintOptions {
        abortOnError false
    }

    sourceSets.main {
        java {
            if (!isNewArchitectureEnabled()) {
                srcDirs += [
                    "src/paper/java",
                ]
            }

            if (REACT_NATIVE_MINOR_VERSION >= 75) { // Use for react-native@0.75 and above
                // borderRadius fix https://github.com/software-mansion/react-native-svg/pull/2415
                srcDirs += "src/SvgViewManager75/java"
                // processTransform replacement https://github.com/software-mansion/react-native-svg/pull/2554
                srcDirs += "src/RenderableViewManager75/java"
            } else { // Maintain compatibility with react-native@0.73 and react-native@0.74
                srcDirs += "src/SvgViewManager73/java"
                srcDirs += "src/RenderableViewManager73/java"
            }

            if (REACT_NATIVE_MINOR_VERSION >= 74) { // Use for react-native@0.74 and above
                // new API https://github.com/software-mansion/react-native-svg/pull/2541
                srcDirs += "src/SvgPackage74/java"
            } else {
                // Maintain compatibility with react-native@0.73
                srcDirs += "src/SvgPackage73/java"
            }
        }
    }
}

repositories {
    mavenCentral()
    mavenLocal()
    google()
    maven {
        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
        url "$rootDir/../node_modules/react-native/android"
    }
}

dependencies {
    implementation 'com.facebook.react:react-native:+'
    if (REACT_NATIVE_MINOR_VERSION >= 76) {
        implementation("com.facebook.fresco:fresco:${FRESCO_VERSION}") {
            exclude group: 'com.facebook.soloader'
        }
        implementation("com.facebook.fresco:imagepipeline-okhttp3:${FRESCO_VERSION}") {
            exclude group: 'com.facebook.soloader'
        }
        implementation("com.facebook.fresco:middleware:${FRESCO_VERSION}")
    }
}
