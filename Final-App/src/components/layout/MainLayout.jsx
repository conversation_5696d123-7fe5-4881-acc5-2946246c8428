import React from 'react';
import { View, StyleSheet, StatusBar } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import TopBar from '../ui/TopBar';
import BottomNavigation from '../ui/BottomNavigation';
import SearchBar from '../ui/SearchBar';
import FloatingActionButton from '../ui/FloatingActionButton';
import { useTheme } from '../../context/ThemeContext';

const MainLayout = ({
  children,
  title,
  subtitle,
  showTopBar = true,
  showBottomNav = true,
  showSearchBar = false,
  showFAB = false,
  topBarProps = {},
  bottomNavProps = {},
  searchBarProps = {},
  fabProps = {},
  activeTab,
  onTabPress,
  backgroundColor,
}) => {
  const { theme } = useTheme();
  const bgColor = backgroundColor || theme.colors.background;

  return (
    <SafeAreaProvider>
      <StatusBar
        barStyle={theme.mode === 'dark' ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.surface}
        translucent={false}
      />
      <SafeAreaView style={[styles.container, { backgroundColor: bgColor }]} edges={['top']}>
        {showTopBar && (
          <TopBar
            title={title}
            subtitle={subtitle}
            backgroundColor={theme.colors.surface}
            textColor={theme.colors.onSurface}
            {...topBarProps}
          />
        )}

        {showSearchBar && (
          <SearchBar
            placeholder="Search..."
            {...searchBarProps}
          />
        )}

        <View style={[styles.content, { backgroundColor: bgColor }]}>
          {children}
        </View>

        {showBottomNav && (
          <BottomNavigation
            activeTab={activeTab}
            onTabPress={onTabPress}
            {...bottomNavProps}
          />
        )}

        {showFAB && (
          <FloatingActionButton
            icon="add"
            bottom={showBottomNav ? 90 : 20}
            {...fabProps}
          />
        )}
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});

export default MainLayout;
