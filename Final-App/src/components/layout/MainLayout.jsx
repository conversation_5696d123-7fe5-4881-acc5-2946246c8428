import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import TopBar from '../ui/TopBar';
import BottomNavigation from '../ui/BottomNavigation';

const MainLayout = ({
  children,
  title,
  subtitle,
  showTopBar = true,
  showBottomNav = true,
  topBarProps = {},
  bottomNavProps = {},
  activeTab,
  onTabPress,
  backgroundColor = '#f5f5f5',
}) => {
  return (
    <SafeAreaProvider>
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={['top']}>
        {showTopBar && (
          <TopBar
            title={title}
            subtitle={subtitle}
            {...topBarProps}
          />
        )}
        
        <View style={styles.content}>
          {children}
        </View>
        
        {showBottomNav && (
          <BottomNavigation
            activeTab={activeTab}
            onTabPress={onTabPress}
            {...bottomNavProps}
          />
        )}
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});

export default MainLayout;
