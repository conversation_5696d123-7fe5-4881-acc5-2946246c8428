import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Platform, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../../context/ThemeContext';

const BottomNavigation = ({ activeTab, onTabPress, tabs }) => {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();

  // Material Design 3 Navigation Bar items
  const defaultTabs = [
    {
      id: 'dashboard',
      label: 'Home',
      icon: 'home-outline',
      activeIcon: 'home',
    },
    {
      id: 'customers',
      label: 'Customers',
      icon: 'people-outline',
      activeIcon: 'people',
    },
    {
      id: 'orders',
      label: 'Orders',
      icon: 'receipt-outline',
      activeIcon: 'receipt',
    },
    {
      id: 'inventory',
      label: 'Inventory',
      icon: 'cube-outline',
      activeIcon: 'cube',
    },
    {
      id: 'more',
      label: 'More',
      icon: 'menu-outline',
      activeIcon: 'menu',
    },
  ];

  const navigationTabs = tabs || defaultTabs;

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: theme.colors.surface,
        borderTopColor: theme.colors.outlineVariant,
        paddingBottom: Platform.OS === 'ios' ? insets.bottom : 8,
        ...theme.shadows.medium,
      }
    ]}>
      <View style={styles.tabContainer}>
        {navigationTabs.map((tab) => {
          const isActive = activeTab === tab.id;
          return (
            <TouchableOpacity
              key={tab.id}
              style={styles.tab}
              onPress={() => onTabPress(tab.id)}
              activeOpacity={0.8}
            >
              {/* Material Design 3 Active Indicator */}
              <View style={[
                styles.activeIndicator,
                {
                  backgroundColor: isActive ? theme.colors.secondaryContainer : 'transparent',
                }
              ]}>
                <Ionicons
                  name={isActive ? tab.activeIcon : tab.icon}
                  size={24}
                  color={isActive ? theme.colors.onSecondaryContainer : theme.colors.onSurfaceVariant}
                />
              </View>

              {/* Label */}
              <Text style={[
                styles.label,
                {
                  color: isActive ? theme.colors.onSurface : theme.colors.onSurfaceVariant,
                  ...theme.typography.labelMedium,
                }
              ]}>
                {tab.label}
              </Text>

              {/* Badge */}
              {tab.badge && (
                <View style={[
                  styles.badge,
                  { backgroundColor: theme.colors.error }
                ]}>
                  <Text style={[
                    styles.badgeText,
                    { color: theme.colors.onError }
                  ]}>
                    {tab.badge}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 1,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingTop: 12,
    paddingBottom: 4,
    paddingHorizontal: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 4,
    position: 'relative',
    minHeight: 64, // Material Design 3 minimum touch target
  },
  activeIndicator: {
    width: 64,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  label: {
    textAlign: 'center',
    fontWeight: '500',
    marginTop: 4,
  },
  badge: {
    position: 'absolute',
    top: 8,
    right: '25%',
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    lineHeight: 12,
  },
});

export default BottomNavigation;
