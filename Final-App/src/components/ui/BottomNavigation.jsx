import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const BottomNavigation = ({ activeTab, onTabPress, tabs }) => {
  const insets = useSafeAreaInsets();

  const defaultTabs = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'grid-outline',
      activeIcon: 'grid',
    },
    {
      id: 'customers',
      label: 'Customers',
      icon: 'people-outline',
      activeIcon: 'people',
    },
    {
      id: 'orders',
      label: 'Orders',
      icon: 'receipt-outline',
      activeIcon: 'receipt',
    },
    {
      id: 'inventory',
      label: 'Inventory',
      icon: 'cube-outline',
      activeIcon: 'cube',
    },
    {
      id: 'more',
      label: 'More',
      icon: 'ellipsis-horizontal-outline',
      activeIcon: 'ellipsis-horizontal',
    },
  ];

  const navigationTabs = tabs || defaultTabs;

  return (
    <View style={[
      styles.container,
      {
        paddingBottom: Platform.OS === 'ios' ? insets.bottom : 8,
      }
    ]}>
      <View style={styles.tabContainer}>
        {navigationTabs.map((tab) => {
          const isActive = activeTab === tab.id;
          return (
            <TouchableOpacity
              key={tab.id}
              style={styles.tab}
              onPress={() => onTabPress(tab.id)}
              activeOpacity={0.7}
            >
              <View style={[
                styles.iconContainer,
                isActive && styles.activeIconContainer
              ]}>
                <Ionicons
                  name={isActive ? tab.activeIcon : tab.icon}
                  size={24}
                  color={isActive ? '#6200ee' : '#666666'}
                />
              </View>
              <Text style={[
                styles.label,
                isActive && styles.activeLabel
              ]}>
                {tab.label}
              </Text>
              {tab.badge && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>{tab.badge}</Text>
                </View>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingTop: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    position: 'relative',
  },
  iconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    marginBottom: 4,
  },
  activeIconContainer: {
    backgroundColor: '#f3e5f5',
  },
  label: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
    textAlign: 'center',
  },
  activeLabel: {
    color: '#6200ee',
    fontWeight: '600',
  },
  badge: {
    position: 'absolute',
    top: 4,
    right: '30%',
    backgroundColor: '#f44336',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});

export default BottomNavigation;
