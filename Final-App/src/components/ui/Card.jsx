import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

const Card = ({
  children,
  title,
  subtitle,
  onPress,
  style,
  contentStyle,
  titleStyle,
  subtitleStyle,
  variant = 'default', // default, elevated, outlined
  padding = 'medium', // none, small, medium, large
  ...props
}) => {
  const { theme } = useTheme();
  const Component = onPress ? TouchableOpacity : View;

  const getCardStyle = () => {
    const baseStyle = {
      backgroundColor: theme.colors.surface,
    };

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyle,
          ...theme.shadows.md,
          shadowColor: theme.colors.shadow,
        };
      case 'outlined':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: theme.colors.border,
        };
      default:
        return {
          ...baseStyle,
          ...theme.shadows.sm,
          shadowColor: theme.colors.shadow,
        };
    }
  };

  const getPaddingStyle = () => {
    switch (padding) {
      case 'none':
        return { padding: 0 };
      case 'small':
        return { padding: theme.spacing.sm };
      case 'large':
        return { padding: theme.spacing.lg };
      default: // medium
        return { padding: theme.spacing.md };
    }
  };

  return (
    <Component
      style={[
        styles.card,
        getCardStyle(),
        getPaddingStyle(),
        style,
      ]}
      onPress={onPress}
      {...props}
    >
      {title && (
        <View style={styles.header}>
          <Text style={[
            styles.title,
            {
              color: theme.colors.text,
              fontSize: theme.typography.sizes.lg,
            },
            titleStyle,
          ]}>
            {title}
          </Text>
          {subtitle && (
            <Text style={[
              styles.subtitle,
              {
                color: theme.colors.textSecondary,
                fontSize: theme.typography.sizes.sm,
              },
              subtitleStyle,
            ]}>
              {subtitle}
            </Text>
          )}
        </View>
      )}

      <View style={[styles.content, contentStyle]}>
        {children}
      </View>
    </Component>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    marginBottom: 16,
  },
  header: {
    marginBottom: 12,
  },
  title: {
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontWeight: '400',
  },
  content: {
    flex: 1,
  },
  // StatCard styles
  statCard: {
    minHeight: 100,
  },
  statContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statText: {
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  statSubtitle: {
    fontSize: 12,
    marginTop: 2,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  // ListCard styles
  listCard: {
    marginBottom: 8,
  },
  listContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftIcon: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  listText: {
    flex: 1,
  },
  listTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  listSubtitle: {
    fontSize: 14,
    marginBottom: 2,
  },
  listDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  rightSection: {
    alignItems: 'flex-end',
    marginLeft: 12,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  rightIcon: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

// Specialized Card Components
const StatCard = ({
  title,
  value,
  subtitle,
  icon,
  color = '#6200ee',
  onPress,
  style,
}) => {
  const { theme } = useTheme();

  return (
    <Card onPress={onPress} style={[styles.statCard, style]} variant="elevated">
      <View style={styles.statContent}>
        <View style={styles.statText}>
          <Text style={[styles.statValue, { color }]}>{value}</Text>
          <Text style={[styles.statTitle, { color: theme.colors.text }]}>{title}</Text>
          {subtitle && (
            <Text style={[styles.statSubtitle, { color: theme.colors.textSecondary }]}>{subtitle}</Text>
          )}
        </View>
        {icon && (
          <View style={[styles.statIcon, { backgroundColor: `${color}15` }]}>
            {icon}
          </View>
        )}
      </View>
    </Card>
  );
};

const ListCard = ({
  title,
  subtitle,
  description,
  leftIcon,
  rightIcon,
  badge,
  onPress,
  style,
}) => {
  const { theme } = useTheme();

  return (
    <Card onPress={onPress} style={[styles.listCard, style]} padding="medium">
      <View style={styles.listContent}>
        {leftIcon && (
          <View style={styles.leftIcon}>
            {leftIcon}
          </View>
        )}

        <View style={styles.listText}>
          <Text style={[styles.listTitle, { color: theme.colors.text }]} numberOfLines={1}>
            {title}
          </Text>
          {subtitle && (
            <Text style={[styles.listSubtitle, { color: theme.colors.textSecondary }]} numberOfLines={1}>
              {subtitle}
            </Text>
          )}
          {description && (
            <Text style={[styles.listDescription, { color: theme.colors.textSecondary }]} numberOfLines={2}>
              {description}
            </Text>
          )}
        </View>

        <View style={styles.rightSection}>
          {badge && (
            <View style={[styles.badge, { backgroundColor: badge.color || theme.colors.primary }]}>
              <Text style={styles.badgeText}>{badge.text}</Text>
            </View>
          )}
          {rightIcon && (
            <View style={styles.rightIcon}>
              {rightIcon}
            </View>
          )}
        </View>
      </View>
    </Card>
  );
};

export { StatCard, ListCard };
export default Card;
