import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

const FilterChip = ({ label, isSelected, onPress, style }) => {
  const { theme } = useTheme();
  
  return (
    <TouchableOpacity
      style={[
        styles.chip,
        {
          backgroundColor: isSelected ? theme.colors.primary : theme.colors.surface,
          borderColor: isSelected ? theme.colors.primary : theme.colors.border,
        },
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Text
        style={[
          styles.chipText,
          {
            color: isSelected ? '#ffffff' : theme.colors.text,
            fontSize: theme.typography.sizes.sm,
          },
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const FilterChips = ({
  filters,
  selectedFilter,
  onFilterSelect,
  style,
  chipStyle,
  horizontal = true,
  showsHorizontalScrollIndicator = false,
}) => {
  const Container = horizontal ? ScrollView : View;
  const containerProps = horizontal ? {
    horizontal: true,
    showsHorizontalScrollIndicator,
    contentContainerStyle: styles.scrollContent,
  } : {};

  return (
    <Container
      style={[styles.container, style]}
      {...containerProps}
    >
      {filters.map((filter) => (
        <FilterChip
          key={filter.id || filter.value}
          label={filter.label}
          isSelected={selectedFilter === (filter.id || filter.value)}
          onPress={() => onFilterSelect(filter.id || filter.value)}
          style={chipStyle}
        />
      ))}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  scrollContent: {
    paddingRight: 16,
  },
  chip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  chipText: {
    fontWeight: '500',
    textAlign: 'center',
  },
});

export { FilterChip };
export default FilterChips;
