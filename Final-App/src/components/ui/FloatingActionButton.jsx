import React, { useRef } from 'react';
import { TouchableOpacity, StyleSheet, Animated, Platform, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

const FloatingActionButton = ({
  icon = 'add',
  onPress,
  size = 'medium', // 'small', 'medium', 'large'
  iconSize,
  backgroundColor,
  iconColor,
  style,
  bottom = 80,
  right = 16,
  disabled = false,
  variant = 'primary', // 'primary', 'secondary', 'tertiary'
  extended = false,
  label,
}) => {
  const { theme } = useTheme();
  const scaleValue = useRef(new Animated.Value(1)).current;

  // Material Design 3 FAB sizes
  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return { width: 40, height: 40, iconSize: 18 };
      case 'large':
        return { width: 96, height: 96, iconSize: 36 };
      default: // medium
        return { width: 56, height: 56, iconSize: 24 };
    }
  };

  const sizeConfig = getSizeConfig();
  const finalIconSize = iconSize || sizeConfig.iconSize;

  // Material Design 3 FAB colors
  const getColors = () => {
    switch (variant) {
      case 'secondary':
        return {
          background: backgroundColor || theme.colors.secondaryContainer,
          icon: iconColor || theme.colors.onSecondaryContainer,
        };
      case 'tertiary':
        return {
          background: backgroundColor || theme.colors.tertiaryContainer,
          icon: iconColor || theme.colors.onTertiaryContainer,
        };
      default: // primary
        return {
          background: backgroundColor || theme.colors.primaryContainer,
          icon: iconColor || theme.colors.onPrimaryContainer,
        };
    }
  };

  const colors = getColors();

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    if (!disabled && onPress) {
      onPress();
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          bottom,
          right,
          transform: [{ scale: scaleValue }],
        },
      ]}
    >
      <TouchableOpacity
        style={[
          styles.fab,
          {
            width: extended ? 'auto' : sizeConfig.width,
            height: sizeConfig.height,
            borderRadius: sizeConfig.height / 2,
            backgroundColor: disabled ? theme.colors.outline : colors.background,
            paddingHorizontal: extended ? 16 : 0,
            ...theme.shadows.lg,
            shadowColor: theme.colors.shadow,
          },
          style,
        ]}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.9}
      >
        <Ionicons
          name={icon}
          size={finalIconSize}
          color={disabled ? theme.colors.onSurfaceVariant : colors.icon}
          style={extended && label ? styles.iconWithLabel : undefined}
        />
        {extended && label && (
          <Text
            style={[
              styles.label,
              {
                color: disabled ? theme.colors.onSurfaceVariant : colors.icon,
                ...theme.typography.labelLarge,
              },
            ]}
          >
            {label}
          </Text>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  },
  fab: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 56,
  },
  iconWithLabel: {
    marginRight: 8,
  },
  label: {
    fontWeight: '500',
  },
});

export default FloatingActionButton;
