import React from 'react';
import { TouchableOpacity, StyleSheet, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

const FloatingActionButton = ({
  icon = 'add',
  onPress,
  size = 56,
  iconSize = 24,
  backgroundColor,
  iconColor = '#ffffff',
  style,
  bottom = 80,
  right = 16,
  disabled = false,
}) => {
  const { theme } = useTheme();
  const bgColor = backgroundColor || theme.colors.primary;

  return (
    <TouchableOpacity
      style={[
        styles.fab,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: disabled ? theme.colors.disabled : bgColor,
          bottom,
          right,
          shadowColor: theme.colors.shadow,
        },
        style,
      ]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <Ionicons
        name={icon}
        size={iconSize}
        color={disabled ? theme.colors.textSecondary : iconColor}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default FloatingActionButton;
