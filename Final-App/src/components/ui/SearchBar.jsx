import React, { useState, useRef, useEffect } from 'react';
import { View, TextInput, TouchableOpacity, StyleSheet, Animated, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

const SearchBar = ({
  placeholder = 'Search...',
  value,
  onChangeText,
  onFocus,
  onBlur,
  onClear,
  onSubmit,
  style,
  inputStyle,
  showFilter = false,
  onFilterPress,
  autoFocus = false,
  editable = true,
  containerStyle,
}) => {
  const { theme } = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const inputRef = useRef(null);

  const handleFocus = () => {
    setIsFocused(true);
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
    onFocus && onFocus();
  };

  const handleBlur = () => {
    setIsFocused(false);
    Animated.timing(animatedValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
    onBlur && onBlur();
  };

  const handleClear = () => {
    onChangeText && onChangeText('');
    onClear && onClear();
    inputRef.current?.focus();
  };

  const handleSubmit = () => {
    onSubmit && onSubmit(value);
    inputRef.current?.blur();
  };

  // Animated border color
  const borderColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.outline, theme.colors.primary],
  });

  // Animated background color
  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.surfaceVariant, theme.colors.surface],
  });

  return (
    <View style={[styles.container, containerStyle]}>
      <Animated.View style={[
        styles.searchContainer,
        {
          backgroundColor,
          borderColor,
          ...theme.shadows.sm,
        },
        style,
      ]}>
        <Ionicons
          name="search"
          size={20}
          color={isFocused ? theme.colors.primary : theme.colors.onSurfaceVariant}
          style={styles.searchIcon}
        />

        <TextInput
          ref={inputRef}
          style={[
            styles.input,
            {
              color: theme.colors.onSurface,
              ...theme.typography.bodyMedium,
            },
            inputStyle,
          ]}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.onSurfaceVariant}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onSubmitEditing={handleSubmit}
          autoFocus={autoFocus}
          editable={editable}
          returnKeyType="search"
          selectionColor={theme.colors.primary}
        />

        {value && value.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClear}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons
              name="close-circle"
              size={20}
              color={theme.colors.onSurfaceVariant}
            />
          </TouchableOpacity>
        )}

        {showFilter && (
          <TouchableOpacity
            style={[
              styles.filterButton,
              { borderLeftColor: theme.colors.outlineVariant }
            ]}
            onPress={onFilterPress}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons
              name="tune"
              size={20}
              color={theme.colors.onSurfaceVariant}
            />
          </TouchableOpacity>
        )}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 28, // Material Design 3 rounded corners
    borderWidth: 1,
    paddingHorizontal: 16,
    height: 56, // Material Design 3 standard height
    minHeight: 56,
  },
  searchIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    height: '100%',
    paddingVertical: 0,
    includeFontPadding: false,
    textAlignVertical: 'center',
    ...Platform.select({
      android: {
        paddingTop: 0,
        paddingBottom: 0,
      },
    }),
  },
  clearButton: {
    marginLeft: 8,
    padding: 4,
  },
  filterButton: {
    marginLeft: 8,
    paddingLeft: 12,
    borderLeftWidth: 1,
    padding: 4,
  },
});

export default SearchBar;
