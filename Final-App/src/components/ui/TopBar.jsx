import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, StatusBar, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../../context/ThemeContext';

const TopBar = ({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onLeftPress,
  onRightPress,
  backgroundColor,
  textColor,
  showBackButton = false,
  onBackPress,
  elevation = true,
}) => {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();

  // Use theme colors if not provided
  const bgColor = backgroundColor || theme.colors.surface;
  const txtColor = textColor || theme.colors.onSurface;

  return (
    <>
      <StatusBar
        barStyle={theme.mode === 'dark' ? 'light-content' : 'dark-content'}
        backgroundColor={bgColor}
        translucent={false}
      />
      <View style={[
        styles.container,
        {
          backgroundColor: bgColor,
          borderBottomColor: theme.colors.outlineVariant,
          paddingTop: Platform.OS === 'ios' ? insets.top : 0,
          elevation: elevation ? 2 : 0,
          shadowOpacity: elevation ? 0.1 : 0,
          ...theme.shadows.small,
        }
      ]}>
        <View style={styles.content}>
          {/* Left Section */}
          <View style={styles.leftSection}>
            {showBackButton ? (
              <TouchableOpacity
                style={[styles.iconButton, { backgroundColor: 'transparent' }]}
                onPress={onBackPress}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="arrow-back" size={24} color={txtColor} />
              </TouchableOpacity>
            ) : leftIcon ? (
              <TouchableOpacity
                style={[styles.iconButton, { backgroundColor: 'transparent' }]}
                onPress={onLeftPress}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name={leftIcon} size={24} color={txtColor} />
              </TouchableOpacity>
            ) : (
              <View style={styles.iconButton} />
            )}
          </View>

          {/* Center Section */}
          <View style={styles.centerSection}>
            <Text style={[
              styles.title,
              {
                color: txtColor,
                ...theme.typography.titleLarge,
              }
            ]} numberOfLines={1}>
              {title}
            </Text>
            {subtitle && (
              <Text style={[
                styles.subtitle,
                {
                  color: theme.colors.onSurfaceVariant,
                  ...theme.typography.bodySmall,
                }
              ]} numberOfLines={1}>
                {subtitle}
              </Text>
            )}
          </View>

          {/* Right Section */}
          <View style={styles.rightSection}>
            {rightIcon ? (
              <TouchableOpacity
                style={[styles.iconButton, { backgroundColor: 'transparent' }]}
                onPress={onRightPress}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name={rightIcon} size={24} color={txtColor} />
              </TouchableOpacity>
            ) : (
              <View style={styles.iconButton} />
            )}
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 2,
    zIndex: 1000,
    borderBottomWidth: 1,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 4,
    paddingVertical: 8,
    minHeight: 64, // Material Design 3 standard app bar height
  },
  leftSection: {
    width: 48,
    alignItems: 'flex-start',
  },
  centerSection: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  rightSection: {
    width: 48,
    alignItems: 'flex-end',
  },
  iconButton: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 24,
  },
  title: {
    textAlign: 'center',
    fontWeight: '400', // Material Design 3 uses regular weight for titles
  },
  subtitle: {
    textAlign: 'center',
    marginTop: 2,
  },
});

export default TopBar;
