// Latest Material Design 3 Theme Constants for Tailor Management App
export const COLORS = {
  // Material Design 3 Primary Palette
  primary: '#6750A4',
  onPrimary: '#FFFFFF',
  primaryContainer: '#EADDFF',
  onPrimaryContainer: '#21005D',

  // Secondary Palette
  secondary: '#625B71',
  onSecondary: '#FFFFFF',
  secondaryContainer: '#E8DEF8',
  onSecondaryContainer: '#1D192B',

  // Tertiary Palette
  tertiary: '#7D5260',
  onTertiary: '#FFFFFF',
  tertiaryContainer: '#FFD8E4',
  onTertiaryContainer: '#31111D',

  // Error Palette
  error: '#BA1A1A',
  onError: '#FFFFFF',
  errorContainer: '#FFDAD6',
  onErrorContainer: '#410002',

  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  info: '#2196F3',

  // Light theme colors
  light: {
    background: '#FFFBFE',
    onBackground: '#1C1B1F',
    surface: '#FFFBFE',
    onSurface: '#1C1B1F',
    surfaceVariant: '#E7E0EC',
    onSurfaceVariant: '#49454F',
    outline: '#79747E',
    outlineVariant: '#CAC4D0',
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#313033',
    inverseOnSurface: '#F4EFF4',
    inversePrimary: '#D0BCFF',
    // Legacy support
    text: '#1C1B1F',
    textSecondary: '#49454F',
    border: '#CAC4D0',
    disabled: '#79747E',
  },

  // Dark theme colors
  dark: {
    background: '#10131C',
    onBackground: '#E6E1E5',
    surface: '#10131C',
    onSurface: '#E6E1E5',
    surfaceVariant: '#49454F',
    onSurfaceVariant: '#CAC4D0',
    outline: '#938F99',
    outlineVariant: '#49454F',
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#E6E1E5',
    inverseOnSurface: '#313033',
    inversePrimary: '#6750A4',
    // Legacy support
    text: '#E6E1E5',
    textSecondary: '#CAC4D0',
    border: '#49454F',
    disabled: '#938F99',
  },
};

export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const TYPOGRAPHY = {
  // Material Design 3 Typography Scale
  displayLarge: { fontSize: 57, lineHeight: 64, fontWeight: '400' },
  displayMedium: { fontSize: 45, lineHeight: 52, fontWeight: '400' },
  displaySmall: { fontSize: 36, lineHeight: 44, fontWeight: '400' },

  headlineLarge: { fontSize: 32, lineHeight: 40, fontWeight: '400' },
  headlineMedium: { fontSize: 28, lineHeight: 36, fontWeight: '400' },
  headlineSmall: { fontSize: 24, lineHeight: 32, fontWeight: '400' },

  titleLarge: { fontSize: 22, lineHeight: 28, fontWeight: '500' },
  titleMedium: { fontSize: 16, lineHeight: 24, fontWeight: '500' },
  titleSmall: { fontSize: 14, lineHeight: 20, fontWeight: '500' },

  labelLarge: { fontSize: 14, lineHeight: 20, fontWeight: '500' },
  labelMedium: { fontSize: 12, lineHeight: 16, fontWeight: '500' },
  labelSmall: { fontSize: 11, lineHeight: 16, fontWeight: '500' },

  bodyLarge: { fontSize: 16, lineHeight: 24, fontWeight: '400' },
  bodyMedium: { fontSize: 14, lineHeight: 20, fontWeight: '400' },
  bodySmall: { fontSize: 12, lineHeight: 16, fontWeight: '400' },

  // Legacy sizes for backward compatibility
  sizes: {
    xs: 11,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    xxl: 22,
  },
  weights: {
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
};

export const BORDER_RADIUS = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  round: 50,
};

export const SHADOWS = {
  sm: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};

// Create Material Design 3 theme objects
export const lightTheme = {
  colors: {
    // Material Design 3 Primary colors
    primary: COLORS.primary,
    onPrimary: COLORS.onPrimary,
    primaryContainer: COLORS.primaryContainer,
    onPrimaryContainer: COLORS.onPrimaryContainer,

    // Secondary colors
    secondary: COLORS.secondary,
    onSecondary: COLORS.onSecondary,
    secondaryContainer: COLORS.secondaryContainer,
    onSecondaryContainer: COLORS.onSecondaryContainer,

    // Tertiary colors
    tertiary: COLORS.tertiary,
    onTertiary: COLORS.onTertiary,
    tertiaryContainer: COLORS.tertiaryContainer,
    onTertiaryContainer: COLORS.onTertiaryContainer,

    // Error colors
    error: COLORS.error,
    onError: COLORS.onError,
    errorContainer: COLORS.errorContainer,
    onErrorContainer: COLORS.onErrorContainer,

    // Status colors
    success: COLORS.success,
    warning: COLORS.warning,
    info: COLORS.info,

    // Light theme specific colors
    ...COLORS.light,
  },
  spacing: SPACING,
  typography: TYPOGRAPHY,
  borderRadius: BORDER_RADIUS,
  shadows: SHADOWS,
  mode: 'light',
};

export const darkTheme = {
  colors: {
    // Material Design 3 Primary colors (dark variants)
    primary: COLORS.primary,
    onPrimary: COLORS.onPrimary,
    primaryContainer: COLORS.primaryContainer,
    onPrimaryContainer: COLORS.onPrimaryContainer,

    // Secondary colors
    secondary: COLORS.secondary,
    onSecondary: COLORS.onSecondary,
    secondaryContainer: COLORS.secondaryContainer,
    onSecondaryContainer: COLORS.onSecondaryContainer,

    // Tertiary colors
    tertiary: COLORS.tertiary,
    onTertiary: COLORS.onTertiary,
    tertiaryContainer: COLORS.tertiaryContainer,
    onTertiaryContainer: COLORS.onTertiaryContainer,

    // Error colors
    error: COLORS.error,
    onError: COLORS.onError,
    errorContainer: COLORS.errorContainer,
    onErrorContainer: COLORS.onErrorContainer,

    // Status colors
    success: COLORS.success,
    warning: COLORS.warning,
    info: COLORS.info,

    // Dark theme specific colors
    ...COLORS.dark,
  },
  spacing: SPACING,
  typography: TYPOGRAPHY,
  borderRadius: BORDER_RADIUS,
  shadows: SHADOWS,
  mode: 'dark',
};
