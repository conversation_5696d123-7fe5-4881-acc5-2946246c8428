import React, { createContext, useContext, useState, useEffect } from 'react';
import NetInfo from '@react-native-community/netinfo';

const NetworkContext = createContext();

export const useNetwork = () => {
  const context = useContext(NetworkContext);
  if (!context) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
};

export const NetworkProvider = ({ children }) => {
  const [isConnected, setIsConnected] = useState(true);
  const [connectionType, setConnectionType] = useState('unknown');
  const [isInternetReachable, setIsInternetReachable] = useState(true);
  const [connectionDetails, setConnectionDetails] = useState(null);

  useEffect(() => {
    // Subscribe to network state updates
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      setConnectionType(state.type);
      setIsInternetReachable(state.isInternetReachable);
      setConnectionDetails(state.details);
      
      console.log('Network state changed:', {
        isConnected: state.isConnected,
        type: state.type,
        isInternetReachable: state.isInternetReachable,
        details: state.details
      });
    });

    // Get initial network state
    NetInfo.fetch().then(state => {
      setIsConnected(state.isConnected);
      setConnectionType(state.type);
      setIsInternetReachable(state.isInternetReachable);
      setConnectionDetails(state.details);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // Check if connection is strong enough for data operations
  const isConnectionStrong = () => {
    if (!isConnected || !isInternetReachable) {
      return false;
    }

    // For cellular connections, check signal strength
    if (connectionType === 'cellular' && connectionDetails) {
      const { cellularGeneration } = connectionDetails;
      // Consider 3G and above as strong enough
      return cellularGeneration === '4g' || cellularGeneration === '5g' || cellularGeneration === '3g';
    }

    // WiFi is generally considered strong
    if (connectionType === 'wifi') {
      return true;
    }

    // For other types, assume connected means strong enough
    return true;
  };

  // Get connection quality description
  const getConnectionQuality = () => {
    if (!isConnected) {
      return 'No Connection';
    }

    if (!isInternetReachable) {
      return 'No Internet';
    }

    if (connectionType === 'wifi') {
      return 'WiFi';
    }

    if (connectionType === 'cellular' && connectionDetails) {
      const { cellularGeneration } = connectionDetails;
      switch (cellularGeneration) {
        case '5g':
          return '5G';
        case '4g':
          return '4G';
        case '3g':
          return '3G';
        case '2g':
          return '2G';
        default:
          return 'Cellular';
      }
    }

    return connectionType || 'Unknown';
  };

  // Refresh network state
  const refreshNetworkState = async () => {
    try {
      const state = await NetInfo.fetch();
      setIsConnected(state.isConnected);
      setConnectionType(state.type);
      setIsInternetReachable(state.isInternetReachable);
      setConnectionDetails(state.details);
      return state;
    } catch (error) {
      console.error('Error refreshing network state:', error);
      return null;
    }
  };

  const value = {
    isConnected,
    connectionType,
    isInternetReachable,
    connectionDetails,
    isConnectionStrong: isConnectionStrong(),
    connectionQuality: getConnectionQuality(),
    refreshNetworkState,
  };

  return (
    <NetworkContext.Provider value={value}>
      {children}
    </NetworkContext.Provider>
  );
};

export default NetworkContext;
