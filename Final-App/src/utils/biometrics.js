import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';

const BIOMETRIC_CREDENTIALS_KEY = 'biometric_credentials';

export const BiometricsUtil = {
  // Check if biometrics is available on the device
  isBiometricsAvailable: async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      return hasHardware && isEnrolled;
    } catch (error) {
      console.error('Error checking biometrics availability:', error);
      return false;
    }
  },

  // Get available biometric types
  getBiometricTypes: async () => {
    try {
      const types = await LocalAuthentication.supportedAuthenticationTypesAsync();
      return types;
    } catch (error) {
      console.error('Error getting biometric types:', error);
      return [];
    }
  },

  // Get human-readable biometric type name
  getBiometricTypeName: (type) => {
    switch (type) {
      case LocalAuthentication.AuthenticationType.FINGERPRINT:
        return 'Fingerprint';
      case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
        return 'Face ID';
      case LocalAuthentication.AuthenticationType.IRIS:
        return 'Iris';
      default:
        return 'Biometric';
    }
  },

  // Check if biometrics is enabled for the app
  isBiometricsEnabled: async () => {
    try {
      const credentials = await SecureStore.getItemAsync(BIOMETRIC_CREDENTIALS_KEY);
      return !!credentials;
    } catch (error) {
      console.error('Error checking biometrics enabled status:', error);
      return false;
    }
  },

  // Enable biometrics for the app
  enableBiometrics: async (username, password) => {
    try {
      // First, authenticate with biometrics to ensure it works
      const authResult = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Enable biometric authentication',
        fallbackLabel: 'Use password instead',
        disableDeviceFallback: false,
      });

      if (authResult.success) {
        // Store credentials securely
        const credentials = JSON.stringify({ username, password });
        await SecureStore.setItemAsync(BIOMETRIC_CREDENTIALS_KEY, credentials);
        return true;
      } else {
        console.log('Biometric authentication failed:', authResult.error);
        return false;
      }
    } catch (error) {
      console.error('Error enabling biometrics:', error);
      return false;
    }
  },

  // Disable biometrics for the app
  disableBiometrics: async () => {
    try {
      await SecureStore.deleteItemAsync(BIOMETRIC_CREDENTIALS_KEY);
      return true;
    } catch (error) {
      console.error('Error disabling biometrics:', error);
      return false;
    }
  },

  // Authenticate with biometrics and get stored credentials
  authenticateWithBiometrics: async () => {
    try {
      // Check if credentials are stored
      const storedCredentials = await SecureStore.getItemAsync(BIOMETRIC_CREDENTIALS_KEY);
      if (!storedCredentials) {
        throw new Error('No biometric credentials stored');
      }

      // Authenticate with biometrics
      const authResult = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate with biometrics',
        fallbackLabel: 'Use password instead',
        disableDeviceFallback: false,
      });

      if (authResult.success) {
        // Return stored credentials
        return JSON.parse(storedCredentials);
      } else {
        console.log('Biometric authentication failed:', authResult.error);
        return null;
      }
    } catch (error) {
      console.error('Error authenticating with biometrics:', error);
      return null;
    }
  },

  // Get security level of biometric authentication
  getSecurityLevel: async () => {
    try {
      const securityLevel = await LocalAuthentication.getEnrolledLevelAsync();
      return securityLevel;
    } catch (error) {
      console.error('Error getting security level:', error);
      return LocalAuthentication.SecurityLevel.NONE;
    }
  },

  // Check if device has biometric hardware
  hasHardware: async () => {
    try {
      return await LocalAuthentication.hasHardwareAsync();
    } catch (error) {
      console.error('Error checking biometric hardware:', error);
      return false;
    }
  },

  // Check if user has enrolled biometrics
  isEnrolled: async () => {
    try {
      return await LocalAuthentication.isEnrolledAsync();
    } catch (error) {
      console.error('Error checking biometric enrollment:', error);
      return false;
    }
  }
};

export default BiometricsUtil;
