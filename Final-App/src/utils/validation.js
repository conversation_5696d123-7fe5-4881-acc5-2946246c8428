// Validation utilities for form inputs
export const ValidationUtil = {
  // Email validation
  validateEmail: (email) => {
    if (!email) {
      return { isValid: false, message: 'Email is required' };
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { isValid: false, message: 'Please enter a valid email address' };
    }
    
    return { isValid: true, message: '' };
  },

  // Password validation
  validatePassword: (password, options = {}) => {
    const { minLength = 6, requireUppercase = false, requireNumbers = false, requireSpecialChars = false } = options;
    
    if (!password) {
      return { isValid: false, message: 'Password is required' };
    }
    
    if (password.length < minLength) {
      return { isValid: false, message: `Password must be at least ${minLength} characters long` };
    }
    
    if (requireUppercase && !/[A-Z]/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one uppercase letter' };
    }
    
    if (requireNumbers && !/\d/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one number' };
    }
    
    if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one special character' };
    }
    
    return { isValid: true, message: '' };
  },

  // Phone number validation
  validatePhone: (phone) => {
    if (!phone) {
      return { isValid: false, message: 'Phone number is required' };
    }
    
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
      return { isValid: false, message: 'Please enter a valid phone number' };
    }
    
    return { isValid: true, message: '' };
  },

  // Name validation
  validateName: (name) => {
    if (!name) {
      return { isValid: false, message: 'Name is required' };
    }
    
    if (name.length < 2) {
      return { isValid: false, message: 'Name must be at least 2 characters long' };
    }
    
    return { isValid: true, message: '' };
  },

  // Required field validation
  validateRequired: (value, fieldName) => {
    if (!value || value.toString().trim() === '') {
      return { isValid: false, message: `${fieldName} is required` };
    }
    
    return { isValid: true, message: '' };
  },

  // Number validation
  validateNumber: (value, options = {}) => {
    const { min, max, allowDecimals = true } = options;
    
    if (!value && value !== 0) {
      return { isValid: false, message: 'This field is required' };
    }
    
    const numValue = parseFloat(value);
    
    if (isNaN(numValue)) {
      return { isValid: false, message: 'Please enter a valid number' };
    }
    
    if (!allowDecimals && !Number.isInteger(numValue)) {
      return { isValid: false, message: 'Please enter a whole number' };
    }
    
    if (min !== undefined && numValue < min) {
      return { isValid: false, message: `Value must be at least ${min}` };
    }
    
    if (max !== undefined && numValue > max) {
      return { isValid: false, message: `Value must be at most ${max}` };
    }
    
    return { isValid: true, message: '' };
  },

  // Date validation
  validateDate: (date) => {
    if (!date) {
      return { isValid: false, message: 'Date is required' };
    }
    
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      return { isValid: false, message: 'Please enter a valid date' };
    }
    
    return { isValid: true, message: '' };
  }
};

export default ValidationUtil;
