import React, {useState} from 'react';
import { StyleSheet, Text, View, Switch, ActivityIndicator, TouchableOpacity, Alert } from 'react-native';
import {StatusBar} from 'expo-status-bar';
import {Ionicons} from '@expo/vector-icons';
import { ThemeProvider, useTheme } from './src/context/ThemeContext';
import { AuthProvider, useAuth } from './src/context/AuthContext';
import { NetworkProvider, useNetwork } from './src/context/NetworkContext';
import { EnhancedNetworkProvider, useEnhancedNetwork } from './src/context/EnhancedNetworkContext';
import { SyncProvider, useSync } from './src/context/SyncContext';
import { NotificationProvider, useNotification } from './src/context/NotificationContext';
import { AnalyticsProvider, useAnalytics } from './src/context/AnalyticsContext';
import { LanguageProvider, useLanguage } from './src/context/LanguageContext';
import { BackupProvider, useBackup } from './src/context/BackupContext';
import { SecurityProvider, useSecurity } from './src/context/SecurityContext';
import { OfflineProvider, useOffline } from './src/context/OfflineContext';
import { ReportsProvider, useReports } from './src/context/ReportsContext';
import {
  UIComponentsDemo,
  LoginScreen,
  ForgotPasswordScreen,
  DashboardScreen,
  CustomersScreen,
  OrdersScreen,
  InventoryScreen,
  SettingsScreen,
  BackupScreen,
  SecurityScreen,
  OfflineScreen,
  ReportsScreen
} from './src/screens';
import { OfflineNotice, EnhancedOfflineNotice, SyncIndicator, NotificationBadge, NotificationCenter } from './src/components/common';

export default function App() {
  return (
    <NetworkProvider>
      <EnhancedNetworkProvider>
        <AuthProvider>
          <ThemeProvider>
            <SyncProvider>
              <NotificationProvider>
                <AnalyticsProvider>
                  <LanguageProvider>
                    <BackupProvider>
                      <SecurityProvider>
                        <OfflineProvider>
                          <ReportsProvider>
                            <AppContent />
                          </ReportsProvider>
                        </OfflineProvider>
                      </SecurityProvider>
                    </BackupProvider>
                  </LanguageProvider>
                </AnalyticsProvider>
              </NotificationProvider>
            </SyncProvider>
          </ThemeProvider>
        </AuthProvider>
      </EnhancedNetworkProvider>
    </NetworkProvider>
  );
}

function AppContent() {
  const {isDark} = useTheme();
  const { user, isLoading, isAuthenticated } = useAuth();
  const { isConnected, isInitialized } = useNetwork();
  const { isConnected: isEnhancedConnected, isOfflineMode } = useEnhancedNetwork();
  const { pendingActions, isSyncing, syncNow } = useSync();
  const {unreadCount} = useNotification();
  const {trackScreenView} = useAnalytics();
  const { t, language, changeLanguage } = useLanguage();
  const {lastBackup} = useBackup();
  const {isSecureEnvironment} = useSecurity();
  const { pendingActions: offlinePendingActions } = useOffline();
  const { reports, favoriteReports } = useReports();
  const [showUIDemo, setShowUIDemo] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [activeScreen, setActiveScreen] = useState('dashboard');
  const [authScreen, setAuthScreen] = useState('login');

  // Show UI Components Demo
  if (showUIDemo) {
    return (
      <View style={{ flex: 1 }}>
        <UIComponentsDemo />
        <TouchableOpacity
          style={[
            styles.backButton,
            { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)' }
          ]}
          onPress={() => setShowUIDemo(false)}
        >
          <Text style={{ color: isDark ? '#ffffff' : '#000000' }}>
            Back to App
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show loading indicator while checking authentication
  if (isLoading) {
    return (
      <View style={[
        styles.container,
        { backgroundColor: isDark ? '#121212' : '#f5f5f5' }
      ]}>
        <ActivityIndicator size="large" color={isDark ? '#bb86fc' : '#6200ee'} />
        <Text style={[
          styles.subtitle,
          { color: isDark ? '#bbbbbb' : '#666666', marginTop: 20 }
        ]}>
          Loading...
        </Text>
      </View>
    );
  }

  // Show auth screens if not authenticated
  if (!isAuthenticated) {
    if (authScreen === 'login') {
      return (
        <LoginScreen
          onForgotPassword={() => setAuthScreen('forgotPassword')}
        />
      );
    } else {
      return (
        <ForgotPasswordScreen
          onBack={() => setAuthScreen('login')}
        />
      );
    }
  }

  // Show the active screen for authenticated users
  const renderScreen = () => {
    switch (activeScreen) {
      case 'dashboard':
        return <DashboardScreen navigation={{ navigate: setActiveScreen }} />;
      case 'customers':
        return <CustomersScreen navigation={{ navigate: setActiveScreen }} />;
      case 'orders':
        return <OrdersScreen navigation={{ navigate: setActiveScreen }} />;
      case 'inventory':
        return <InventoryScreen navigation={{ navigate: setActiveScreen }} />;
      case 'reports':
        return <ReportsScreen navigation={{ navigate: setActiveScreen }} />;
      case 'settings':
        return <SettingsScreen navigation={{ navigate: setActiveScreen }} />;
      case 'BackupScreen':
        return <BackupScreen navigation={{ goBack: () => setActiveScreen('settings') }} />;
      case 'SecurityScreen':
        return <SecurityScreen navigation={{ goBack: () => setActiveScreen('settings') }} />;
      case 'OfflineScreen':
        return <OfflineScreen navigation={{ goBack: () => setActiveScreen('settings') }} />;
      default:
        return <DashboardScreen navigation={{ navigate: setActiveScreen }} />;
    }
  };

  // Render bottom navigation
  const renderBottomNav = () => {
    const navItems = [
      { id: 'dashboard', icon: 'home', label: 'Home' },
      { id: 'customers', icon: 'people', label: 'Customers' },
      { id: 'orders', icon: 'receipt', label: 'Orders' },
      { id: 'inventory', icon: 'cube', label: 'Inventory' },
      { id: 'reports', icon: 'bar-chart', label: 'Reports' },
      { id: 'settings', icon: 'settings', label: 'Settings' },
    ];

    return (
      <View style={[
        styles.bottomNav,
        { backgroundColor: isDark ? '#1e1e1e' : '#ffffff' }
      ]}>
        {navItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.navItem}
            onPress={() => {
              setActiveScreen(item.id);
              trackScreenView(item.id);
            }}
          >
            <Ionicons
              name={activeScreen === item.id ? `${item.icon}` : `${item.icon}-outline`}
              size={24}
              color={activeScreen === item.id
                ? (isDark ? '#bb86fc' : '#6200ee')
                : (isDark ? '#bbbbbb' : '#666666')
              }
            />
            <Text style={[
                styles.navLabel,
                {
                  color: activeScreen === item.id
                    ? (isDark ? '#bb86fc' : '#6200ee')
                    : (isDark ? '#bbbbbb' : '#666666')
                }
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <EnhancedOfflineNotice />
      <View style={{ flex: 1 }}>
        {renderScreen()}
      </View>
      {renderBottomNav()}

      {/* Sync Indicator */}
      {isAuthenticated && <SyncIndicator />}

      {/* Notification Badge */}
      {isAuthenticated && (
        <TouchableOpacity
          style={styles.notificationBadge}
          onPress={() => setShowNotifications(true)}
        >
          <NotificationBadge />
        </TouchableOpacity>
      )}

      {/* Notification Center */}
      <NotificationCenter
        visible={showNotifications}
        onClose={() => setShowNotifications(false)}
      />

      {/* UI Demo Button */}
      <TouchableOpacity
        style={[
          styles.uiDemoButton,
          { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)' }
        ]}
        onPress={() => setShowUIDemo(true)}
      >
        <Text style={{ color: isDark ? '#ffffff' : '#000000' }}>
          UI Demo
        </Text>
      </TouchableOpacity>

      <StatusBar style={isDark ? 'light' : 'dark'} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  content: {
    width: '100%',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 20,
    textAlign: 'center',
  },
  infoText: {
    fontSize: 16,
    marginBottom: 30,
    textAlign: 'center',
  },
  themeToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 10,
    minWidth: 200,
    alignItems: 'center',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonContainer: {
    width: '80%',
    marginTop: 20,
  },
  backButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  uiDemoButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
  navLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  notificationBadge: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 100,
  }
});
