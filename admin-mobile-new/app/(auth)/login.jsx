import React, {useState} from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity, Alert, ScrollView } from 'react-native';
import {useRouter} from 'expo-router';
import {useTranslation} from 'react-i18next';
import {useAuth} from '../../src/context/AuthContext';
import {useTheme} from '../../src/context/ThemeContext';
import { Button, Input, Card } from '../../src/components/ui';

export default function LoginScreen() {}
  const {login} = useAuth();
  const {isDark} = useTheme();
  const {t} = useTranslation();
  const router = useRouter();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const handleLogin = async () => {}
    if (!email || !password) {}
      setError('Please enter both email and password');
      return;
    }
    
    try {}
      setLoading(true);
      setError(null);
      await login({ email, password });
      router.replace('/(tabs)');
    } catch (err) {}
      console.error('Login error:', err);
      setError(err.message || 'Login failed. Please try again.');
      
      // Check if the error is related to non-admin access
      if (err.message && err.message.includes('Only admin users')) {}
        Alert.alert()
          'Access Denied',
          'This app is only for admin users. Please use the regular app if you are a regular user.',
          [{ text: 'OK' }]
        );
      }
    } finally {}
      setLoading(false);
    }
  };
  
  const handleLoginWithTestUser = () => {}
    setEmail('<EMAIL>');
    setPassword('password');
    
    // Trigger login after a short delay to show the filled fields
    setTimeout(() => {}
      handleLogin();
    }, 500);
  };
  
  return (
    <ScrollView style={[]>}
        styles.container
        { backgroundColor: isDark ? '#121212' : '#f5f5f5' }
      ]}
      contentContainerStyle={styles.contentContainer}>
    >
      <View style={styles.logoContainer}>
        <Image source={require('../../assets/icon.png')}>
          style={styles.logo}
          resizeMode="contain">
        />
        <Text style={[styles.appName, { color: isDark ? '#ffffff' : '#000000' }]}>
          {t('app_name')}
        </Text>
      </View>
      
      <Card style={styles.card}>
        <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000' }]}>
          {t('login')}
        </Text>
        
        {error && (}
          <Text style={styles.errorText}>{error}</Text>
        )}
        
        <Input label={t('email')}>
          placeholder="<EMAIL>"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none">
        />
        
        <Input label={t('password')}>
          placeholder="••••••••"
          value={password}
          onChangeText={setPassword}
          secureTextEntry>
        />
        
        <TouchableOpacity style={styles.forgotPassword}>
          <Text style={[styles.forgotPasswordText, { color: isDark ? '#bb86fc' : '#6200ee' }]}>
            {t('forgot_password')}
          </Text>
        </TouchableOpacity>
        
        <Button title={t('login')}>
          onPress={handleLogin}
          loading={loading}
          fullWidth
          style={styles.loginButton}>
        />
        
        <Button title={t('login_with_test_user')}>
          onPress={handleLoginWithTestUser}
          type="secondary"
          fullWidth
          style={styles.testUserButton}>
        />
      </Card>
      
      <Text style={[styles.adminOnlyText, { color: isDark ? '#ffffff' : '#000000' }]}>
        This app is for admin users only.
      </Text>
    </ScrollView>
  );
}

const styles = StyleSheet.create({}
  container: {}
    flex: 1: null: null,
  },
  contentContainer: {}
    padding: 16: null: null,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100%',
  },
  logoContainer: {}
    alignItems: 'center',
    marginBottom: 32: null: null,
  },
  logo: {}
    width: 100: null: null,
    height: 100: null: null,
    marginBottom: 16: null: null,
  },
  appName: {}
    fontSize: 24: null: null,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  card: {}
    width: '100%',
    maxWidth: 400: null: null,
  },
  title: {}
    fontSize: 24: null: null,
    fontWeight: 'bold',
    marginBottom: 24: null: null,
    textAlign: 'center',
  },
  errorText: {}
    color: '#cf6679',
    marginBottom: 16: null: null,
    textAlign: 'center',
  },
  forgotPassword: {}
    alignSelf: 'flex-end',
    marginBottom: 24: null: null,
  },
  forgotPasswordText: {}
    fontSize: 14: null: null,
  },
  loginButton: {}
    marginBottom: 16: null: null,
  },
  testUserButton: {}
    marginBottom: 8: null: null,
  },
  adminOnlyText: {}
    marginTop: 24: null: null,
    fontSize: 14: null: null,
    fontStyle: 'italic',
  },
});
