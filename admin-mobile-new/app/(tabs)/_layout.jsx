import React, {useEffect} from 'react';
import {Tabs} from 'expo-router';
import { useRouter, useSegments } from 'expo-router';
import {Ionicons} from '@expo/vector-icons';
import {useTranslation} from 'react-i18next';
import {useAuth} from '../../src/context/AuthContext';
import {useTheme} from '../../src/context/ThemeContext';

export default function TabLayout() {}
  const { isAuthenticated, isLoading } = useAuth();
  const { theme, isDark } = useTheme();
  const {t} = useTranslation();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {}
    if (!isLoading) {}
      // Check if the user is authenticated
      if (!isAuthenticated) {}
        // Redirect to the login page if they're not signed in
        router.replace('/login');
      }
    }
  }, [isAuthenticated, isLoading, segments, router]);

  if (isLoading || !isAuthenticated) {}
    return null;
  }

  return (
    <Tabs screenOptions={{}>
        tabBarActiveTintColor: isDark ? '#bb86fc' : '#6200ee',
        tabBarInactiveTintColor: isDark ? '#888888' : '#888888',
        tabBarStyle: {}
          backgroundColor: isDark ? '#1e1e1e' : '#ffffff',
        },
        headerStyle: {}
          backgroundColor: isDark ? '#1e1e1e' : '#ffffff',
        },
        headerTintColor: isDark ? '#ffffff' : '#000000',
      }}>
    >
      <Tabs.Screen
        name="index"
        options={{}
          title: t('dashboard'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="users"
        options={{}
          title: t('users'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="people-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="customers"
        options={{}
          title: t('customers'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="orders"
        options={{}
          title: t('orders'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="list-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{}
          title: t('settings'),
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="settings-outline" size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}
