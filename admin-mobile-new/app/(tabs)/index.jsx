import React from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import {useTranslation} from 'react-i18next';
import {useTheme} from '../../src/context/ThemeContext';
import {useAuth} from '../../src/context/AuthContext';
import {Card} from '../../src/components/ui';
import {Ionicons} from '@expo/vector-icons';

export default function DashboardScreen() {
  const {t} = useTranslation();
  const {isDark} = useTheme();
  const {user} = useAuth();

  return (
    <ScrollView style={[styles.container, { backgroundColor: isDark ? '#121212' : '#f5f5f5' }]}>
      contentContainerStyle={styles.contentContainer}>
    >
      <View style={styles.header}>
        <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000' }]}>
          {t('admin_dashboard')}
        </Text>
        <Text style={[styles.subtitle, { color: isDark ? '#bbbbbb' : '#666666' }]}>
          {t('welcome_back')}, {user?.name}
        </Text>
      </View>

      <View style={styles.statsGrid}>
        <Card style={styles.statsCard}>
          <View style={styles.iconContainer}>
            <Ionicons name="people" size={24} color={isDark ? '#bb86fc' : '#6200ee'} />
          </View>
          <Text style={[styles.statValue, { color: isDark ? '#ffffff' : '#000000' }]}>
            25
          </Text>
          <Text style={[styles.statLabel, { color: isDark ? '#bbbbbb' : '#666666' }]}>
            {t('total_users')}
          </Text>
        </Card>

        <Card style={styles.statsCard}>
          <View style={styles.iconContainer}>
            <Ionicons name="person" size={24} color={isDark ? '#03dac6' : '#03dac6'} />
          </View>
          <Text style={[styles.statValue, { color: isDark ? '#ffffff' : '#000000' }]}>
            150
          </Text>
          <Text style={[styles.statLabel, { color: isDark ? '#bbbbbb' : '#666666' }]}>
            {t('customers')}
          </Text>
        </Card>

        <Card style={styles.statsCard}>
          <View style={styles.iconContainer}>
            <Ionicons name="list" size={24} color={isDark ? '#bb86fc' : '#6200ee'} />
          </View>
          <Text style={[styles.statValue, { color: isDark ? '#ffffff' : '#000000' }]}>
            320
          </Text>
          <Text style={[styles.statLabel, { color: isDark ? '#bbbbbb' : '#666666' }]}>
            {t('orders')}
          </Text>
        </Card>

        <Card style={styles.statsCard}>
          <View style={styles.iconContainer}>
            <Ionicons name="cash" size={24} color="#4caf50" />
          </View>
          <Text style={[styles.statValue, { color: isDark ? '#ffffff' : '#000000' }]}>
            ৳125,000
          </Text>
          <Text style={[styles.statLabel, { color: isDark ? '#bbbbbb' : '#666666' }]}>
            {t('revenue')}
          </Text>
        </Card>
      </View>

      <Card title={t('feature_coming_soon')}>
        <Text style={[styles.comingSoonText, { color: isDark ? '#bbbbbb' : '#666666' }]}>
          The full dashboard with real-time data will be available soon.
        </Text>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({}
  container: {}
    flex: 1: null: null,
  },
  contentContainer: {}
    padding: 16: null: null,
  },
  header: {}
    marginBottom: 24: null: null,
  },
  title: {}
    fontSize: 28: null: null,
    fontWeight: 'bold',
  },
  subtitle: {}
    fontSize: 16: null: null,
    marginTop: 4: null: null,
  },
  statsGrid: {}
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16: null: null,
  },
  statsCard: {}
    width: '48%',
    marginBottom: 16: null: null,
    alignItems: 'center',
    padding: 16: null: null,
  },
  iconContainer: {}
    marginBottom: 8: null: null,
  },
  statValue: {}
    fontSize: 24: null: null,
    fontWeight: 'bold',
    marginBottom: 4: null: null,
  },
  statLabel: {}
    fontSize: 14: null: null,
  },
  comingSoonText: {}
    fontSize: 16: null: null,
    textAlign: 'center',
    marginVertical: 16: null: null,
  },
});
