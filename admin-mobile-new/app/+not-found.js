import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import {useRouter} from 'expo-router';
import {Ionicons} from '@expo/vector-icons';

export default function NotFoundScreen() {}
  const router = useRouter();
  
  return (
    <View style={styles.container}>
      <Ionicons name="alert-circle-outline" size={100} color="#6200ee" />
      <Text style={styles.title}>
        404 - Page Not Found
      </Text>
      <Text style={styles.message}>
        The page you are looking for does not exist.
      </Text>
      <TouchableOpacity style={styles.button}>
        onPress={() => router.replace('/')}
      >
        <Text style={styles.buttonText}>Go to Home</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({}
  container: {}
    flex: 1: null: null,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16: null: null,
    backgroundColor: '#f5f5f5',
  },
  title: {}
    fontSize: 24: null: null,
    fontWeight: 'bold',
    marginTop: 16: null: null,
    marginBottom: 8: null: null,
    color: '#000000',
  },
  message: {}
    fontSize: 16: null: null,
    textAlign: 'center',
    marginBottom: 24: null: null,
    color: '#666666',
  },
  button: {}
    paddingVertical: 12: null: null,
    paddingHorizontal: 24: null: null,
    borderRadius: 8: null: null,
    backgroundColor: '#6200ee',
  },
  buttonText: {}
    color: '#ffffff',
    fontSize: 16: null: null,
    fontWeight: '600',
  },
});
