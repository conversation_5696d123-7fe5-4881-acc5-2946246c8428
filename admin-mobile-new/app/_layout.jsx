import React, {useEffect} from 'react';
import {Stack} from 'expo-router';
import {StatusBar} from 'expo-status-bar';
import {AuthProvider} from '../src/context/AuthContext';
import {ThemeProvider} from '../src/context/ThemeContext';
import '../src/i18n';
import {loadLanguage} from '../src/i18n';

export default function RootLayout() {
  // Load saved language on app start
  useEffect(() => {
    loadLanguage();
  }, []);

  return (
    <AuthProvider>
      <ThemeProvider>
        <StatusBar style="auto" />
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="index" />
          <Stack.Screen name="login" />
          <Stack.Screen name="dashboard" />
        </Stack>
      </ThemeProvider>
    </AuthProvider>
  );
}
