import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../src/context/AuthContext';
import { useTheme } from '../src/context/ThemeContext';
import { Card, Button } from '../src/components/ui';

export default function Dashboard() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const { theme, isDark, setTheme } = useTheme();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout();
            router.replace('/login');
          },
        },
      ]
    );
  };

  const toggleTheme = () => {
    setTheme(isDark ? 'light' : 'dark');
  };

  const dashboardItems = [
    {
      title: 'Customer Management',
      description: 'Manage customer profiles and information',
      icon: '👥',
      onPress: () => Alert.alert('Feature', 'Customer Management coming soon!'),
    },
    {
      title: 'Order Management',
      description: 'View and manage customer orders',
      icon: '📋',
      onPress: () => Alert.alert('Feature', 'Order Management coming soon!'),
    },
    {
      title: 'Inventory',
      description: 'Track fabrics and materials',
      icon: '📦',
      onPress: () => Alert.alert('Feature', 'Inventory Management coming soon!'),
    },
    {
      title: 'Reports',
      description: 'View business analytics and reports',
      icon: '📊',
      onPress: () => Alert.alert('Feature', 'Reports coming soon!'),
    },
    {
      title: 'Settings',
      description: 'App settings and preferences',
      icon: '⚙️',
      onPress: () => Alert.alert('Feature', 'Settings coming soon!'),
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.welcomeText}>Welcome back,</Text>
            <Text style={styles.userName}>{user?.name || 'Admin'}</Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={[styles.themeButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
              onPress={toggleTheme}
            >
              <Text style={styles.themeButtonText}>
                {isDark ? '☀️' : '🌙'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.logoutButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
              onPress={handleLogout}
            >
              <Text style={styles.logoutButtonText}>Logout</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Dashboard Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Quick Actions
          </Text>

          <View style={styles.grid}>
            {dashboardItems.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={styles.gridItem}
                onPress={item.onPress}
              >
                <Card style={styles.dashboardCard}>
                  <View style={styles.cardContent}>
                    <Text style={styles.cardIcon}>{item.icon}</Text>
                    <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
                      {item.title}
                    </Text>
                    <Text style={[styles.cardDescription, { color: theme.colors.textSecondary }]}>
                      {item.description}
                    </Text>
                  </View>
                </Card>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Stats Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Overview
          </Text>

          <View style={styles.statsContainer}>
            <Card style={styles.statCard}>
              <Text style={[styles.statNumber, { color: theme.colors.primary }]}>24</Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Active Orders
              </Text>
            </Card>

            <Card style={styles.statCard}>
              <Text style={[styles.statNumber, { color: theme.colors.primary }]}>156</Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Total Customers
              </Text>
            </Card>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeText: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
  },
  userName: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 10,
  },
  themeButton: {
    padding: 8,
    borderRadius: 20,
    minWidth: 36,
    alignItems: 'center',
  },
  themeButtonText: {
    fontSize: 16,
  },
  logoutButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  logoutButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  gridItem: {
    width: '48%',
  },
  dashboardCard: {
    minHeight: 120,
  },
  cardContent: {
    alignItems: 'center',
    padding: 16,
  },
  cardIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  statCard: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    textAlign: 'center',
  },
});
