import React, { useEffect } from 'react';
import { useRouter } from 'expo-router';

export default function Index() {
  const router = useRouter();

  useEffect(() => {
    // Directly navigate to login screen for testing
    console.log('Index: Directly navigating to login');
    router.replace('/login');
  }, [router]);

  return null; // Don't render anything, just redirect
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});