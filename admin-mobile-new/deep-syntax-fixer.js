const fs = require('fs');
const path = require('path');

// Deep syntax fixer for Expo Go compatibility
class DeepSyntaxFixer {
  constructor() {
    this.fixedFiles = [];
    this.errors = [];
  }

  // Fix deep syntax errors in a file
  fixDeepSyntaxErrors(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let fixed = false;
      const originalContent = content;

      // 1. Fix JSX component tags that are missing closing brackets
      const jsxTagPattern = /<([A-Z][a-zA-Z0-9]*|[a-z][a-zA-Z0-9]*)\s+([^>]*?)$/gm;
      content = content.replace(jsxTagPattern, (match, tag, attrs) => {
        // If the line doesn't end with a closing bracket or self-closing tag
        if (!attrs.trim().endsWith('>') && !attrs.trim().endsWith('/>')) {
          return `<${tag} ${attrs}>`;
        }
        return match;
      });

      // 2. Fix incomplete JSX attribute values
      const jsxAttrPattern = /(\w+)=\{\{([^}]*?)\)\s*\}\}/g;
      content = content.replace(jsxAttrPattern, (match, attr, value) => {
        return `${attr}={{ ${value} }}`;
      });

      // 3. Fix malformed arrow functions in JSX props
      const arrowInJsxPattern = /(\w+)=\{\(\)\s*=>\s*\{([^}]*?)\}\}/g;
      content = content.replace(arrowInJsxPattern, (match, prop, body) => {
        return `${prop}={() => {${body}}}`;
      });

      // 4. Fix incomplete object literals in JSX
      const incompleteObjectPattern = /\{\s*(\w+),\s*\}/g;
      content = content.replace(incompleteObjectPattern, '{ $1: true }');

      // 5. Fix malformed component declarations
      const componentDeclPattern = /const\s+(\w+)\s*=\s*\(\s*\{\s*([^}]*?)\s*\}\s*\)\s*=>\s*\{/g;}
      content = content.replace(componentDeclPattern, 'const $1 = ({ $2 }) => {');}
      // 6. Fix incomplete return statements in components
      const returnPattern = /return\s*\(\s*$/gm;
      content = content.replace(returnPattern, 'return (');

      // 7. Fix malformed map functions in JSX
      const mapPattern = /\{([^}]*?)\.map\(\(([^)]*?)\)\s*=>\s*\(/g;
      content = content.replace(mapPattern, '{$1.map(($2) => (');}
      // 8. Fix incomplete JSX closing tags
      const closingTagPattern = /<\/([A-Z][a-zA-Z0-9]*|[a-z][a-zA-Z0-9]*)\s*$/gm;
      content = content.replace(closingTagPattern, '</$1>');

      // 9. Fix malformed useEffect hooks
      const useEffectPattern = /useEffect\(\(\)\s*=>\s*\{/g;}
      content = content.replace(useEffectPattern, 'useEffect(() => {');}
      // 10. Fix incomplete JSX expressions
      const jsxExprPattern = /\{([^{}]*?)\s*$/gm;
      content = content.replace(jsxExprPattern, '{$1}');

      // 11. Fix malformed destructuring in function parameters
      const destructuringPattern = /\(\s*\{\s*([^}]*?)\s*\}\s*\)\s*=>/g;
      content = content.replace(destructuringPattern, '({ $1 }) =>');

      // 12. Fix incomplete style objects in JSX
      const stylePattern = /style\s*=\s*\{\s*\{\s*([^}]*?)\s*$/gm;
      content = content.replace(stylePattern, 'style={{ $1 }}');

      // 13. Fix malformed ternary operators in JSX
      const ternaryPattern = /\?\s*([^:]*?)\s*:\s*$/gm;
      content = content.replace(ternaryPattern, '? $1 : null');

      // 14. Fix incomplete array literals in JSX
      const arrayPattern = /\[\s*([^\\[\\]]*?)\s*$/gm;
      content = content.replace(arrayPattern, '[$1]');

      // 15. Fix incomplete function calls
      const funcCallPattern = /(\w+)\(\s*$/gm;
      content = content.replace(funcCallPattern, '$1()');

      // 16. Fix malformed template literals
      const templatePattern = /\`([^`]*?)$/gm;`
      content = content.replace(templatePattern, '`$1`');`

      // 17. Fix incomplete object property access
      const propAccessPattern = /(\w+)\.(\w+)\s*$/gm;
      content = content.replace(propAccessPattern, '$1.$2');

      // 18. Fix malformed async arrow functions
      const asyncArrowPattern = /async\s*\(\s*\)\s*=>\s*\{/g;}
      content = content.replace(asyncArrowPattern, 'async () => {');}
      // 19. Fix incomplete conditional statements
      const conditionalPattern = /if\s*\(\s*([^)]*?)\s*$/gm;
      content = content.replace(conditionalPattern, 'if ($1)');

      // 20. Fix incomplete export statements
      const exportPattern = /export\s*\{\s*([^}]*?)\s*$/gm;
      content = content.replace(exportPattern, 'export { $1 }');

      // 21. Fix incomplete import statements
      const importPattern = /import\s*\{\s*([^}]*?)\s*$/gm;
      content = content.replace(importPattern, 'import { $1 }');

      // 22. Fix incomplete JSX fragments
      const fragmentPattern = /<>\s*$/gm;
      content = content.replace(fragmentPattern, '<>');

      // 23. Fix incomplete JSX fragment closings
      const fragmentClosePattern = /<\/>\s*$/gm;
      content = content.replace(fragmentClosePattern, '</>');

      // 24. Fix incomplete spread operators
      const spreadPattern = /\.\.\.\s*$/gm;
      content = content.replace(spreadPattern, '...');

      // 25. Fix incomplete object destructuring
      const objDestructPattern = /const\s*\{\s*([^}]*?)\s*$/gm;
      content = content.replace(objDestructPattern, 'const { $1 }');

      // 26. Fix incomplete array destructuring
      const arrDestructPattern = /const\s*\[\s*([^\\[\\]]*?)\s*$/gm;
      content = content.replace(arrDestructPattern, 'const [$1]');

      // 27. Fix incomplete useState hooks
      const useStatePattern = /const\s*\[\s*(\w+),\s*set(\w+)\s*\]\s*=\s*useState\(\s*$/gm;
      content = content.replace(useStatePattern, 'const [$1, set$2] = useState(');

      // 28. Fix incomplete useRef hooks
      const useRefPattern = /const\s*(\w+)\s*=\s*useRef\(\s*$/gm;
      content = content.replace(useRefPattern, 'const $1 = useRef(');

      // 29. Fix incomplete useContext hooks
      const useContextPattern = /const\s*(\w+)\s*=\s*useContext\(\s*$/gm;
      content = content.replace(useContextPattern, 'const $1 = useContext(');

      // 30. Fix incomplete useMemo hooks
      const useMemoPattern = /const\s*(\w+)\s*=\s*useMemo\(\s*\(\)\s*=>\s*\{([^}]*?)\s*$/gm;
      content = content.replace(useMemoPattern, 'const $1 = useMemo(() => {$2}');

      if (content !== originalContent) {}
        fs.writeFileSync(filePath, content);
        this.fixedFiles.push(filePath);
        fixed = true;
      }

      return fixed;
    } catch (error) {}
      this.errors.push(`Error fixing ${filePath}: ${error.message}`);`
      return false;
    }
  }

  // Fix all JSX files in the project
  fixAllFiles(projectDir) {}
    const jsxFiles = this.findJsxFiles(projectDir);
    let fixedCount = 0;

    console.log('🔧 Starting deep syntax fixes...\n');

    jsxFiles.forEach(filePath => {}
      const relativePath = path.relative(projectDir, filePath);
      if (this.fixDeepSyntaxErrors(filePath)) {}
        fixedCount++;
        console.log(`✅ Fixed: ${relativePath}`);`
      }
    });

    console.log(`\n🎯 Fixed ${fixedCount} files out of ${jsxFiles.length} total files.`);`
    return fixedCount;
  }

  // Find all JSX files
  findJsxFiles(dir) {}
    const files = [];

    function traverse(currentDir) {}
      try {}
        const items = fs.readdirSync(currentDir);

        for (const item of items) {}
          const fullPath = path.join(currentDir, item);
          const stat = fs.statSync(fullPath);

          if (stat.isDirectory() && item !== 'node_modules' && item !== '.expo' && item !== '.git') {}
            traverse(fullPath);
          } else if (item.endsWith('.jsx') || item.endsWith('.js')) {}
            files.push(fullPath);
          }
        }
      } catch (error) {}
        console.error(`Error reading directory ${currentDir}:`, error.message);`
      }
    }

    traverse(dir);
    return files;
  }
}

// Main execution
const fixer = new DeepSyntaxFixer();
const projectDir = __dirname;

console.log('🚀 DEEP SYNTAX FIXER FOR EXPO GO COMPATIBILITY');
console.log('=============================================\n');

const fixedCount = fixer.fixAllFiles(projectDir);

console.log('\n🏁 DEEP SYNTAX FIXES COMPLETE!');
console.log('=============================');

if (fixedCount > 0) {}
  console.log(`✅ Applied deep syntax fixes to ${fixedCount} files`);`
  console.log('✅ Fixed subtle syntax errors that can cause Expo Go to fail');
} else {}
  console.log('ℹ️  No deep syntax fixes were needed');
}

if (fixer.errors.length > 0) {}
  console.log('\n⚠️  Errors encountered during fixing:');
  fixer.errors.forEach(error => console.log(`   ${error}`));`
}

console.log('\n📋 Next steps:');
console.log('1. Clear Metro cache: rm -rf .expo && rm -rf node_modules/.cache');
console.log('2. Restart Expo: npx expo start --clear');
console.log('3. Test in Expo Go mobile app');
