{"name": "admin-mobile-new", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "axios": "^1.9.0", "expo": "~53.0.9", "expo-analytics-amplitude": "~11.3.0", "expo-crypto": "^14.1.4", "expo-device": "^7.1.4", "expo-document-picker": "^13.1.5", "expo-file-system": "^18.1.10", "expo-linking": "^7.1.5", "expo-local-authentication": "^16.0.4", "expo-localization": "^16.1.5", "expo-notifications": "^0.31.2", "expo-router": "^5.0.7", "expo-secure-store": "^14.2.3", "expo-sharing": "~13.1.5", "expo-status-bar": "~2.2.3", "i18n-js": "^4.5.1", "i18next": "^25.2.0", "react": "19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-svg": "^15.11.2", "react-native-web": "^0.20.0", "uuid": "^11.1.0", "@react-native-community/datetimepicker": "8.3.0", "expo-print": "~14.1.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/uuid": "^10.0.0", "typescript": "~5.8.3"}, "private": true}