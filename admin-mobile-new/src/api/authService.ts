import apiClient from './apiClient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import axios from 'axios';

// Types
export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  role: string;
  language: string;
  theme: string;
  profile_photo?: string;
  is_active: boolean;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  phone?: string;
}

// Direct IP client for fallback
const createDirectIpClient = (ip: string = '*************') => {
  return axios.create({
    baseURL: `http://${ip}:8000/api`,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    timeout: 30000, // 30 seconds timeout
  });
};

const authService = {
  login: async (data: LoginData) => {
    try {
      console.log('Sending admin login request to:', apiClient.defaults.baseURL + '/login');
      console.log('Login data:', { email: data.email, passwordLength: data.password?.length || 0 });
      console.log('Platform:', Platform.OS);

      const response = await apiClient.post('/login', data);
      console.log('Login response:', response.data);

      // Verify that the user is an admin
      if (response.data.user.role !== 'admin') {
        throw new Error('Unauthorized: Only admin users can access this application');
      }

      if (response.data.token) {
        await AsyncStorage.setItem('auth_token', response.data.token);
        await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
      } else {
        console.warn('No token received in login response');
      }

      return response.data;
    } catch (error) {
      console.error('Login service error:', error);
      
      // Try with direct IP as fallback
      try {
        console.log('Trying direct IP for login');
        const directClient = createDirectIpClient();
        
        const response = await directClient.post('/login', data);
        console.log('Direct IP login response:', response.data);
        
        // Verify that the user is an admin
        if (response.data.user.role !== 'admin') {
          throw new Error('Unauthorized: Only admin users can access this application');
        }
        
        if (response.data.token) {
          await AsyncStorage.setItem('auth_token', response.data.token);
          await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
        }
        
        return response.data;
      } catch (directError) {
        console.error('Direct IP login failed:', directError);
        throw directError;
      }
    }
  },
  
  logout: async () => {
    try {
      console.log('Sending logout request to:', apiClient.defaults.baseURL + '/logout');
      console.log('Platform:', Platform.OS);
      
      await apiClient.post('/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local storage regardless of API success
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user');
    }
  },
  
  // Get all users (admin only)
  getUsers: async () => {
    try {
      const response = await apiClient.get('/admin/users');
      return response.data;
    } catch (error) {
      console.error('Get users error:', error);
      throw error;
    }
  },
  
  // Get a specific user (admin only)
  getUser: async (userId: number) => {
    try {
      const response = await apiClient.get(`/admin/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Get user error:', error);
      throw error;
    }
  },
  
  // Create a new user (admin only)
  createUser: async (data: RegisterData & { role: string }) => {
    try {
      const response = await apiClient.post('/admin/users', data);
      return response.data;
    } catch (error) {
      console.error('Create user error:', error);
      throw error;
    }
  },
  
  // Update a user (admin only)
  updateUser: async (userId: number, data: Partial<User>) => {
    try {
      const response = await apiClient.put(`/admin/users/${userId}`, data);
      return response.data;
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  },
  
  // Update a user's role (admin only)
  updateUserRole: async (userId: number, role: string) => {
    try {
      const response = await apiClient.put(`/admin/users/${userId}/role`, { role });
      return response.data;
    } catch (error) {
      console.error('Update user role error:', error);
      throw error;
    }
  },
  
  // Update a user's status (admin only)
  updateUserStatus: async (userId: number, isActive: boolean) => {
    try {
      const response = await apiClient.put(`/admin/users/${userId}/status`, { is_active: isActive });
      return response.data;
    } catch (error) {
      console.error('Update user status error:', error);
      throw error;
    }
  },
  
  // Delete a user (admin only)
  deleteUser: async (userId: number) => {
    try {
      const response = await apiClient.delete(`/admin/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Delete user error:', error);
      throw error;
    }
  },
  
  // Get users report (admin only)
  getUsersReport: async () => {
    try {
      const response = await apiClient.get('/admin/reports/users');
      return response.data;
    } catch (error) {
      console.error('Get users report error:', error);
      throw error;
    }
  },
};

export default authService;
