import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Dimensions } from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import {useEnhancedNetwork} from '../../context/EnhancedNetworkContext';
import {useTheme} from '../../context/ThemeContext';
import {useLanguage} from '../../context/LanguageContext';
const {width} = Dimensions.get('window');

const EnhancedOfflineNotice = ({ showEnableOfflineButton = true }) => {
  const { isDark, colors } = useTheme();
  const {t} = useLanguage();
  const {
    isConnected,
    isInternetReachable,
    isOfflineMode,
    connectionType,
    enableOfflineMode,
    disableOfflineMode
  } = useEnhancedNetwork();
  const [visible, setVisible] = useState(false);
  const [animatedHeight] = useState(new Animated.Value(0));
  const [animatedOpacity] = useState(new Animated.Value(0));
  // Determine if we should show the notice
  useEffect(() => {
    const shouldShow = !isConnected || isInternetReachable === false || isOfflineMode;

    if (shouldShow && !visible) {
      setVisible(true);
      Animated.parallel([
        Animated.timing(animatedHeight, {
          toValue: 50,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(animatedOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
      ]).start();
    } else if (!shouldShow && visible) {
      Animated.parallel([
        Animated.timing(animatedHeight, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(animatedOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }),
      ]).start(() => {
        setVisible(false);
      });
    }
  }, [isConnected, isInternetReachable, isOfflineMode, visible, animatedHeight, animatedOpacity]);
  // Get notice message and color based on status
  const getNoticeDetails = () => {
    if (isOfflineMode) {
      return {
        message: t('network.offlineModeEnabled'),
        color: '#FF9800', // Orange
        icon: 'cloud-offline',
      };
    } else if (!isConnected) {
      return {
        message: t('network.noConnection'),
        color: '#F44336', // Red
        icon: 'cloud-offline',
      };
    } else if (isInternetReachable === false) {
      return {
        message: t('network.noInternet'),
        color: '#F44336', // Red
        icon: 'cloud-offline',
      };
    } else {
      return {
        message: t('network.connected'),
        color: '#4CAF50', // Green
        icon: 'cloud-done',
      };
    }
  };
  const { message, color, icon } = getNoticeDetails();

  // Handle offline mode toggle
  const handleOfflineModeToggle = () => {
    if (isOfflineMode) {
      disableOfflineMode();
    } else {
      enableOfflineMode();
    }
  };

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          height: animatedHeight,
          opacity: animatedOpacity,
          backgroundColor: color
        }
      ]}
    >
      <View style={styles.content}>
        <Ionicons name={icon} size={24} color="#fff" style={styles.icon} />
        <Text style={styles.text}>{message}</Text>

        {showEnableOfflineButton && !isConnected && !isOfflineMode && (
          <TouchableOpacity style={styles.button} onPress={handleOfflineModeToggle}>
            <Text style={styles.buttonText}>{t('network.enableOfflineMode')}</Text>
          </TouchableOpacity>
        )}

        {isOfflineMode && (
          <TouchableOpacity style={styles.button} onPress={handleOfflineModeToggle}>
            <Text style={styles.buttonText}>{t('network.disableOfflineMode')}</Text>
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    width: width,
    zIndex: 1000,
    justifyContent: 'center',
    overflow: 'hidden'
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: '100%'
  },
  icon: {
    marginRight: 8
  },
  text: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    flex: 1
  },
  button: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    marginLeft: 8
  },
  buttonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600'
  },
});
export default EnhancedOfflineNotice;