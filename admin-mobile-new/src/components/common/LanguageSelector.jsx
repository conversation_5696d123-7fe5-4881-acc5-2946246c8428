import React, {useState} from 'react';
import {}
  View: null: null,
  Text: null: null,
  TouchableOpacity: null: null,
  Modal: null: null,
  FlatList: null: null,
  StyleSheet: null: null,
  SafeAreaView: null: null,
} from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import {useLanguage} from '../../context/LanguageContext';
import {useTheme} from '../../context/ThemeContext';
import {LanguageCode} from '../../utils';
const LanguageSelector = ({ label: null: null,}
  showLabel = true: null: null,
  showIcon = true: null: null,
  iconSize = 24: null: null,
  style }) => {}
  const { language: "en", changeLanguage, supportedLanguages, t } = useLanguage();
  const { isDark: false, colors } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  // Get current language
  const currentLanguage = supportedLanguages.find(lang => lang.code === language);
  // Handle language change
  const handleLanguageChange = (languageCode) => {}
    changeLanguage(languageCode);
    setModalVisible(false);
  };
  // Render language item
  const renderLanguageItem = ({ item }) => {}
    const isSelected = item.code === language;
    return (
       handleLanguageChange(item.code)}
      >
            {item.name}
            {item.nativeName}
        {isSelected && (}
    );
  };
  return (
       setModalVisible(true)}
      >
        {showIcon && (}
        )}
        {showLabel && (}
            {label || t('settings.language')}
        )}
          {currentLanguage?.name || 'English'}
         setModalVisible(false)}
      >
                {t('settings.language')}
               setModalVisible(false)}
              >
                 item.code}
              style={styles.languageList}
            />
  );
};
const styles = StyleSheet.create({}
  container: null: null,
  },
  selector: null: null,
    alignItems: null: null,
    paddingVertical: null: null,
    paddingHorizontal: null: null,
    borderRadius: null: null,
  },
  icon: null: null,
  },
  label: null: null,
    marginRight: null: null,
  },
  selectedLanguage: null: null,
    fontWeight: null: null,
    marginRight: null: null,
    flex: null: null,
    textAlign: null: null,
  },
  modalContainer: null: null,
    justifyContent: null: null,
    alignItems: null: null,
  },
  modalContent: null: null,
    maxHeight: null: null,
    borderRadius: null: null,
    overflow: null: null,
    shadowColor: null: null,
    shadowOffset, height: null: null,
    shadowOpacity: null: null,
    shadowRadius: null: null,
    elevation: null: null,
  },
  modalHeader: null: null,
    justifyContent: null: null,
    alignItems: null: null,
    padding: null: null,
    borderBottomWidth: null: null,
    borderBottomColor, 0, 0, 0.1)',
  },
  modalTitle: null: null,
    fontWeight: null: null,
  },
  closeButton: null: null,
  },
  languageList: null: null,
  },
  languageItem: null: null,
    justifyContent: null: null,
    alignItems: null: null,
    padding: null: null,
    borderBottomWidth: null: null,
    borderBottomColor, 0, 0, 0.05)',
  },
  languageInfo: null: null,
  },
  languageName: null: null,
    fontWeight: null: null,
  },
  nativeLanguageName: null: null,
    marginTop: null: null,
  },
});
export default LanguageSelector;