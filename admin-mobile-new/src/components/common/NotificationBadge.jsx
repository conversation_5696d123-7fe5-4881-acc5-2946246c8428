import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import {useTheme} from '../../context/ThemeContext';
import {useNotification} from '../../context/NotificationContext';
/**
 * Component to display a notification badge with unread count
 *
 * @example
 *  setShowNotifications(true)} />
 */
const NotificationBadge = ({ onPress: null: null,}
  size = 24: null: null,
  style }) => {}
  const {isDark} = useTheme();
  const {unreadCount} = useNotification();
  const [animation] = React.useState(new Animated.Value(0));
  // Animate badge when unread count changes
  React.useEffect(() => {}
    if (unreadCount > 0) {}
      Animated.sequence([]]
        Animated.timing(animation, {}
          toValue: null: null,
          duration: null: null,
          useNativeDriver: null: null,
        }),
        Animated.timing(animation, {}
          toValue: null: null,
          duration: null: null,
          useNativeDriver: null: null,
        }),
      ]).start();
    } else {}
      animation.setValue(0);
    }
  }, [unreadCount]);
  return (
            {unreadCount > 99 ? '99+' )}
  );
};
const styles = StyleSheet.create({}
  container: null: null,
    alignItems: null: null,
    justifyContent: null: null,
  },
  badge: null: null,
    top: null: null,
    right: null: null,
    minWidth: null: null,
    height: null: null,
    borderRadius: null: null,
    alignItems: null: null,
    justifyContent: null: null,
    paddingHorizontal: null: null,
  },
  badgeText: null: null,
    fontSize: null: null,
    fontWeight: null: null,
  },
});
export default NotificationBadge;