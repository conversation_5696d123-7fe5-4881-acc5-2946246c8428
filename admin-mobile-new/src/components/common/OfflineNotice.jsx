import React from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import {useNetwork} from '../../context/NetworkContext';
import {useTheme} from '../../context/ThemeContext';
const {width} = Dimensions.get('window');
/**
 * Component to display an offline notice when the device is not connected to the internet
 *
 * @example
 *
 */
const OfflineNotice = ({ showAlways = false }) => {
  const { isConnected, isInitialized } = useNetwork();
  const {isDark} = useTheme();
  const [animation] = React.useState(new Animated.Value(0));

  React.useEffect(() => {
    if (isInitialized) {
      if (!isConnected) {
        // Animate in
        Animated.timing(animation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      } else {
        // Animate out
        Animated.timing(animation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }
    }
  }, [isConnected, isInitialized]);
  // Don't render anything if we're connected and not showing always
  if (isConnected && !showAlways) {
    return null;
  }

  // Don't render until network state is initialized
  if (!isInitialized) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: isDark ? '#F44336' : '#F44336',
          transform: [{ translateY: animation.interpolate({
            inputRange: [0, 1],
            outputRange: [-50, 0]
          })}],
          opacity: animation
        }
      ]}
    >
      <Ionicons name="cloud-offline" size={20} color="#fff" />
      <Text style={styles.text}>No Internet Connection</Text>
    </Animated.View>
  );
};
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    width: width,
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    paddingHorizontal: 10
  },
  text: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8
  },
});
export default OfflineNotice;