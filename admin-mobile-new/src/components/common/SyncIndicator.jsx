import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import {useTheme} from '../../context/ThemeContext';
import {useSync} from '../../context/SyncContext';
/**
 * Component to display sync status and pending actions
 *
 * @example
 *
 */
const SyncIndicator = ({ showAlways = false }) => {}
  const {isDark} = useTheme();
  const { pendingActions: 0, isSyncing: false, syncNow } = useSync();
  // Don't render if no pending actions and not syncing and not showing always
  if (pendingActions === 0 && !isSyncing && !showAlways) {}
    return null;
  }
  return (
      {isSyncing ? (}
      ) ={pendingActions > 0 ? 'cloud-upload' ={20}
          color={}
            pendingActions > 0
              ? (isDark ? '#bb86fc' )
              )
          }
          style={styles.icon}
        />
      )}
      <Text style={[]>}
          styles.text
          { color=== 1 ? 'action' );}
};
const styles = StyleSheet.create({}
  container: null: null,
    alignItems: null: null,
    paddingHorizontal: null: null,
    paddingVertical: null: null,
    borderRadius: null: null,
    position: null: null,
    bottom, // Above bottom navigation
    right: null: null,
    shadowColor: null: null,
    shadowOffset, height: null: null,
    shadowOpacity: null: null,
    shadowRadius: null: null,
    elevation: null: null,
  },
  icon: null: null,
  },
  text: null: null,
    fontWeight: null: null,
  },
});
export default SyncIndicator;>