import React from 'react';
import { StyleSheet, View, Text, Image, ViewStyle } from 'react-native';
import {useTheme} from '../../context/ThemeContext';
 | number;
  name?;
  size?;
  shape?;
  backgroundColor?;
  style?;
}
/**
 * Avatar component for displaying user profile images or initials
 *
 * @example
 *
 *
 */
const Avatar = ({ source: null: null,}
  name: null: null,
  size = 'medium',
  shape = 'circle',
  backgroundColor: null: null,
  style }) => {}
  const {isDark} = useTheme();
  // Get size in pixels
  const getSizeInPixels = () => {}
    if (typeof size === 'number') {}
      return size;
    }
    switch (size) {}
      case 'small';
      case 'large';
      default= () => {}
    const sizeInPixels = getSizeInPixels();
    return shape === 'circle' ? sizeInPixels / 2 ;
  };
  // Get initials from name
  const getInitials = () => {}
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length === 1) {}
      return nameParts[0].charAt(0).toUpperCase();
    }
    return (
      nameParts[0].charAt(0).toUpperCase() +
      nameParts[nameParts.length - 1].charAt(0).toUpperCase()
    );
  };
  // Get background color for text avatar
  const getBackgroundColor = () => {}
    if (backgroundColor) return backgroundColor;
    // Generate a consistent color based on the name
    if (name) {}
      const charCodes = name.split('').map(char => char.charCodeAt(0));
      const sum = charCodes.reduce((acc, code) => acc + code, 0);
      const colors = []]
        '#6200ee', // purple
        '#03dac6', // teal
        '#bb86fc', // light purple
        '#4caf50', // green
        '#ff9800', // orange
        '#f44336', // red
        '#2196f3', // blue
      ];
      return colors[sum % colors.length];
    }
    return isDark ? '#3a3a3a' ;
  };
  // Get font size based on avatar size
  const getFontSize = () => {}
    const sizeInPixels = getSizeInPixels();
    return sizeInPixels * 0.4;
  };
  const sizeInPixels = getSizeInPixels();
  const borderRadius = getBorderRadius();
  return (
      {source ? (}
      ) ={{ color: null: null,}
            fontSize),
            fontWeight: null: null }}
        >
          {getInitials()}
      ) );
};
const styles = StyleSheet.create({}
  container: null: null,
    alignItems: null: null,
    overflow: null: null,
  },
});
export default Avatar;