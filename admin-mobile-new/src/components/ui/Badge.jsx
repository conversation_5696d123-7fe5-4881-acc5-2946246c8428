import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

/**
 * Badge component for displaying status, counts, or labels
 */
const Badge = ({
  text,
  type = 'primary',
  size = 'medium',
  style,
  textStyle
}) => {
  const { isDark } = useTheme();

  // Get badge background color based on type and theme
  const getBackgroundColor = () => {
    switch (type) {
      case 'primary':
        return '#6200ee';
      case 'success':
        return '#4caf50';
      case 'warning':
        return '#ff9800';
      case 'danger':
        return '#f44336';
      case 'info':
        return '#2196f3';
      default:
        return '#6200ee';
    }
  };

  const getSizeStyle = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: 2,
          paddingHorizontal: 6,
          borderRadius: 8,
        };
      case 'large':
        return {
          paddingVertical: 6,
          paddingHorizontal: 12,
          borderRadius: 12,
        };
      default:
        return {
          paddingVertical: 4,
          paddingHorizontal: 8,
          borderRadius: 10,
        };
    }
  };

  // Get text size based on badge size
  const getTextSizeStyle = () => {
    switch (size) {
      case 'small':
        return { fontSize: 10 };
      case 'large':
        return { fontSize: 14 };
      default:
        return { fontSize: 12 };
    }
  };

  return (
    <View
      style={[
        styles.badge,
        getSizeStyle(),
        { backgroundColor: getBackgroundColor() },
        style
      ]}
    >
      <Text
        style={[
          styles.text,
          getTextSizeStyle(),
          textStyle
        ]}
      >
        {text}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    alignSelf: 'flex-start',
  },
  text: {
    color: '#ffffff',
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default Badge;