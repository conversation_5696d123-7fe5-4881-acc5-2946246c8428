import React from 'react';
import { StyleSheet, TouchableOpacity, Text, ActivityIndicator } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

const Button = ({
  title,
  onPress,
  type = 'primary',
  loading = false,
  disabled = false,
  style,
  textStyle,
  fullWidth = false,
}) => {
  const { isDark } = useTheme();

  const getButtonStyle = () => {
    switch (type) {
      case 'primary':
        return {
          backgroundColor: '#6200ee',
          borderColor: 'transparent'
        };
      case 'secondary':
        return {
          backgroundColor: isDark ? '#292929' : '#e0e0e0',
          borderColor: 'transparent'
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: '#6200ee'
        };
      case 'danger':
        return {
          backgroundColor: isDark ? '#cf6679' : '#b00020',
          borderColor: 'transparent'
        };
      default:
        return {
          backgroundColor: '#6200ee',
          borderColor: 'transparent'
        };
    }
  };

  const getTextStyle = () => {
    switch (type) {
      case 'primary':
        return { color: '#ffffff' };
      case 'secondary':
        return { color: isDark ? '#ffffff' : '#000000' };
      case 'outline':
        return { color: '#6200ee' };
      case 'danger':
        return { color: '#ffffff' };
      default:
        return { color: '#ffffff' };
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        fullWidth && styles.fullWidth,
        disabled && styles.disabled,
        style
      ]}
      onPress={onPress}
      disabled={disabled || loading}
    >
      {loading ? (
        <ActivityIndicator size="small" color={getTextStyle().color} />
      ) : (
        <Text style={[styles.text, getTextStyle(), textStyle]}>
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    minWidth: 100,
  },
  fullWidth: {
    width: '100%',
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
});

export default Button;
