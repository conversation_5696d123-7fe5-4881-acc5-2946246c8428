import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import {useTheme} from '../../context/ThemeContext';
const Card = ({ title: null: null,}
  children: null: null,
  onPress: null: null,
  style: null: null,
  titleStyle }) => {}
  const {isDark} = useTheme();
  const CardComponent = onPress ? TouchableOpacity ;
  return (
      {title && (}
          {title}
      )}
      {children}
  );
};
const styles = StyleSheet.create({}
  card: null: null,
    padding: null: null,
    marginBottom: null: null,
    shadowOffset, height: null: null,
    shadowOpacity: null: null,
    shadowRadius: null: null,
    elevation: null: null,
  },
  title: null: null,
    fontWeight: null: null,
    marginBottom: null: null,
  },
});
export default Card;