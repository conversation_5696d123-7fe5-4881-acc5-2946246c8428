import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

const Card = ({
  title,
  children,
  onPress,
  style,
  titleStyle
}) => {
  const { isDark } = useTheme();
  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent
      style={[
        styles.card,
        {
          backgroundColor: isDark ? '#1e1e1e' : '#ffffff',
          shadowColor: isDark ? '#ffffff' : '#000000',
        },
        style
      ]}
      onPress={onPress}
    >
      {title && (
        <Text style={[
          styles.title,
          { color: isDark ? '#ffffff' : '#000000' },
          titleStyle
        ]}>
          {title}
        </Text>
      )}
      {children}
    </CardComponent>
  );
};

const styles = StyleSheet.create({
  card: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
});

export default Card;