import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

/**
 * Divider component for separating content
 */
const Divider = ({
  text,
  orientation = 'horizontal',
  thickness = 1,
  style,
  textStyle
}) => {
  const { isDark } = useTheme();

  // Get divider color based on theme
  const getDividerColor = () => {
    return isDark ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)';
  };

  // Render horizontal divider
  if (orientation === 'horizontal') {
    if (text) {
      return (
        <View style={[styles.horizontalWithText, style]}>
          <View style={[styles.line, { backgroundColor: getDividerColor(), height: thickness }]} />
          <Text style={[
            styles.text,
            { color: isDark ? '#bbbbbb' : '#666666' },
            textStyle
          ]}>
            {text}
          </Text>
          <View style={[styles.line, { backgroundColor: getDividerColor(), height: thickness }]} />
        </View>
      );
    }

    return (
      <View style={[
        styles.horizontal,
        { backgroundColor: getDividerColor(), height: thickness },
        style
      ]} />
    );
  }

  // Render vertical divider
  return (
    <View style={[
      styles.vertical,
      { backgroundColor: getDividerColor(), width: thickness },
      style
    ]} />
  );
};

const styles = StyleSheet.create({
  horizontal: {
    width: '100%',
    marginVertical: 8,
  },
  vertical: {
    height: '100%',
    marginHorizontal: 8,
  },
  horizontalWithText: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginVertical: 16,
  },
  line: {
    flex: 1,
  },
  text: {
    paddingHorizontal: 16,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default Divider;