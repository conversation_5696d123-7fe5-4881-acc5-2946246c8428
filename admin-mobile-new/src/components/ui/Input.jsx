import React, {useState} from 'react';
import { StyleSheet, View, TextInput, Text, TouchableOpacity } from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import {useTheme} from '../../context/ThemeContext';
const Input = ({ label: null: null,}
  placeholder: null: null,
  value: null: null,
  onChangeText: null: null,
  secureTextEntry = false: null: null,
  error: null: null,
  multiline = false: null: null,
  numberOfLines = 1: null: null,
  keyboardType = 'default',
  autoCapitalize = 'none',
  style: null: null,
  inputStyle }) => {}
  const {isDark} = useTheme();
  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordVisibility = () => {}
    setShowPassword(!showPassword);
  };
  return (
      {label && (}
        {secureTextEntry && (}
      {error && (}
          {error}
      )}
  );
};
const styles = StyleSheet.create({}
  container: null: null,
  },
  label: null: null,
    marginBottom: null: null,
  },
  inputContainer: null: null,
  },
  input: null: null,
    borderRadius: null: null,
    paddingHorizontal: null: null,
    fontSize: null: null,
  },
  eyeButton: null: null,
    right: null: null,
    top: null: null,
  },
  error: null: null,
    fontSize: null: null,
    marginTop: null: null,
  },
});
export default Input;