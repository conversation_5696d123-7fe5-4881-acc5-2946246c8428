import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity
} from 'react-native';
import {useTheme} from '../../context/ThemeContext';
/**
 * ListItem component for displaying items in a list
 *
 * @example
 * <ListItem
 *   title="Item Title"
 *   subtitle="Item Subtitle"
 *   onPress={() => console.log('Pressed')}
 * />
 */
export const ListItem = ({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onPress,
  style,
  titleStyle,
  subtitleStyle,
  showDivider = true
}) => {
  const {isDark} = useTheme();
  const Container = onPress ? TouchableOpacity : View;

  return (
    <View style={[styles.item, style]}>
      <Container
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 16
        }}
        onPress={onPress}
      >
        {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}

        <View style={styles.content}>
          <Text style={[
            styles.title,
            { color: isDark ? '#ffffff' : '#000000' },
            titleStyle
          ]}>
            {title}
          </Text>

          {subtitle && (
            <Text style={[
              styles.subtitle,
              { color: isDark ? '#bbbbbb' : '#666666' },
              subtitleStyle
            ]}>
              {subtitle}
            </Text>
          )}
        </View>

        {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}
      </Container>

      {showDivider && (
        <View style={[
          styles.divider,
          { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }
        ]} />
      )}
    </View>
  );
};
/**
 * List component for displaying a list of items
 *
 * @example
 * <List>
 *   <ListItem title="Item 1" />
 *   <ListItem title="Item 2" />
 * </List>
 */
const List = ({ children, style }) => {
  const {isDark} = useTheme();

  return (
    <View style={[
      styles.list,
      { backgroundColor: isDark ? '#1e1e1e' : '#ffffff' },
      style
    ]}>
      {children}
    </View>
  );
};
const styles = StyleSheet.create({
  list: {
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 16
  },
  item: {
    width: '100%'
  },
  leftIcon: {
    marginRight: 16
  },
  content: {
    flex: 1
  },
  title: {
    fontSize: 16,
    fontWeight: '500'
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4
  },
  rightIcon: {
    marginLeft: 16
  },
  divider: {
    height: 1,
    width: '100%',
    marginLeft: 16
  }
});
export default List;