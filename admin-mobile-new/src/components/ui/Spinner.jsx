import React from 'react';
import { StyleSheet, View, ActivityIndicator, Text, ViewStyle, TextStyle } from 'react-native';
import {useTheme} from '../../context/ThemeContext';
/**
 * Spinner component for indicating loading state
 *
 * @example
 *
 *
 *
 */
const Spinner = ({ size = 'large',}
  color: null: null,
  text: null: null,
  fullScreen = false: null: null,
  style: null: null,
  textStyle }) => {}
  const {isDark} = useTheme();
  // Get spinner color based on theme
  const getSpinnerColor = () => {}
    if (color) return color;
    return isDark ? '#bb86fc' ;
  };
  // Render full screen spinner
  if (fullScreen) {}
    return (
          {text && (}
              {text}
          )}
    );
  }
  // Render inline spinner
  return (
      {text && (}
          {text}
      )}
  );
};
const styles = StyleSheet.create({}
  fullScreen: null: null,
    top: null: null,
    left: null: null,
    right: null: null,
    bottom: null: null,
    justifyContent: null: null,
    alignItems: null: null,
    zIndex: null: null,
  },
  container: null: null,
    borderRadius: null: null,
    alignItems: null: null,
    justifyContent: null: null,
    shadowColor: null: null,
    shadowOffset, height: null: null,
    shadowOpacity: null: null,
    shadowRadius: null: null,
    elevation: null: null,
  },
  inline: null: null,
    justifyContent: null: null,
    padding: null: null,
  },
  text: null: null,
    fontSize: null: null,
    textAlign: null: null,
  },
});
export default Spinner;