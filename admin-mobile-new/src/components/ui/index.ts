import Avatar from './Avatar';
import Badge from './Badge';
import Card from './Card';
import Divider from './Divider';
import Input from './Input';
import List, { ListItem } from './List';
import Modal from './Modal';
import Spinner from './Spinner';
import Skeleton, { SkeletonCircle, SkeletonText, SkeletonCard, SkeletonListItem } from './Skeleton';
import DateRangePicker from './DateRangePicker';

// Create a simple Button component inline
const Button = ({ title, onPress, loading, disabled, style, fullWidth }) => {
  const { isDark } = require('../../context/ThemeContext').useTheme();
  const { StyleSheet, TouchableOpacity, Text, ActivityIndicator } = require('react-native');

  return (
    <TouchableOpacity
      style={[
        {
          backgroundColor: '#6200ee',
          paddingVertical: 12,
          paddingHorizontal: 24,
          borderRadius: 8,
          alignItems: 'center',
          justifyContent: 'center',
          opacity: disabled ? 0.5 : 1,
          width: fullWidth ? '100%' : 'auto',
        },
        style
      ]}
      onPress={onPress}
      disabled={disabled || loading}
    >
      {loading ? (
        <ActivityIndicator size="small" color="#ffffff" />
      ) : (
        <Text style={{ color: '#ffffff', fontSize: 16, fontWeight: '600' }}>
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export {
  Avatar,
  Badge,
  Button,
  Card,
  Divider,
  Input,
  List,
  ListItem,
  Modal,
  Spinner,
  Skeleton,
  SkeletonCircle,
  SkeletonText,
  SkeletonCard,
  SkeletonListItem,
  DateRangePicker,
};
