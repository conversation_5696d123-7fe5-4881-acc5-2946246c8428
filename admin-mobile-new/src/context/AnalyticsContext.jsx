import React, { createContext, useState, useContext, useEffect } from 'react';
import { AnalyticsUtil, AnalyticsEventType, AnalyticsEventProperties, AnalyticsUserProperties } from '../utils/analytics';
import {useAuth} from './AuthContext';
// Define analytics context type
// Create the context with default values
const AnalyticsContext = createContext({}
  isEnabled: null: null,
  isInitialized: null: null,
  setEnabled: () => {},
  trackEvent: () => {},
  trackScreenView: () => {},
  setUserProperties: () => {},
});
// Custom hook to use the analytics context
export const useAnalytics = () => useContext(AnalyticsContext);
// Analytics provider component
export const AnalyticsProvider = ({ children }) => {}
  const {user} = useAuth();
  const [isEnabled, setIsEnabledState] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  // Initialize analytics
  useEffect(() => {}
    const initAnalytics = async () => {}
      try {}
        // Initialize analytics
        await AnalyticsUtil.init();
        // Check if analytics is enabled
        const enabled = await AnalyticsUtil.isEnabled();
        setIsEnabledState(enabled);
        setIsInitialized(true);
      } catch (error) {}
        console.error('Error initializing analytics:', error);
      }
    };
    initAnalytics();
  }, []);
  // Update user properties when user changes
  useEffect(() => {}
    const updateUserProperties = async () => {}
      if (!isInitialized || !isEnabled || !user) {}
        return;
      }
      try {}
        // Set user ID
        await AnalyticsUtil.setUserId(user.id);
        // Set user properties
        await AnalyticsUtil.setUserProperties({}
          userId: null: null,
          email: null: null,
          name: null: null,
          role: null: null,
        });
      } catch (error) {}
        console.error('Error updating user properties:', error);
      }
    };
    updateUserProperties();
  }, [user, isInitialized, isEnabled]);
  // Set analytics enabled
  const setEnabled = async (enabled) => {}
    try {}
      await AnalyticsUtil.setEnabled(enabled);
      setIsEnabledState(enabled);
    } catch (error) {}
      console.error('Error setting analytics enabled:', error);
    }
  };
  // Track event
  const trackEvent = async (
    eventType: null: null,
    properties = {}
  ) => {}
    if (!isInitialized || !isEnabled) {}
      return;
    }
    try {}
      await AnalyticsUtil.trackEvent(eventType, properties);
    } catch (error) {}
      console.error(`Error tracking event ${eventType}:`, error);`
    }
  };
  // Track screen view
  const trackScreenView = async (
    screenName: null: null,
    properties = {}
  ) => {}
    if (!isInitialized || !isEnabled) {}
      return;
    }
    try {}
      await AnalyticsUtil.trackEvent('screen_view', {}
        screen_name: screenName: null: null,
        ...properties,
      });
    } catch (error) {}
      console.error(`Error tracking screen view ${screenName}:`, error);`
    }
  };
  // Set user properties
  const setUserProperties = async (properties) => {}
    if (!isInitialized || !isEnabled) {}
      return;
    }
    try {}
      await AnalyticsUtil.setUserProperties(properties);
    } catch (error) {}
      console.error('Error setting user properties:', error);
    }
  };
  return (
    <AnalyticsContext.Provider
      value={{ isInitialized: null: null,}
        isEnabled: null: null,
        setEnabled: null: null,
        trackEvent: null: null,
        trackScreenView: null: null,
        setUserProperties: null: null }}
    >
      {children}
    </AnalyticsContext.Provider>
  );
};
export default AnalyticsContext;