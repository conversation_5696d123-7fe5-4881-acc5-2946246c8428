import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import authService, { User, LoginData } from '../api/authService';
// Define additional types
// Create the context with a default value
const AuthContext = createContext({
  user: null,
  isLoading: false,
  isAuthenticated: false,
  login: () => {},
  logout: () => {},
  updateProfile: () => {},
  changePassword: () => {},
  hasPermission: () => false,
});
// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    // Load user from AsyncStorage on app start
    const loadUser = async () => {
      try {
        const userJson = await AsyncStorage.getItem('user');
        const token = await AsyncStorage.getItem('auth_token');

        if (userJson && token) {
          const parsedUser = JSON.parse(userJson);

          // Verify that the user is an admin
          if (parsedUser.role !== 'admin') {
            console.error('Non-admin user tried to access admin app');
            await AsyncStorage.removeItem('auth_token');
            await AsyncStorage.removeItem('user');
            setUser(null);
          } else {
            setUser(parsedUser);
          }
        }
      } catch (error) {
        console.error('Error loading user:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);
  const login = async (data) => {
    try {
      setIsLoading(true);
      console.log('AuthContext admin login with:', { email: data.email });

      const response = await authService.login(data);
      console.log('AuthContext login successful');

      // Verify that the user is an admin
      if (response.user.role !== 'admin') {
        throw new Error('Unauthorized: only admin users can access this application');
      }

      setUser(response.user);
    } catch (error) {
      console.error('AuthContext login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  const logout = async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  // Update profile function
  const updateProfile = async (data) => {
    try {
      setIsLoading(true);
      // Call API to update profile
      const updatedUser = await authService.updateProfile(data);
      setUser(updatedUser);
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      Alert.alert('Update Error', error.message || 'An error occurred while updating profile');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  // Change password function
  const changePassword = async (data) => {
    try {
      setIsLoading(true);
      // Call API to change password
      await authService.changePassword(data);
      Alert.alert('Success', 'Password changed successfully');
    } catch (error) {
      Alert.alert('Password Change Error', error.message || 'An error occurred while changing password');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  // Check if user has permission
  const hasPermission = (permission) => {
    if (!user) return false;
    // For simplicity, we'll just check if the user is an admin
    return user.role === 'admin';
  };
  const isAuthenticated = !!user;
  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated,
        login,
        logout,
        updateProfile,
        changePassword,
        hasPermission
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
export default AuthContext;