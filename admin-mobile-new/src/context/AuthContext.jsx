import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authService, User, LoginCredentials } from '../services/api';
import {Alert} from 'react-native';
// Define additional types
// Create the context with a default value
const AuthContext = createContext({
  user: null,
  isLoading: false,
  isAuthenticated: false,
  login: () => {},
  logout: () => {},
  updateProfile: () => {},
  changePassword: () => {},
  hasPermission: () => false,
});
// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  // Check if user is already logged in
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get user from API or storage
        const userData = await authService.getCurrentUser();
        if (userData) {
          setUser(userData);
        }
      } catch (error) {
        console.error('Error checking auth:', error);
      } finally {
        setIsLoading(false);
      }
    };
    checkAuth();
  }, []);
  // Login function
  const login = async (data) => {
    try {
      setIsLoading(true);
      // Call API to login
      const userData = await authService.login(data);
      // Check if user is admin
      if (userData.role !== 'admin') {
        throw new Error('Only admin users can access this app');
      }
      setUser(userData);
    } catch (error) {
      // Show error message
      Alert.alert('Login Error', error.message || 'An error occurred during login');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);
      // Call API to logout
      await authService.logout();
      setUser(null);
    } catch (error) {
      console.error('Error logging out:', error);
      Alert.alert('Logout Error', error.message || 'An error occurred during logout');
    } finally {
      setIsLoading(false);
    }
  };
  // Update profile function
  const updateProfile = async (data) => {
    try {
      setIsLoading(true);
      // Call API to update profile
      const updatedUser = await authService.updateProfile(data);
      setUser(updatedUser);
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      Alert.alert('Update Error', error.message || 'An error occurred while updating profile');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  // Change password function
  const changePassword = async (data) => {
    try {
      setIsLoading(true);
      // Call API to change password
      await authService.changePassword(data);
      Alert.alert('Success', 'Password changed successfully');
    } catch (error) {
      Alert.alert('Password Change Error', error.message || 'An error occurred while changing password');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  // Check if user has permission
  const hasPermission = (permission) => {
    if (!user) return false;
    // For simplicity, we'll just check if the user is an admin
    return user.role === 'admin';
  };
  const isAuthenticated = !!user;
  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated,
        login,
        logout,
        updateProfile,
        changePassword,
        hasPermission
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
export default AuthContext;