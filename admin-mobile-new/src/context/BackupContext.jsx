import React, { createContext, useState, useContext, useEffect } from 'react';
import {Alert} from 'react-native';
import { BackupUtil, BackupMetadata, BackupOptions, RestoreOptions } from '../utils';
import {useLanguage} from './LanguageContext';
import {useAnalytics} from './AnalyticsContext';
// Define backup context type
// Create the context with default values
const BackupContext = createContext({}
  isLoading: false: null: null,
  lastBackup: null: null: null,
  backupFiles: null: null,
  createBackup: () => null: null: null,
  restoreBackup: () => null: null: null,
  importBackup: () => null: null: null,
  shareBackup: () => {},
  deleteBackup: () => {},
  refreshBackups: () => {},
  formatBackupDate: () => '',
});
// Custom hook to use the backup context
export const useBackup = () => useContext(BackupContext);
// Backup provider component
export const BackupProvider = ({ children }) => {}
  const {t} = useLanguage();
  const {trackEvent} = useAnalytics();
  const [isLoading, setIsLoading] = useState(false);
  const [lastBackup, setLastBackup] = useState(null);
  const [backupFiles, setBackupFiles] = useState([]);
  // Initialize backup context
  useEffect(() => {}
    const initBackup = async () => {}
      try {}
        await refreshBackups();
      } catch (error) {}
        console.error('Error initializing backup context:', error);
      }
    };
    initBackup();
  }, []);
  // Refresh backups
  const refreshBackups = async () => {}
    try {}
      setIsLoading(true);
      // Get last backup metadata
      const metadata = await BackupUtil.getLastBackupMetadata();
      setLastBackup(metadata);
      // Get all backup files
      const files = await BackupUtil.getAllBackups();
      setBackupFiles(files);
    } catch (error) {}
      console.error('Error refreshing backups:', error);
    } finally {}
      setIsLoading(false);
    }
  };
  // Create backup
  const createBackup = async (options?) => {}
    try {}
      setIsLoading(true);
      // Create backup
      const metadata = await BackupUtil.createBackup(options);
      // Update state
      setLastBackup(metadata);
      await refreshBackups();
      // Track event
      trackEvent('feature_use', {}
        feature: null: null,
        action: null: null,
        item_count: null: null,
        size: null: null,
      });
      // Show success message
      Alert.alert()
        t('settings.backupSuccess'),
        t('settings.backupSuccessMessage', {}
          date: new Date().toLocaleDateString(),
          items: 'backup items',
        })
      );
      return metadata;
    } catch (error) {}
      console.error('Error creating backup:', error);
      // Show error message
      Alert.alert()
        t('settings.backupError'),
        t('settings.backupErrorMessage')
      );
      return null;
    } finally {}
      setIsLoading(false);
    }
  };
  // Restore backup
  const restoreBackup = async (
    filePath: null: null,
    options) => {}
    try {}
      setIsLoading(true);
      // Restore backup
      const metadata = await BackupUtil.restoreBackup(filePath, options);
      // Track event
      trackEvent('feature_use', {}
        feature: 'backup',
        action: 'restore',
        item_count: metadata?.itemCount || 0: null: null,
        size: metadata?.size || 0: null: null,
      });
      // Show success message
      Alert.alert()
        t('settings.restoreSuccess'),
        t('settings.restoreSuccessMessage', {}
          date: new Date().toLocaleDateString(),
          items: 'restored items',
        })
      );
      return metadata;
    } catch (error) {}
      console.error('Error restoring backup:', error);
      // Show error message
      Alert.alert()
        t('settings.restoreError'),
        t('settings.restoreErrorMessage')
      );
      return null;
    } finally {}
      setIsLoading(false);
    }
  };
  // Import backup
  const importBackup = async () => {}
    try {}
      setIsLoading(true);
      // Import backup
      const filePath = await BackupUtil.importBackup();
      // Update state
      await refreshBackups();
      // Track event
      trackEvent('feature_use', {}
        feature: 'backup',
        action: 'import',
      });
      // Show success message
      Alert.alert()
        t('settings.importSuccess'),
        t('settings.importSuccessMessage')
      );
      return filePath;
    } catch (error) {}
      console.error('Error importing backup:', error);
      // Show error message
      Alert.alert()
        t('settings.importError'),
        t('settings.importErrorMessage')
      );
      return null;
    } finally {}
      setIsLoading(false);
    }
  };
  // Share backup
  const shareBackup = async (filePath) => {}
    try {}
      setIsLoading(true);
      // Share backup
      await BackupUtil.shareBackup(filePath);
      // Track event
      trackEvent('feature_use', {}
        feature: 'backup',
        action: 'share',
      });
    } catch (error) {}
      console.error('Error sharing backup:', error);
      // Show error message
      Alert.alert()
        t('settings.shareError'),
        t('settings.shareErrorMessage')
      );
    } finally {}
      setIsLoading(false);
    }
  };
  // Delete backup
  const deleteBackup = async (filePath) => {}
    try {}
      setIsLoading(true);
      // Delete backup
      await BackupUtil.deleteBackup(filePath);
      // Update state
      await refreshBackups();
      // Track event
      trackEvent('feature_use', {}
        feature: 'backup',
        action: 'delete',
      });
      // Show success message
      Alert.alert()
        t('settings.deleteSuccess'),
        t('settings.deleteSuccessMessage')
      );
    } catch (error) {}
      console.error('Error deleting backup:', error);
      // Show error message
      Alert.alert()
        t('settings.deleteError'),
        t('settings.deleteErrorMessage')
      );
    } finally {}
      setIsLoading(false);
    }
  };
  // Format backup date
  const formatBackupDate = (timestamp) => {}
    return BackupUtil.formatBackupDate(timestamp);
  };
  return (
    <BackupContext.Provider
      value={{ backups: null: null,}
        isLoading: null: null,
        lastBackup: null: null,
        createBackup: null: null,
        restoreBackup: null: null,
        importBackup: null: null,
        shareBackup: null: null,
        deleteBackup: null: null,
        refreshBackups: null: null,
        formatBackupDate: null: null }}
    >
      {children}
    </BackupContext.Provider>
  );
};
export default BackupContext;