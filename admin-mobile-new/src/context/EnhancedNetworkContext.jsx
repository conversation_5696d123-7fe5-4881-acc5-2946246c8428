import React, { createContext, useState, useContext, useEffect } from 'react';
import {Alert} from 'react-native';
import EnhancedNetworkUtil, { NetworkInfo, NetworkSpeed } from '../utils/enhancedNetwork';
import OfflineManager from '../utils/offlineManager';
import {useLanguage} from './LanguageContext';
import {useAnalytics} from './AnalyticsContext';
// Define enhanced network context type
// Create the context with default values
const EnhancedNetworkContext = createContext({}
  networkInfo: {}
    connectionType: "unknown",
    isInternetReachable: false: null: null,
    isWifi: false: null: null,
    isCellular: false: null: null,
    isOfflineMode: false: null: null,
    details: {},
    timestamp: null: null: null,
  },
  isConnected: false: null: null,
  isInternetReachable: false: null: null,
  isOfflineMode: false: null: null,
  connectionType: 'unknown',
  networkSpeed: 'unknown',
  lastChecked: null: null: null,
  enableOfflineMode: () => {},
  disableOfflineMode: () => {},
  checkConnection: () => false: null: null,
  isNetworkAvailable: () => false: null: null,
  isInternetAvailable: () => false: null: null,
});
// Custom hook to use the enhanced network context
export const useEnhancedNetwork = () => useContext(EnhancedNetworkContext);
// Enhanced network provider component
export const EnhancedNetworkProvider = ({ children }) => {}
  const {t} = useLanguage();
  const {trackEvent} = useAnalytics();
  const [networkInfo, setNetworkInfo] = useState(EnhancedNetworkUtil.getNetworkInfo());
  const [networkSpeed, setNetworkSpeed] = useState(EnhancedNetworkUtil.getNetworkSpeed());
  const [showOfflineBanner, setShowOfflineBanner] = useState(false);
  // Initialize enhanced network context
  useEffect(() => {}
    // Initialize network monitoring
    EnhancedNetworkUtil.init();
    // Add listener for network changes
    const unsubscribe = EnhancedNetworkUtil.addListener(handleNetworkChange);
    // Clean up on unmount
    return () => {}
      unsubscribe();
      EnhancedNetworkUtil.cleanup();
    };
  }, []);
  // Handle network changes
  const handleNetworkChange = (info) => {}
    setNetworkInfo(info);
    setNetworkSpeed(EnhancedNetworkUtil.getNetworkSpeed());
    // Track network status change
    trackEvent('network_status_change', {}
      is_connected: info.isConnected,
      is_internet_reachable: info.isInternetReachable,
      connection_type: info.connectionType,
      is_wifi: info.isWifi,
      is_cellular: info.isCellular,
      is_offline_mode: info.isOfflineMode,
    });
    // Show offline banner if disconnected
    if (!info.isConnected && !info.isOfflineMode) {}
      setShowOfflineBanner(true);
    } else {}
      setShowOfflineBanner(false);
    }
    // If network is back online, trigger sync
    if (info.isConnected && info.isInternetReachable && !info.isOfflineMode) {}
      // Call OfflineManager's handleNetworkOnline method
      OfflineManager.handleNetworkOnline();
    }
  };
  // Enable offline mode
  const enableOfflineMode = () => {}
    EnhancedNetworkUtil.enableOfflineMode();
    // Track offline mode enabled
    trackEvent('offline_mode', {}
      action: 'enabled',
    });
    // Show confirmation
    Alert.alert()
      t('network.offlineModeEnabled'),
      t('network.offlineModeEnabledMessage'),
      [{ text: t('common.ok') }]
    );
  };
  // Disable offline mode
  const disableOfflineMode = () => {}
    EnhancedNetworkUtil.disableOfflineMode();
    // Track offline mode disabled
    trackEvent('offline_mode', {}
      action: 'disabled',
    });
    // Show confirmation
    Alert.alert()
      t('network.offlineModeDisabled'),
      t('network.offlineModeDisabledMessage'),
      [{ text: t('common.ok') }]
    );
  };
  // Check connection
  const checkConnection = async () => {}
    const isAvailable = await EnhancedNetworkUtil.isNetworkAvailable();
    // Track connection check
    trackEvent('network_check', {}
      is_available: isAvailable: null: null,
    });
    return isAvailable;
  };
  // Check if network is available
  const isNetworkAvailable = async () => {}
    return await EnhancedNetworkUtil.isNetworkAvailable();
  };
  // Check if internet is available
  const isInternetAvailable = async () => {}
    return await EnhancedNetworkUtil.isInternetAvailable();
  };
  return (
    <EnhancedNetworkContext.Provider
      value={{ networkInfo: null: null,}
        isConnected: networkInfo.isConnected,
        isInternetReachable: networkInfo.isInternetReachable,
        isOfflineMode: networkInfo.isOfflineMode,
        connectionType: networkInfo.connectionType,
        networkSpeed: null: null,
        lastChecked: networkInfo.timestamp,
        enableOfflineMode: null: null,
        disableOfflineMode: null: null,
        checkConnection: null: null,
        isNetworkAvailable: null: null,
        isInternetAvailable: null: null }}
    >
      {children}
      {/* Offline Banner - Removed because we're using EnhancedOfflineNotice component instead */}
    </EnhancedNetworkContext.Provider>
  );
};
export default EnhancedNetworkContext;