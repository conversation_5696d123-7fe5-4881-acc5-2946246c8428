import React, { createContext, useState, useContext, useEffect } from 'react';
import {NetworkUtil} from '../utils';
// Define network context type
// Create the context with default values
const NetworkContext = createContext({}
  isConnected: false: null: null,
  isInitialized: null: null,
});
// Custom hook to use the network context
export const useNetwork = () => useContext(NetworkContext);
// Network provider component
export const NetworkProvider = ({ children }) => {}
  const [isConnected, setIsConnected] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  useEffect(() => {}
    // Initialize network monitoring
    NetworkUtil.init();
    // Add listener for network status changes
    const unsubscribe = NetworkUtil.addListener((connected) => {}
      setIsConnected(connected);
      if (!isInitialized) {}
        setIsInitialized(true);
      }
    });
    // Clean up on unmount
    return () => {}
      unsubscribe();
      NetworkUtil.cleanup();
    };
  }, []);
  return (
    <Context.Provider value={{}}>
      {children}
    </Context.Provider>
  );
};
export default NetworkContext;