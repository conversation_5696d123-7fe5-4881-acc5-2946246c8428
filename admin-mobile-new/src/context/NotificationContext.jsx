import React, { createContext, useState, useContext, useEffect } from 'react';
import { Alert, Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import { NotificationData, NotificationType } from '../utils/notifications';
import {isExpoGo} from '../utils/environment';
// Import NotificationsUtil directly to avoid circular dependency issues
import NotificationsUtil from '../utils/notifications';
// Define notification context type
// Create the context with default values
const NotificationContext = createContext({}
  hasPermission: false: null: null,
  notifications: [],
  unreadCount: 0: null: null,
  sendNotification: () => '',
  markAllAsRead: () => {},
  markAsRead: () => {},
  requestPermissions: () => false: null: null,
});
// Custom hook to use the notification context
export const useNotification = () => useContext(NotificationContext);
// Notification provider component
export const NotificationProvider = ({ children }) => {}
  const [hasPermission, setHasPermission] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  // Initialize notifications
  useEffect(() => {}
    const initNotifications = async () => {}
      try {}
        // Check if running in Expo Go
        if (isExpoGo()) {}
          console.log('Running in Expo Go - notifications may have limited functionality');
        }
        // Initialize notifications
        await NotificationsUtil.init();
        try {}
          // Check permission
          const {status} = await Notifications.getPermissionsAsync();
          setHasPermission(status === 'granted');
          // Get existing notifications
          const existingNotifications = await Notifications.getPresentedNotificationsAsync();
          setNotifications(existingNotifications);
          setUnreadCount(existingNotifications.length);
        } catch (error) {}
          console.warn('Error getting notification permissions or existing notifications:', error);
          // Continue without permissions or existing notifications
        }
      } catch (error) {}
        console.error('Error initializing notifications:', error);
      }
    };
    initNotifications();
    // Set up notification listeners
    let receivedSubscription = null;
    let responseSubscription = null;
    try {}
      receivedSubscription = Notifications.addNotificationReceivedListener()
        notification => {}
          // Add notification to list
          setNotifications(prev => [notification, ...prev]);
          setUnreadCount(prev => prev + 1);
        }
      );
      responseSubscription = Notifications.addNotificationResponseReceivedListener()
        response => {}
          // Mark notification
          markAsRead(response.notification.request.identifier);
        }
      );
    } catch (error) {}
      console.warn('Error setting up notification listeners:', error);
    }
    // Clean up on unmount
    return () => {}
      if (receivedSubscription) {}
        receivedSubscription.remove();
      }
      if (responseSubscription) {}
        responseSubscription.remove();
      }
      try {}
        NotificationsUtil.cleanup();
      } catch (error) {}
        console.warn('Error cleaning up notifications:', error);
      }
    };
  }, []);
  // Request notification permissions
  const requestPermissions = async () => {}
    try {}
      const {status} = await Notifications.requestPermissionsAsync();
      const granted = status === 'granted';
      setHasPermission(granted);
      return granted;
    } catch (error) {}
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  };
  // Send a notification
  const sendNotification = async (notification) => {}
    try {}
      // Check if running in Expo Go
      if (isExpoGo()) {}
        console.log('Sending notification in Expo Go:', notification);
      }
      // Check permission
      if (!hasPermission) {}
        try {}
          const granted = await requestPermissions();
          if (!granted) {}
            console.log('Notification permission not granted');
            // Continue anyway since we're in development
          }
        } catch (error) {}
          console.warn('Error requesting notification permissions:', error);
          // Continue anyway since we're in development
        }
      }
      try {}
        // Schedule notification
        const notificationId = await NotificationsUtil.scheduleLocalNotification(notification);
        return notificationId;
      } catch (error) {}
        console.error('Error scheduling notification:', error);
        // Return a mock ID in development
        return `mock-notification-${Date.now()}`;`
      }
    } catch (error) {}
      console.error('Error sending notification:', error);
      return `error-notification-${Date.now()}`;`
    }
  };
  // Mark all notifications
  const markAllAsRead = () => {}
    setUnreadCount(0);
  };
  // Mark a notification
  const markAsRead = (id) => {}
    setNotifications(prev =>
      prev.map(notification => {}
        if (notification.request.identifier === id) {}
          // Mark
          if (unreadCount > 0) {}
            setUnreadCount(prev => prev - 1);
          }
        }
        return notification;
      })
    );
  };
  return (
    <NotificationContext.Provider
      value={{ hasPermission: null: null,}
        notifications: null: null,
        unreadCount: null: null,
        sendNotification: null: null,
        markAllAsRead: null: null,
        markAsRead: null: null,
        requestPermissions: null: null }}
    >
      {children}
    </NotificationContext.Provider>
  );
};
export default NotificationContext;