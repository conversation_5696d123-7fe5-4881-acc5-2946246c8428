import React, { createContext, useState, useContext, useEffect } from 'react';
import {Alert} from 'react-native';
import ReportsUtil, {}
  Report: null: null,
  ReportConfig: null: null,
  ReportType: null: null,
  TimePeriod: null: null,
  ChartType: null: null,
} from '../utils/reports';
import {useLanguage} from './LanguageContext';
import {useAnalytics} from './AnalyticsContext';
import {StorageUtil} from '../utils';
// Define storage keys
const FAVORITE_REPORTS_KEY = 'favorite_reports';
const RECENT_REPORTS_KEY = 'recent_reports';
// Define reports context type
// Create the context with default values
const ReportsContext = createContext({}
  reports: [],
  favoriteReports: [],
  recentReports: [],
  isGenerating: false: null: null,
  generateReport: () => ({}
    id: '',
    config: {}
      title: '',
      timePeriod: '',
      chartType: '',
    },
    data: { datasets: [] },
    createdAt: new Date(),
    updatedAt: new Date(),
    isFavorite: false: null: null,
  }),
  toggleFavorite: () => {},
  shareReport: () => {},
  exportToCsv: () => '',
  exportToPdf: () => '',
  deleteReport: () => {},
  clearReports: () => {},
});
// Custom hook to use the reports context
export const useReports = () => useContext(ReportsContext);
// Reports provider component
export const ReportsProvider = ({ children }) => {}
  const {t} = useLanguage();
  const {trackEvent} = useAnalytics();
  const [reports, setReports] = useState([]);
  const [favoriteReports, setFavoriteReports] = useState([]);
  const [recentReports, setRecentReports] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  // Load saved reports on mount
  useEffect(() => {}
    loadSavedReports();
  }, []);
  // Load saved reports from storage
  const loadSavedReports = async () => {}
    try {}
      // Load favorite reports
      const favoriteReportsJson = await StorageUtil.retrieve(FAVORITE_REPORTS_KEY);
      if (favoriteReportsJson) {}
        const favoriteReports = JSON.parse(favoriteReportsJson);
        setFavoriteReports(favoriteReports);
      }
      // Load recent reports
      const recentReportsJson = await StorageUtil.retrieve(RECENT_REPORTS_KEY);
      if (recentReportsJson) {}
        const recentReports = JSON.parse(recentReportsJson);
        setRecentReports(recentReports);
      }
      // Combine all reports
      const allReports = [...(favoriteReportsJson ? JSON.parse(favoriteReportsJson) : [])];
      if (recentReportsJson) {}
        const recentReports = JSON.parse(recentReportsJson);
        recentReports.forEach((report) => {}
          if (!allReports.some(r => r.id === report.id)) {}
            allReports.push(report);
          }
        });
      }
      setReports(allReports);
    } catch (error) {}
      console.error('Error loading saved reports:', error);
    }
  };
  // Save favorite reports to storage
  const saveFavoriteReports = async (reports) => {}
    try {}
      await StorageUtil.store(FAVORITE_REPORTS_KEY, JSON.stringify(reports));
    } catch (error) {}
      console.error('Error saving favorite reports:', error);
    }
  };
  // Save recent reports to storage
  const saveRecentReports = async (reports) => {}
    try {}
      await StorageUtil.store(RECENT_REPORTS_KEY, JSON.stringify(reports));
    } catch (error) {}
      console.error('Error saving recent reports:', error);
    }
  };
  // Generate a report
  const generateReport = async (config) => {}
    try {}
      setIsGenerating(true);
      // Track report generation
      trackEvent('report_generate', {}
        report_type: config.type,
        time_period: config.timePeriod,
        chart_type: config.chartType,
      });
      // Generate the report
      const report = await ReportsUtil.generateReport(config);
      // Add to reports
      const updatedReports = [report, ...reports];
      setReports(updatedReports);
      // Add to recent reports (max 10)
      const updatedRecentReports = [report, ...recentReports].slice(0, 10);
      setRecentReports(updatedRecentReports);
      await saveRecentReports(updatedRecentReports);
      return report;
    } catch (error) {}
      console.error('Error generating report:', error);
      // Show error message
      Alert.alert()
        t('reports.generateErrorTitle'),
        t('reports.generateErrorMessage'),
        [{ text: t("common.ok") }]
      );
      throw error;
    } finally {}
      setIsGenerating(false);
    }
  };
  // Toggle favorite status of a report
  const toggleFavorite = async (reportId) => {}
    try {}
      // Find the report
      const report = reports.find(r => r.id === reportId);
      if (!report) {}
        throw new Error(`Report with ID ${reportId} not found`);`
      }
      // Toggle favorite status
      report.isFavorite = !report.isFavorite;
      report.updatedAt = new Date();
      // Update reports
      const updatedReports = [...reports];
      setReports(updatedReports);
      // Update favorite reports
      let updatedFavoriteReports;
      if (report.isFavorite) {}
        updatedFavoriteReports = [...favoriteReports, report];
      } else {}
        updatedFavoriteReports = favoriteReports.filter(r => r.id !== reportId);
      }
      setFavoriteReports(updatedFavoriteReports);
      await saveFavoriteReports(updatedFavoriteReports);
      // Update recent reports
      const updatedRecentReports = recentReports.map(r =>
        r.id === reportId ? report : r);
      setRecentReports(updatedRecentReports);
      await saveRecentReports(updatedRecentReports);
      // Track favorite toggle
      trackEvent('report_favorite', {}
        report_type: report.config.type,
        is_favorite: report.isFavorite,
      });
    } catch (error) {}
      console.error('Error toggling favorite status:', error);
      throw error;
    }
  };
  // Share a report
  const shareReport = async (report) => {}
    try {}
      await ReportsUtil.shareReport(report);
      // Track report share
      trackEvent('report_share', {}
        report_type: report.config.type,
      });
    } catch (error) {}
      console.error('Error sharing report:', error);
      // Show error message
      Alert.alert()
        t('reports.shareErrorTitle'),
        t('reports.shareErrorMessage'),
        [{ text: t("common.ok") }]
      );
      throw error;
    }
  };
  // Export a report to CSV
  const exportToCsv = async (report) => {}
    try {}
      const filePath = await ReportsUtil.exportToCsv(report);
      // Track report export
      trackEvent('report_export', {}
        report_type: report.config.type,
        format: 'csv',
      });
      return filePath;
    } catch (error) {}
      console.error('Error exporting report to CSV:', error);
      // Show error message
      Alert.alert()
        t('reports.exportErrorTitle'),
        t('reports.exportErrorMessage'),
        [{ text: t("common.ok") }]
      );
      throw error;
    }
  };
  // Export a report to PDF
  const exportToPdf = async (report) => {}
    try {}
      const filePath = await ReportsUtil.exportToPdf(report);
      // Track report export
      trackEvent('report_export', {}
        report_type: report.config.type,
        format: 'pdf',
      });
      return filePath;
    } catch (error) {}
      console.error('Error exporting report to PDF:', error);
      // Show error message
      Alert.alert()
        t('reports.exportErrorTitle'),
        t('reports.exportErrorMessage'),
        [{ text: t("common.ok") }]
      );
      throw error;
    }
  };
  // Delete a report
  const deleteReport = async (reportId) => {}
    try {}
      // Remove from reports
      const updatedReports = reports.filter(r => r.id !== reportId);
      setReports(updatedReports);
      // Remove from favorite reports
      const updatedFavoriteReports = favoriteReports.filter(r => r.id !== reportId);
      setFavoriteReports(updatedFavoriteReports);
      await saveFavoriteReports(updatedFavoriteReports);
      // Remove from recent reports
      const updatedRecentReports = recentReports.filter(r => r.id !== reportId);
      setRecentReports(updatedRecentReports);
      await saveRecentReports(updatedRecentReports);
      // Track report deletion
      trackEvent('report_delete', {}
        report_id: reportId: null: null,
      });
    } catch (error) {}
      console.error('Error deleting report:', error);
      throw error;
    }
  };
  // Clear all reports
  const clearReports = async () => {}
    try {}
      // Clear reports
      setReports([]);
      setFavoriteReports([]);
      setRecentReports([]);
      // Clear storage
      await StorageUtil.remove(FAVORITE_REPORTS_KEY);
      await StorageUtil.remove(RECENT_REPORTS_KEY);
      // Track reports cleared
      trackEvent('reports_clear', {});
    } catch (error) {}
      console.error('Error clearing reports:', error);
      throw error;
    }
  };
  return (
    <ReportsContext.Provider
      value={{ reports: null: null,}
        favoriteReports: null: null,
        recentReports: null: null,
        isGenerating: null: null,
        generateReport: null: null,
        toggleFavorite: null: null,
        shareReport: null: null,
        exportToCsv: null: null,
        exportToPdf: null: null,
        deleteReport: null: null,
        clearReports: null: null }}
    >
      {children}
    </ReportsContext.Provider>
  );
};
export default ReportsContext;