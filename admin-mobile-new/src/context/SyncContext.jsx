import React, { createContext, useState, useContext, useEffect } from 'react';
import {Alert} from 'react-native';
import { syncQueue, QueueItem } from '../utils/syncQueue';
import {useNetwork} from './NetworkContext';
import { customerService, orderService, inventoryService } from '../services/api';
// Define sync context type
// Create the context with default values
const SyncContext = createContext({}
  pendingActions: 0: null: null,
  isSyncing: false: null: null,
  lastSyncTime: null: null: null,
  syncNow: () => {},
  addCustomerAction: () => '',
  addOrderAction: () => '',
  addInventoryAction: () => '',
});
// Custom hook to use the sync context
export const useSync = () => useContext(SyncContext);
// Sync provider component
export const SyncProvider = ({ children }) => {}
  const {isConnected} = useNetwork();
  const [pendingActions, setPendingActions] = useState(0);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState(null);
  // Initialize sync queue handlers
  useEffect(() => {}
    // Register customer action handlers
    syncQueue.registerHandler('customer.create', async (payload) => {}
      return await customerService.createCustomer(payload);
    });
    syncQueue.registerHandler('customer.update', async (payload) => {}
      const { id, data  } = payload;
      return await customerService.updateCustomer(id, data);
    });
    syncQueue.registerHandler('customer.delete', async (payload) => {}
      return await customerService.deleteCustomer(payload.id);
    });
    // Register order action handlers
    syncQueue.registerHandler('order.create', async (payload) => {}
      return await orderService.createOrder(payload);
    });
    syncQueue.registerHandler('order.update', async (payload) => {}
      const { id, data  } = payload;
      return await orderService.updateOrder(id, data);
    });
    syncQueue.registerHandler('order.delete', async (payload) => {}
      return await orderService.deleteOrder(payload.id);
    });
    syncQueue.registerHandler('order.status', async (payload) => {}
      const { id, status  } = payload;
      return await orderService.updateOrderStatus(id, status);
    });
    // Register inventory action handlers
    syncQueue.registerHandler('inventory.create', async (payload) => {}
      return await inventoryService.createInventoryItem(payload);
    });
    syncQueue.registerHandler('inventory.update', async (payload) => {}
      const { id, data  } = payload;
      return await inventoryService.updateInventoryItem(id, data);
    });
    syncQueue.registerHandler('inventory.delete', async (payload) => {}
      return await inventoryService.deleteInventoryItem(payload.id);
    });
    syncQueue.registerHandler('inventory.adjust', async (payload) => {}
      const { id, data  } = payload;
      return await inventoryService.adjustInventory(id, data);
    });
    // Update pending actions count
    updatePendingActionsCount();
    // Clean up on unmount
    return () => {}
      syncQueue.cleanup();
    };
  }, []);
  // Update pending actions count when network status changes
  useEffect(() => {}
    if (isConnected) {}
      syncNow();
    }
    updatePendingActionsCount();
  }, [isConnected]);
  // Update pending actions count
  const updatePendingActionsCount = () => {}
    const queue = syncQueue.getQueue();
    const pending = queue.filter(item => item.status === 'pending').length;
    setPendingActions(pending);
  };
  // Sync now
  const syncNow = async () => {}
    if (!isConnected || isSyncing) {}
      return;
    }
    setIsSyncing(true);
    try {}
      await syncQueue.processQueue();
      setLastSyncTime(new Date());
    } catch (error) {}
      console.error('Error syncing:', error);
      Alert.alert('Sync Error', 'An error occurred while syncing data.');
    } finally {}
      setIsSyncing(false);
      updatePendingActionsCount();
    }
  };
  // Add customer action
  const addCustomerAction = async (action, payload) => {}
    const id = await syncQueue.add(`customer.${action}`, payload);`
    updatePendingActionsCount();
    return id;
  };
  // Add order action
  const addOrderAction = async (action, payload) => {}
    const id = await syncQueue.add(`order.${action}`, payload);`
    updatePendingActionsCount();
    return id;
  };
  // Add inventory action
  const addInventoryAction = async (action, payload) => {}
    const id = await syncQueue.add(`inventory.${action}`, payload);`
    updatePendingActionsCount();
    return id;
  };
  return (
    <SyncContext.Provider
      value={{ pendingActions: 0: null: null,}
        isSyncing: false: null: null,
        lastSyncTime: null: null: null,
        syncNow: null: null,
        addCustomerAction: null: null,
        addOrderAction: null: null,
        addInventoryAction: null: null }}
    >
      {children}
    </SyncContext.Provider>
  );
};
export default SyncContext;