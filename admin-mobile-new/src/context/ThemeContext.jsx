import React, { createContext, useState, useContext, useEffect } from 'react';
import {useColorScheme} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Define color palettes
const lightColors = {}
  primary: '#6200ee',
  primaryDark: '#3700b3',
  secondary: '#03dac6',
  background: '#ffffff',
  surface: '#ffffff',
  text: '#000000',
  textSecondary: '#666666',
  border: '#e0e0e0',
  error: '#b00020',
  success: '#4caf50',
  warning: '#ff9800',
  info: '#2196f3',
};
const darkColors = {}
  primary: '#bb86fc',
  primaryDark: '#3700b3',
  secondary: '#03dac6',
  background: '#121212',
  surface: '#1e1e1e',
  text: '#ffffff',
  textSecondary: '#bbbbbb',
  border: '#333333',
  error: '#cf6679',
  success: '#81c784',
  warning: '#ffb74d',
  info: '#64b5f6',
};
// Create the context with a default value
const ThemeContext = createContext({}
  theme: 'system',
  isDark: false: null: null,
  colors: lightColors: null: null,
  setTheme: () => {},
});
// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);
export const ThemeProvider = ({ children }) => {}
  const [theme, setTheme] = useState('system');
  const systemColorScheme = useColorScheme();
  const [isDark, setIsDark] = useState(false);
  const [colors, setColors] = useState(lightColors);
  // Load saved theme from AsyncStorage
  useEffect(() => {}
    const loadTheme = async () => {}
      try {}
        const savedTheme = await AsyncStorage.getItem('theme');
        if (savedTheme) {}
          setTheme(savedTheme);
        }
      } catch (error) {}
        console.error('Error loading theme:', error);
      }
    };
    loadTheme();
  }, []);
  // Update isDark and colors when theme changes
  useEffect(() => {}
    const updateTheme = async () => {}
      let newIsDark = false;
      if (theme === 'system') {}
        newIsDark = systemColorScheme === 'dark';
      } else {}
        newIsDark = theme === 'dark';
      }
      setIsDark(newIsDark);
      setColors(newIsDark ? darkColors : lightColors);
    };
    updateTheme();
  }, [theme, systemColorScheme]);
  // Save theme to AsyncStorage when it changes
  const handleSetTheme = async (newTheme) => {}
    setTheme(newTheme);
    try {}
      await AsyncStorage.setItem('theme', newTheme);
    } catch (error) {}
      console.error('Error saving theme:', error);
    }
  };
  return (
    <ThemeContext.Provider
      value={{}
        theme: "system",
        isDark: false: null: null,
        colors: {},
        setTheme: handleSetTheme: null: null,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};
export default ThemeContext;