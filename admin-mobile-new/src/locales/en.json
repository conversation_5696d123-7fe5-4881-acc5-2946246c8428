{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "filter": "Filter", "sort": "Sort", "back": "Back", "next": "Next", "previous": "Previous", "done": "Done", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "retry": "Retry", "noData": "No data available", "noResults": "No results found", "all": "All", "active": "Active", "inactive": "Inactive", "status": "Status", "actions": "Actions", "details": "Details", "view": "View", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "date": "Date", "time": "Time", "amount": "Amount", "total": "Total", "quantity": "Quantity", "price": "Price", "description": "Description", "notes": "Notes", "category": "Category", "type": "Type", "id": "ID", "created": "Created", "updated": "Updated", "createdAt": "Created At", "updatedAt": "Updated At", "required": "Required", "optional": "Optional", "selectOption": "Select an option", "enterValue": "Enter a value", "invalidInput": "Invalid input", "offline": "You are offline", "online": "You are back online", "syncing": "Syncing...", "synced": "All synced", "pendingSync": "{{count}} pending {{count, plural, one {action} other {actions}}}", "selectDateRange": "Select Date Range", "startDate": "Start Date", "endDate": "End Date", "apply": "Apply"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "signup": "Sign Up", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordsDontMatch": "Passwords don't match", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "logoutSuccess": "Logout successful", "logoutError": "Logout failed", "resetPasswordSuccess": "Password reset email sent", "resetPasswordError": "Failed to send password reset email", "changePasswordSuccess": "Password changed successfully", "changePasswordError": "Failed to change password", "invalidCredentials": "Invalid email or password", "accountLocked": "Your account has been locked", "accountDisabled": "Your account has been disabled", "sessionExpired": "Your session has expired, please login again", "unauthorized": "Unauthorized access", "passwordResetInstructions": "Enter your email address and we'll send you a link to reset your password.", "passwordRequirements": "Password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character.", "biometricLogin": "Biometric Login", "enableBiometricLogin": "Enable Biometric Login", "disableBiometricLogin": "Disable Biometric Login", "biometricLoginEnabled": "Biometric login enabled", "biometricLoginDisabled": "Biometric login disabled", "biometricLoginNotAvailable": "Biometric login not available on this device", "biometricLoginFailed": "Biometric authentication failed", "biometricLoginCancelled": "Biometric authentication cancelled"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome, {{name}}!", "todayOrders": "Today's Orders", "pendingOrders": "Pending Orders", "completedOrders": "Completed Orders", "totalCustomers": "Total Customers", "totalOrders": "Total Orders", "totalRevenue": "Total Revenue", "recentOrders": "Recent Orders", "recentCustomers": "Recent Customers", "lowStockItems": "Low Stock Items", "quickActions": "Quick Actions", "addCustomer": "Add Customer", "createOrder": "Create Order", "addInventory": "Add Inventory", "viewReports": "View Reports", "salesOverview": "Sales Overview", "thisWeek": "This Week", "thisMonth": "This Month", "thisYear": "This Year", "lastWeek": "Last Week", "lastMonth": "Last Month", "lastYear": "Last Year", "noRecentOrders": "No recent orders", "noRecentCustomers": "No recent customers", "noLowStockItems": "No low stock items"}, "customers": {"title": "Customers", "customer": "Customer", "customers": "Customers", "addCustomer": "Add Customer", "editCustomer": "Edit Customer", "deleteCustomer": "Delete Customer", "customerDetails": "Customer Details", "customerOrders": "Customer Orders", "customerMeasurements": "Customer Measurements", "deleteCustomerConfirm": "Are you sure you want to delete this customer?", "customerAddedSuccess": "Customer added successfully", "customerAddedError": "Failed to add customer", "customerUpdatedSuccess": "Customer updated successfully", "customerUpdatedError": "Failed to update customer", "customerDeletedSuccess": "Customer deleted successfully", "customerDeletedError": "Failed to delete customer", "searchCustomers": "Search customers...", "noCustomersFound": "No customers found", "noCustomersFoundForSearch": "No customers found matching your search", "totalSpent": "Total Spent", "lastOrder": "Last Order", "measurements": "Measurements", "chest": "Chest", "waist": "Waist", "hips": "Hips", "inseam": "Inseam", "sleeve": "Sleeve", "shoulder": "Shoulder", "neck": "Neck", "thigh": "Thigh", "length": "Length", "unit": "Unit", "inches": "Inches", "centimeters": "Centimeters"}, "orders": {"title": "Orders", "order": "Order", "orders": "Orders", "addOrder": "Add Order", "editOrder": "Edit Order", "deleteOrder": "Delete Order", "orderDetails": "Order Details", "orderItems": "Order Items", "orderPayments": "Order Payments", "deleteOrderConfirm": "Are you sure you want to delete this order?", "orderAddedSuccess": "Order added successfully", "orderAddedError": "Failed to add order", "orderUpdatedSuccess": "Order updated successfully", "orderUpdatedError": "Failed to update order", "orderDeletedSuccess": "Order deleted successfully", "orderDeletedError": "Failed to delete order", "searchOrders": "Search orders...", "noOrdersFound": "No orders found", "noOrdersFoundForSearch": "No orders found matching your search", "orderNumber": "Order Number", "orderDate": "Order Date", "dueDate": "Due Date", "deliveryDate": "Delivery Date", "orderStatus": "Order Status", "paymentStatus": "Payment Status", "paymentMethod": "Payment Method", "subtotal": "Subtotal", "discount": "Discount", "tax": "Tax", "total": "Total", "paid": "Paid", "due": "Due", "addItem": "Add Item", "editItem": "<PERSON>em", "deleteItem": "Delete Item", "addPayment": "Add Payment", "editPayment": "Edit Payment", "deletePayment": "Delete Payment", "itemName": "Item Name", "itemDescription": "Item Description", "paymentDate": "Payment Date", "paymentAmount": "Payment Amount", "paymentNote": "Payment Note", "pending": "Pending", "inProgress": "In Progress", "readyForDelivery": "Ready for Delivery", "delivered": "Delivered", "completed": "Completed", "cancelled": "Cancelled", "onHold": "On Hold", "returned": "Returned", "fullyPaid": "<PERSON>y Paid", "partiallyPaid": "Partially Paid", "unpaid": "Unpaid", "overdue": "Overdue", "refunded": "Refunded", "cash": "Cash", "creditCard": "Credit Card", "debitCard": "Debit Card", "bankTransfer": "Bank Transfer", "mobileBanking": "Mobile Banking", "cheque": "Cheque", "other": "Other"}, "inventory": {"title": "Inventory", "item": "<PERSON><PERSON>", "items": "Items", "addItem": "Add Item", "editItem": "<PERSON>em", "deleteItem": "Delete Item", "itemDetails": "<PERSON><PERSON>", "deleteItemConfirm": "Are you sure you want to delete this item?", "itemAddedSuccess": "Item added successfully", "itemAddedError": "Failed to add item", "itemUpdatedSuccess": "Item updated successfully", "itemUpdatedError": "Failed to update item", "itemDeletedSuccess": "Item deleted successfully", "itemDeletedError": "Failed to delete item", "searchItems": "Search items...", "noItemsFound": "No items found", "noItemsFoundForSearch": "No items found matching your search", "itemName": "Item Name", "itemCode": "Item Code", "category": "Category", "quantity": "Quantity", "unit": "Unit", "price": "Price", "cost": "Cost", "inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "reorderLevel": "Reorder Level", "adjustInventory": "Adjust Inventory", "addStock": "Add Stock", "removeStock": "Remove Stock", "adjustmentReason": "Adjustment Reason", "adjustmentNote": "Adjustment Note", "adjustmentDate": "Adjustment Date", "adjustmentQuantity": "Adjustment Quantity", "fabric": "<PERSON><PERSON><PERSON>", "accessory": "Accessory", "tool": "Tool", "material": "Material", "other": "Other", "meters": "Meters", "yards": "Yards", "pieces": "Pieces", "rolls": "Rolls", "boxes": "Boxes", "purchase": "Purchase", "sale": "Sale", "return": "Return", "damage": "Damage", "loss": "Loss", "correction": "Correction", "initialStock": "Initial Stock"}, "settings": {"title": "Settings", "general": "General", "account": "Account", "profile": "Profile", "security": "Security", "notifications": "Notifications", "appearance": "Appearance", "language": "Language", "about": "About", "help": "Help", "feedback": "<PERSON><PERSON><PERSON>", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "version": "Version", "updateProfile": "Update Profile", "changePassword": "Change Password", "darkMode": "Dark Mode", "lightMode": "Light Mode", "systemTheme": "System Theme", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "orderNotifications": "Order Notifications", "customerNotifications": "Customer Notifications", "inventoryNotifications": "Inventory Notifications", "paymentNotifications": "Payment Notifications", "reminderNotifications": "Reminder Notifications", "systemNotifications": "System Notifications", "english": "English", "bengali": "Bengali", "profileUpdatedSuccess": "Profile updated successfully", "profileUpdatedError": "Failed to update profile", "passwordChangedSuccess": "Password changed successfully", "passwordChangedError": "Failed to change password", "settingsSavedSuccess": "Setting<PERSON> saved successfully", "settingsSavedError": "Failed to save settings", "analytics": "Analytics", "enableAnalytics": "Enable Analytics", "analyticsDescription": "Help us improve the app by sending anonymous usage data", "backup": "Backup", "restore": "Rest<PERSON>", "backupData": "Backup Data", "restoreData": "Restore Data", "lastBackup": "Last Backup", "backupSuccess": "Backup created successfully", "backupError": "Failed to create backup", "restoreSuccess": "Data restored successfully", "restoreError": "Failed to restore data", "exportData": "Export Data", "importData": "Import Data", "exportSuccess": "Data exported successfully", "exportError": "Failed to export data", "importSuccess": "Data imported successfully", "importError": "Failed to import data", "offline": "Offline"}, "reports": {"title": "Reports", "overview": "Overview", "reports": "Reports", "favorites": "Favorites", "generateReport": "Generate Report", "generate": "Generate", "generating": "Generating...", "reportType": "Report Type", "timePeriod": "Time Period", "chartType": "Chart Type", "reportTitle": "Report Title", "sales": "Sales", "customers": "Customers", "inventory": "Inventory", "orders": "Orders", "payments": "Payments", "today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "lastMonth": "Last Month", "thisYear": "This Year", "lastYear": "Last Year", "customRange": "Custom Range", "barChart": "Bar Chart", "lineChart": "Line Chart", "pieChart": "Pie Chart", "progressChart": "Progress Chart", "quickStats": "Quick Stats", "totalReports": "Total Reports", "recentReports": "Recent Reports", "favoriteReports": "Favorite Reports", "recent": "Recent", "viewAll": "View All", "noRecentReports": "No recent reports", "noFavoriteReports": "No favorite reports", "markAsFavorite": "<PERSON> reports as favorite to see them here", "allReports": "All Reports", "generateFirst": "Generate First Report", "noReports": "No reports generated yet", "reportDetail": "Report Detail", "summary": "Summary", "total": "Total", "average": "Average", "min": "Min", "max": "Max", "favorite": "Favorite", "unfavorite": "Unfavorite", "share": "Share", "exportPdf": "Export PDF", "delete": "Delete", "chartWillBeDisplayedHere": "Chart will be displayed here", "noChartAvailable": "No chart available", "salesReport": "Sales Report", "customersReport": "Customers Report", "inventoryReport": "Inventory Report", "ordersReport": "Orders Report", "paymentsReport": "Payments Report", "customReport": "Custom Report", "generateErrorTitle": "Generation Error", "generateErrorMessage": "Failed to generate report. Please try again.", "shareErrorTitle": "Share Error", "shareErrorMessage": "Failed to share report. Please try again.", "exportErrorTitle": "Export Error", "exportErrorMessage": "Failed to export report. Please try again.", "chartError": "Chart Error", "chartErrorMessage": "Unable to display chart. Please try again."}, "analytics": {"advancedAnalytics": "Advanced Analytics", "trends": "Trends", "predictions": "Predictions", "patterns": "Patterns", "analyzingData": "Analyzing data...", "trendOverview": "Trend Overview", "currentValue": "Current Value", "change": "Change", "changePercent": "Change %", "confidence": "Confidence", "trendChart": "Trend Chart", "volatilityAnalysis": "Volatility Analysis", "volatilityIndex": "Volatility Index", "highVolatility": "High volatility - significant fluctuations", "moderateVolatility": "Moderate volatility - some fluctuations", "lowVolatility": "Low volatility - stable performance", "nextPeriodForecast": "Next Period Forecast", "predictedValue": "Predicted Value", "keyFactors": "Key Factors", "recommendations": "Recommendations", "detectedPatterns": "Detected Patterns", "noPatternsDetected": "No significant patterns detected", "seasonalAnalysis": "Seasonal Analysis", "seasonalStrength": "Seasonal Strength", "strongSeasonalPattern": "Strong seasonal pattern detected", "peakPeriods": "Peak Periods", "lowPeriods": "Low Periods", "movingAverages": "Moving Averages", "shortTerm": "Short Term", "mediumTerm": "Medium Term", "longTerm": "Long Term"}, "realtime": {"liveData": "Live Data", "live": "Live", "paused": "Paused", "updates": "Updates", "lastUpdate": "Last Update", "pause": "Pause", "resume": "Resume", "sales": "Sales", "customers": "Customers", "orders": "Orders", "inventory": "Inventory", "liveChart": "Live Chart", "dataStream": "Data Stream", "waitingForData": "Waiting for data..."}, "ai": {"businessIntelligence": "AI Business Intelligence", "insights": "Insights", "recommendations": "Recommendations", "market": "Market", "predictions": "Predictions", "analyzingBusiness": "Analyzing Business Data...", "processingData": "Processing with AI algorithms", "impact": "Impact", "confidence": "Confidence", "value": "Value", "actionItems": "Action Items", "expectedOutcome": "Expected Outcome", "timeline": "Timeline", "implementationSteps": "Implementation Steps", "marketTrends": "Market Trends", "strength": "Strength", "competitivePosition": "Competitive Position", "ranking": "Ranking", "strengths": "Strengths", "threats": "Threats", "customerBehavior": "Customer Behavior", "satisfactionScore": "Satisfaction Score", "predictiveModel": "Predictive Model", "modelType": "Model Type", "accuracy": "Accuracy", "scenarios": "Scenarios"}, "visualization": {"interactiveCharts": "Interactive Charts", "generatingCharts": "Generating interactive charts...", "reset": "Reset", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "selectDataPoint": "Tap on data points for details", "chartTypes": "Chart Types", "overview": "Overview", "revenue": "Revenue", "customers": "Customers", "performance": "Performance", "distribution": "Distribution", "correlation": "Correlation"}}