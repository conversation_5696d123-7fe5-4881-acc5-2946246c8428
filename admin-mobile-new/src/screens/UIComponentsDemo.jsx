import React, {useState} from 'react';
import { StyleSheet, View, Text, ScrollView, Switch } from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import {useTheme} from '../context/ThemeContext';
import {
  Avatar,
  Badge,
  Button,
  Card,
  Divider,
  Input,
  List,
  ListItem,
  Modal,
  Spinner
} from '../components/ui';
const UIComponentsDemo = () => {
  const { isDark, setTheme } = useTheme();
  const [inputValue, setInputValue] = useState('');
  const [passwordValue, setPasswordValue] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isFullScreenSpinnerVisible, setIsFullScreenSpinnerVisible] = useState(false);

  const toggleTheme = () => {
    setTheme(isDark ? 'light' : 'dark');
  };

  const showFullScreenSpinner = () => {
    setIsFullScreenSpinnerVisible(true);
    // Hide spinner after 3 seconds
    setTimeout(() => {
      setIsFullScreenSpinnerVisible(false);
    }, 3000);
  };
  return (
    <ScrollView style={[styles.container, { backgroundColor: isDark ? '#121212' : '#f5f5f5' }]}>
      <View style={styles.contentContainer}>
        <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000' }]}>
          UI Components Demo
        </Text>

        <View style={styles.themeToggle}>
          <Text style={{ color: isDark ? '#bbbbbb' : '#666666', marginRight: 10 }}>
            Dark Mode
          </Text>
          <Switch value={isDark} onValueChange={toggleTheme} />
        </View>

        {/* Buttons */}
        <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000', fontSize: 20 }]}>
          Buttons
        </Text>
        <View style={styles.componentRow}>
          <Button title="Primary" onPress={() => {}} />
          <Button title="Secondary" type="secondary" onPress={() => {}} />
          <Button title="Outline" type="outline" onPress={() => {}} />
          <Button title="With Icon" icon="star" onPress={() => {}} />
          <Button title="Disabled" disabled onPress={() => {}} />
        </View>

        {/* Cards */}
        <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000', fontSize: 20 }]}>
          Cards
        </Text>
        <Card title="Basic Card">
          <Text style={{ color: isDark ? '#bbbbbb' : '#666666' }}>
            This is a basic card component with a title and content.
          </Text>
        </Card>

        <Card
          title="Card with Actions"
          actions={[
            { title: "Action 1", onPress: () => {} },
            { title: "Action 2", onPress: () => {} }
          ]}
        >
          <Text style={{ color: isDark ? '#bbbbbb' : '#666666' }}>
            This card has action buttons.
          </Text>
        </Card>

        {/* Inputs */}
        <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000', fontSize: 20 }]}>
          Inputs
        </Text>
        <Input
          label="Text Input"
          placeholder="Enter text"
          value={inputValue}
          onChangeText={setInputValue}
        />

        <Input
          label="Password"
          placeholder="Enter password"
          value={passwordValue}
          onChangeText={setPasswordValue}
          secureTextEntry
        />

        <Input
          label="With Error"
          placeholder="Enter text"
          error="This field is required"
        />

        <Input
          label="Multiline"
          placeholder="Enter multiple lines of text"
          multiline
          numberOfLines={3}
        />

        {/* Spinners */}
        <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000', fontSize: 20 }]}>
          Spinners
        </Text>
        <View style={styles.componentRow}>
          <Spinner size="small" />
          <Spinner size="medium" />
          <Spinner size="large" />
          <Button title="Show Full Screen" onPress={showFullScreenSpinner} />
        </View>

        {isFullScreenSpinnerVisible && (
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0,0,0,0.7)',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000
          }}>
            <Spinner size="large" color="#ffffff" />
          </View>
        )}

        {/* Modals */}
        <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000', fontSize: 20 }]}>
          Modals
        </Text>
        <Button
          title="Show Modal"
          onPress={() => setIsModalVisible(true)}
        />

        <Modal
          visible={isModalVisible}
          onClose={() => setIsModalVisible(false)}
          title="Modal Title"
        >
          <Text style={{ marginBottom: 20, color: isDark ? '#bbbbbb' : '#666666' }}>
            This is a modal dialog with custom content.
          </Text>
          <Button
            title="Close"
            onPress={() => setIsModalVisible(false)}
          />
        </Modal>
      </View>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16
  },
  contentContainer: {
    paddingBottom: 40
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16
  },
  themeToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24
  },
  componentRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: 10,
    marginVertical: 16
  }
});
export default UIComponentsDemo;