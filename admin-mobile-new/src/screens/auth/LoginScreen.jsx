import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity, Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import {StatusBar} from 'expo-status-bar';
import {Ionicons} from '@expo/vector-icons';
import {useTheme} from '../../context/ThemeContext';
import {useAuth} from '../../context/AuthContext';
import {useNetwork} from '../../context/NetworkContext';
import { Button, Input, Divider } from '../../components/ui';
import { ValidationUtil, BiometricsUtil } from '../../utils';
const LoginScreen = ({
  navigation,
  onForgotPassword
}) => {
  const {isDark} = useTheme();
  const {login} = useAuth();
  const {isConnected} = useNetwork();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [isBiometricsAvailable, setIsBiometricsAvailable] = useState(false);
  const [isBiometricsEnabled, setIsBiometricsEnabled] = useState(false);
  const [biometricType, setBiometricType] = useState('Biometric');
  // Validate email
  const validateEmail = (email) => {
    const result = ValidationUtil.validateEmail(email);
    setEmailError(result.message);
    return result.isValid;
  };

  // Validate password
  const validatePassword = (password) => {
    const result = ValidationUtil.validatePassword(password, { minLength: 6 });
    setPasswordError(result.message);
    return result.isValid;
  };
  // Check biometrics availability
  useEffect(() => {
    const checkBiometrics = async () => {
      try {
        // Check if biometrics is available
        const isAvailable = await BiometricsUtil.isBiometricsAvailable();
        setIsBiometricsAvailable(isAvailable);

        if (isAvailable) {
          // Check if biometrics is enabled
          const isEnabled = await BiometricsUtil.isBiometricsEnabled();
          setIsBiometricsEnabled(isEnabled);

          // Get biometric type
          const types = await BiometricsUtil.getBiometricTypes();
          if (types.length > 0) {
            setBiometricType(BiometricsUtil.getBiometricTypeName(types[0]));
          }

          // If biometrics is enabled, try to authenticate
          if (isEnabled) {
            handleBiometricLogin();
          }
        }
      } catch (error) {
        console.error('Error checking biometrics:', error);
      }
    };

    checkBiometrics();
  }, []);
  // Handle biometric login
  const handleBiometricLogin = async () => {
    try {
      // Authenticate with biometrics
      const credentials = await BiometricsUtil.authenticateWithBiometrics();
      if (credentials) {
        setEmail(credentials.username);
        setPassword(credentials.password);
        // Login with credentials
        setIsLoading(true);
        try {
          await login({ email: credentials.username, password: credentials.password });
        } catch (error) {
          console.error('Biometric login error:', error);
        } finally {
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error('Error authenticating with biometrics:', error);
    }
  };
  // Handle login
  const handleLogin = async () => {
    // Check if we're online
    if (!isConnected) {
      Alert.alert(
        'No Internet Connection',
        'You need to be connected to the internet to log in.'
      );
      return;
    }

    // Validate inputs
    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);
    if (!isEmailValid || !isPasswordValid) {
      return;
    }

    setIsLoading(true);
    try {
      await login({ email, password });

      // If login is successful, ask to enable biometrics
      if (isBiometricsAvailable && !isBiometricsEnabled) {
        Alert.alert(
          `Enable ${biometricType} Login`,
          `Would you like to enable ${biometricType} login for faster access next time?`,
          [
            {
              text: 'Not Now',
              style: 'cancel',
            },
            {
              text: 'Enable',
              onPress: async () => {
                const success = await BiometricsUtil.enableBiometrics(email, password);
                if (success) {
                  setIsBiometricsEnabled(true);
                }
              },
            },
          ]
        );
      }
    } catch (error) {
      // Error is already handled in the AuthContext
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };
  // Handle demo login
  const handleDemoLogin = async () => {
    // Check if we're online
    if (!isConnected) {
      Alert.alert(
        'No Internet Connection',
        'You need to be connected to the internet to log in.'
      );
      return;
    }

    setEmail('<EMAIL>');
    setPassword('password');
    setIsLoading(true);

    try {
      await login({ email: '<EMAIL>', password: 'password' });
    } catch (error) {
      // Error is already handled in the AuthContext
      console.error('Demo login error:', error);
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: isDark ? '#121212' : '#f5f5f5' }]}
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}>
        <StatusBar style={isDark ? 'light' : 'dark'} />

        {/* Logo */}
        <View style={styles.logoContainer}>
          <Text style={[styles.logoText, { color: isDark ? '#ffffff' : '#000000' }]}>
            Tailor Management
          </Text>
          <Text style={[styles.logoSubtext, { color: isDark ? '#bbbbbb' : '#666666' }]}>
            Professional Tailoring Business Management
          </Text>
        </View>

        {/* Login Form */}
        <View style={[
          styles.formContainer,
          { backgroundColor: isDark ? '#1e1e1e' : '#ffffff' }
        ]}>
          <Text style={[styles.formTitle, { color: isDark ? '#ffffff' : '#000000' }]}>
            Login to Your Account
          </Text>

          <Input
            label="Email"
            placeholder="Enter your email"
            value={email}
            onChangeText={(text) => {
              setEmail(text);
              if (emailError) validateEmail(text);
            }}
            keyboardType="email-address"
            autoCapitalize="none"
            error={emailError}
          />

          <Input
            label="Password"
            placeholder="Enter your password"
            value={password}
            onChangeText={(text) => {
              setPassword(text);
              if (passwordError) validatePassword(text);
            }}
            secureTextEntry
            error={passwordError}
          />

          <TouchableOpacity
            style={styles.forgotPassword}
            onPress={() => {
              if (onForgotPassword) {
                onForgotPassword();
              } else if (navigation) {
                navigation.navigate('ForgotPassword');
              }
            }}
          >
            <Text style={{ color: isDark ? '#bb86fc' : '#6200ee' }}>
              Forgot Password?
            </Text>
          </TouchableOpacity>

          <Button
            title="Login"
            onPress={handleLogin}
            loading={isLoading}
          />

          <Divider style={{ marginVertical: 20 }} />

          {isBiometricsAvailable && isBiometricsEnabled && (
            <Button
              title={`Login with ${biometricType}`}
              icon={biometricType === 'Face ID' ? 'face-recognition' : 'fingerprint'}
              onPress={handleBiometricLogin}
              type="outline"
              style={{ marginBottom: 10 }}
            />
          )}

          <Button
            title="Demo Login"
            onPress={handleDemoLogin}
            type="secondary"
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoText: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  logoSubtext: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
  formContainer: {
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    maxWidth: 500,
    width: '100%',
    alignSelf: 'center',
  },
  formTitle: {
    fontSize: 22,
    fontWeight: '600',
    marginBottom: 20,
    textAlign: 'center',
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 20,
  },
  footer: {
    marginTop: 20,
    alignItems: 'center',
  },
});
export default LoginScreen;