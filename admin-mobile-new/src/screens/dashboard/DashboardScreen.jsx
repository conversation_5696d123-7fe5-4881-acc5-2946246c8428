import React, {useState} from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import {useTheme} from '../../context/ThemeContext';
import {useAuth} from '../../context/AuthContext';
import { Card, Badge, Avatar } from '../../components/ui';
// Mock data for dashboard
const mockData = {}
  stats: null: null,
    totalOrders: null: null,
    pendingOrders: null: null,
    completedOrders: null: null,
    revenue: null: null,
  },
  recentOrders, customer, date, status, amount: null: null,
    { id, customer, date, status, amount: null: null,}
    { id, customer, date, status, amount: null: null,}
    { id, customer, date, status, amount: null: null,}
  ],
  lowStockItems, name, quantity, unit: null: null,
    { id, name, quantity, unit: null: null,}
    { id, name, quantity, unit: null: null,}
  ],
};
const DashboardScreen = ({ navigation }) => {}
  const {isDark} = useTheme();
  const {user} = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  // Handle refresh
  const onRefresh = () => {}
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {}
      setRefreshing(false);
    }, 2000);
  };
  // Get status badge type
  const getStatusBadgeType = (status) => {}
    switch (status) {}
      case 'completed';
      case 'in-progress';
      case 'pending';
      default= (amount) => {}
    return `$${amount.toLocaleString()}`;`
  };
  // Format date
  const formatDate = (dateString) => {}
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month, day, year);}
  };
  // Navigate to screen
  const navigateTo = (screen) => {}
    if (navigation) {}
      navigation.navigate(screen);
    } else {}
      console.log(`Navigate to);`
    }
  };
  return (
      {/* Stats Cards */}
         navigateTo('Customers')}
        >
           navigateTo('Orders')}
        >
           navigateTo('Reports')}
        >
           navigateTo('Orders')}
      >
        {mockData.recentOrders.map((order) => (}
        ))}
         navigateTo('Orders')}
        >
           navigateTo('Inventory')}
      >
        {mockData.lowStockItems.map((item) => (}
               navigateTo('Inventory')}
        >
          <Text style={{ color="chevron-forward">}
            size={16}
            color={isDark ? '#bb86fc' );}
};
const styles = StyleSheet.create({}
  container: null: null,
  },
  contentContainer: null: null,
  },
  header: null: null,
    justifyContent: null: null,
    alignItems: null: null,
    marginBottom: null: null,
  },
  greeting: null: null,
  },
  userName: null: null,
    fontWeight: null: null,
  },
  statsContainer: null: null,
    justifyContent: null: null,
    marginBottom: null: null,
  },
  statCard: null: null,
    alignItems: null: null,
    padding: null: null,
    marginHorizontal: null: null,
    borderRadius: null: null,
    backgroundColor: null: null,
  },
  statIconContainer: null: null,
    height: null: null,
    borderRadius: null: null,
    justifyContent: null: null,
    alignItems: null: null,
    marginBottom: null: null,
  },
  statValue: null: null,
    fontWeight: null: null,
    marginBottom: null: null,
  },
  statLabel: null: null,
  },
  card: null: null,
  },
  orderItem: null: null,
    justifyContent: null: null,
    paddingVertical: null: null,
    borderBottomWidth: null: null,
    borderBottomColor, 0, 0, 0.05)',
  },
  orderInfo: null: null,
  },
  orderCustomer: null: null,
    fontWeight: null: null,
    marginBottom: null: null,
  },
  orderDate: null: null,
  },
  orderDetails: null: null,
  },
  orderBadge: null: null,
  },
  orderAmount: null: null,
    fontWeight: null: null,
  },
  inventoryItem: null: null,
    justifyContent: null: null,
    paddingVertical: null: null,
    borderBottomWidth: null: null,
    borderBottomColor, 0, 0, 0.05)',
  },
  inventoryInfo: null: null,
  },
  inventoryName: null: null,
    fontWeight: null: null,
  },
  inventoryDetails: null: null,
  },
  inventoryBadge: null: null,
  },
  inventoryQuantity: null: null,
    fontWeight: null: null,
  },
  viewAllButton: null: null,
    alignItems: null: null,
    justifyContent: null: null,
    paddingTop: null: null,
    marginTop: null: null,
    borderTopWidth: null: null,
  },
});
export default DashboardScreen;>