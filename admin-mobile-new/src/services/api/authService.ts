import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient, { ApiResponse } from './client';

// Define interfaces
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  email: string;
  token: string;
  password: string;
  password_confirmation: string;
}

export interface ProfileUpdateData {
  name?: string;
  email?: string;
  phone?: string;
}

export interface PasswordChangeData {
  current_password: string;
  password: string;
  password_confirmation: string;
}

// Auth service class
class AuthService {
  // Login user
  async login(credentials: LoginCredentials): Promise<User> {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
      
      // Save token and user data
      await AsyncStorage.setItem('auth_token', response.data.token);
      await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
      
      return response.data.user;
    } catch (error) {
      throw error;
    }
  }
  
  // Register user
  async register(data: RegisterData): Promise<User> {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/register', data);
      
      // Save token and user data
      await AsyncStorage.setItem('auth_token', response.data.token);
      await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
      
      return response.data.user;
    } catch (error) {
      throw error;
    }
  }
  
  // Logout user
  async logout(): Promise<void> {
    try {
      // Call logout endpoint
      await apiClient.post('/auth/logout');
      
      // Clear token and user data
      await AsyncStorage.multiRemove(['auth_token', 'user']);
    } catch (error) {
      // Even if the API call fails, clear local storage
      await AsyncStorage.multiRemove(['auth_token', 'user']);
      throw error;
    }
  }
  
  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      // Try to get user from storage first
      const userJson = await AsyncStorage.getItem('user');
      
      if (userJson) {
        return JSON.parse(userJson);
      }
      
      // If not in storage, fetch from API
      const response = await apiClient.get<User>('/auth/user');
      
      // Save user data
      await AsyncStorage.setItem('user', JSON.stringify(response.data));
      
      return response.data;
    } catch (error) {
      // If error, return null
      return null;
    }
  }
  
  // Request password reset
  async requestPasswordReset(data: PasswordResetRequest): Promise<ApiResponse> {
    return apiClient.post('/auth/password/email', data);
  }
  
  // Reset password
  async resetPassword(data: PasswordResetConfirm): Promise<ApiResponse> {
    return apiClient.post('/auth/password/reset', data);
  }
  
  // Update profile
  async updateProfile(data: ProfileUpdateData): Promise<User> {
    try {
      const response = await apiClient.put<User>('/auth/profile', data);
      
      // Update user in storage
      await AsyncStorage.setItem('user', JSON.stringify(response.data));
      
      return response.data;
    } catch (error) {
      throw error;
    }
  }
  
  // Change password
  async changePassword(data: PasswordChangeData): Promise<ApiResponse> {
    return apiClient.post('/auth/password/change', data);
  }
  
  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    const token = await AsyncStorage.getItem('auth_token');
    return !!token;
  }
}

// Export singleton instance
export const authService = new AuthService();

// Export default for convenience
export default authService;
