import AsyncStorage from '@react-native-async-storage/async-storage';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { isExpoGo } from '../../utils/environment';
import { NetworkUtil } from '../../utils';

// Define API configuration
const API_CONFIG = {
  // Replace with your actual API URL
  BASE_URL: 'https://api.tailormanagement.com/api',
  // For development, you might use a local URL
  // BASE_URL: 'http://localhost:8000/api',
  TIMEOUT: 30000, // 30 seconds
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

// Define constants
const USE_MOCK_API = __DEV__ || isExpoGo();

// Define response interface
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message: string;
  success: boolean;
}

// Import mock data
import mockData from './mockData';

// Create API client class
class ApiClient {
  private client: AxiosInstance;
  private static instance: ApiClient;
  private useMockApi: boolean;

  private constructor() {
    // Determine if we should use mock API
    this.useMockApi = USE_MOCK_API;

    // Create axios instance
    this.client = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: API_CONFIG.HEADERS,
    });

    // Add request interceptor
    this.client.interceptors.request.use(
      async (config) => {
        // Check network connectivity
        const isConnected = await NetworkUtil.isNetworkAvailable();

        // If not connected, use mock API
        if (!isConnected) {
          this.useMockApi = true;
          console.log('Network unavailable, using mock API');
        }

        // Get token from storage
        const token = await AsyncStorage.getItem('auth_token');

        // If token exists, add it to the headers
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        // Transform response data
        return this.transformResponse(response);
      },
      async (error: AxiosError) => {
        // Handle response error
        return this.handleError(error);
      }
    );
  }

  // Get singleton instance
  public static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }

    return ApiClient.instance;
  }

  // Transform response to standard format
  private transformResponse<T>(response: AxiosResponse): ApiResponse<T> {
    return {
      data: response.data.data || response.data,
      status: response.status,
      message: response.data.message || 'Success',
      success: true,
    };
  }

  // Handle API errors
  private async handleError(error: AxiosError): Promise<ApiResponse> {
    // Default error response
    const errorResponse: ApiResponse = {
      data: null,
      status: error.response?.status || 500,
      message: 'An error occurred',
      success: false,
    };

    // If response exists, get error details
    if (error.response) {
      const { data, status } = error.response;

      errorResponse.status = status;
      errorResponse.message = data.message || 'An error occurred';

      // Handle 401 Unauthorized error
      if (status === 401) {
        // Clear token and user data
        await AsyncStorage.multiRemove(['auth_token', 'user']);

        // You might want to trigger a logout action here
        // For now, we'll just return the error
      }
    } else if (error.request) {
      // Request was made but no response was received
      errorResponse.message = 'No response from server';
    } else {
      // Something happened in setting up the request
      errorResponse.message = error.message;
    }

    return Promise.reject(errorResponse);
  }

  // Mock API response
  private async mockApiResponse<T>(
    method: string,
    url: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    console.log(`[MOCK API] ${method} ${url}`, data);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Handle auth endpoints
    if (url.startsWith('/auth')) {
      return this.mockAuthEndpoints<T>(method, url, data);
    }

    // Handle customer endpoints
    if (url.startsWith('/customers')) {
      return this.mockCustomerEndpoints<T>(method, url, data);
    }

    // Handle order endpoints
    if (url.startsWith('/orders')) {
      return this.mockOrderEndpoints<T>(method, url, data);
    }

    // Handle inventory endpoints
    if (url.startsWith('/inventory')) {
      return this.mockInventoryEndpoints<T>(method, url, data);
    }

    // Default response
    return {
      data: null as T,
      status: 404,
      message: 'Endpoint not found in mock API',
      success: false,
    };
  }

  // Mock auth endpoints
  private async mockAuthEndpoints<T>(
    method: string,
    url: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    // Handle login
    if (method === 'POST' && url === '/auth/login') {
      const { email, password } = data;

      // Check if user exists
      if (mockData.auth[email] && password === 'password') {
        return {
          data: mockData.auth[email] as unknown as T,
          status: 200,
          message: 'Login successful',
          success: true,
        };
      } else {
        return {
          data: null as T,
          status: 401,
          message: 'Invalid credentials',
          success: false,
        };
      }
    }

    // Handle get current user
    if (method === 'GET' && url === '/auth/user') {
      const token = await AsyncStorage.getItem('auth_token');

      if (token && token.startsWith('mock-')) {
        const userType = token.split('-')[1];
        const user = mockData.users.find(u => u.role === userType);

        if (user) {
          return {
            data: user as unknown as T,
            status: 200,
            message: 'User retrieved successfully',
            success: true,
          };
        }
      }

      return {
        data: null as T,
        status: 401,
        message: 'Unauthorized',
        success: false,
      };
    }

    // Handle logout
    if (method === 'POST' && url === '/auth/logout') {
      return {
        data: null as T,
        status: 200,
        message: 'Logout successful',
        success: true,
      };
    }

    // Default response
    return {
      data: null as T,
      status: 404,
      message: 'Auth endpoint not found in mock API',
      success: false,
    };
  }

  // Mock customer endpoints
  private mockCustomerEndpoints<T>(
    method: string,
    url: string,
    data?: any
  ): ApiResponse<T> {
    // Handle get all customers
    if (method === 'GET' && url === '/customers') {
      return {
        data: mockData.customers as unknown as T,
        status: 200,
        message: 'Customers retrieved successfully',
        success: true,
      };
    }

    // Default response
    return {
      data: null as T,
      status: 404,
      message: 'Customer endpoint not found in mock API',
      success: false,
    };
  }

  // Mock order endpoints
  private mockOrderEndpoints<T>(
    method: string,
    url: string,
    data?: any
  ): ApiResponse<T> {
    // Handle get all orders
    if (method === 'GET' && url === '/orders') {
      return {
        data: mockData.orders as unknown as T,
        status: 200,
        message: 'Orders retrieved successfully',
        success: true,
      };
    }

    // Default response
    return {
      data: null as T,
      status: 404,
      message: 'Order endpoint not found in mock API',
      success: false,
    };
  }

  // Mock inventory endpoints
  private mockInventoryEndpoints<T>(
    method: string,
    url: string,
    data?: any
  ): ApiResponse<T> {
    // Handle get all inventory items
    if (method === 'GET' && url === '/inventory') {
      return {
        data: mockData.inventory as unknown as T,
        status: 200,
        message: 'Inventory items retrieved successfully',
        success: true,
      };
    }

    // Default response
    return {
      data: null as T,
      status: 404,
      message: 'Inventory endpoint not found in mock API',
      success: false,
    };
  }

  // GET request
  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    // Use mock API if enabled
    if (this.useMockApi) {
      return this.mockApiResponse<T>('GET', url);
    }

    return this.client.get(url, config);
  }

  // POST request
  public async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    // Use mock API if enabled
    if (this.useMockApi) {
      return this.mockApiResponse<T>('POST', url, data);
    }

    return this.client.post(url, data, config);
  }

  // PUT request
  public async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    // Use mock API if enabled
    if (this.useMockApi) {
      return this.mockApiResponse<T>('PUT', url, data);
    }

    return this.client.put(url, data, config);
  }

  // PATCH request
  public async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    // Use mock API if enabled
    if (this.useMockApi) {
      return this.mockApiResponse<T>('PATCH', url, data);
    }

    return this.client.patch(url, data, config);
  }

  // DELETE request
  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    // Use mock API if enabled
    if (this.useMockApi) {
      return this.mockApiResponse<T>('DELETE', url);
    }

    return this.client.delete(url, config);
  }
}

// Export singleton instance
export const apiClient = ApiClient.getInstance();

// Export default for convenience
export default apiClient;
