import apiClient, { ApiResponse } from './client';

// Define interfaces
export interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  is_active: boolean;
  total_orders: number;
  total_spent: number;
  created_at: string;
  updated_at: string;
  measurements?: CustomerMeasurements;
}

export interface CustomerMeasurements {
  id: number;
  customer_id: number;
  chest?: number;
  waist?: number;
  hips?: number;
  inseam?: number;
  sleeve?: number;
  shoulder?: number;
  neck?: number;
  thigh?: number;
  calf?: number;
  ankle?: number;
  arm_length?: number;
  wrist?: number;
  height?: number;
  weight?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CustomerCreateData {
  name: string;
  email: string;
  phone: string;
  address: string;
  measurements?: Partial<CustomerMeasurements>;
}

export interface CustomerUpdateData {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  is_active?: boolean;
  measurements?: Partial<CustomerMeasurements>;
}

export interface CustomerFilters {
  search?: string;
  status?: 'active' | 'inactive' | null;
  sort_by?: string;
  sort_direction?: 'asc' | 'desc';
  page?: number;
  per_page?: number;
}

// Customer service class
class CustomerService {
  // Get all customers
  async getCustomers(filters: CustomerFilters = {}): Promise<ApiResponse<Customer[]>> {
    return apiClient.get('/customers', { params: filters });
  }
  
  // Get customer by ID
  async getCustomer(id: number): Promise<Customer> {
    const response = await apiClient.get<Customer>(`/customers/${id}`);
    return response.data;
  }
  
  // Create customer
  async createCustomer(data: CustomerCreateData): Promise<Customer> {
    const response = await apiClient.post<Customer>('/customers', data);
    return response.data;
  }
  
  // Update customer
  async updateCustomer(id: number, data: CustomerUpdateData): Promise<Customer> {
    const response = await apiClient.put<Customer>(`/customers/${id}`, data);
    return response.data;
  }
  
  // Delete customer
  async deleteCustomer(id: number): Promise<ApiResponse> {
    return apiClient.delete(`/customers/${id}`);
  }
  
  // Get customer measurements
  async getCustomerMeasurements(customerId: number): Promise<CustomerMeasurements> {
    const response = await apiClient.get<CustomerMeasurements>(`/customers/${customerId}/measurements`);
    return response.data;
  }
  
  // Update customer measurements
  async updateCustomerMeasurements(
    customerId: number, 
    data: Partial<CustomerMeasurements>
  ): Promise<CustomerMeasurements> {
    const response = await apiClient.put<CustomerMeasurements>(
      `/customers/${customerId}/measurements`, 
      data
    );
    return response.data;
  }
  
  // Get customer orders
  async getCustomerOrders(customerId: number): Promise<ApiResponse> {
    return apiClient.get(`/customers/${customerId}/orders`);
  }
  
  // Get customer statistics
  async getCustomerStats(): Promise<ApiResponse> {
    return apiClient.get('/customers/stats');
  }
}

// Export singleton instance
export const customerService = new CustomerService();

// Export default for convenience
export default customerService;
