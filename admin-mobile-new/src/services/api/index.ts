// Export API client
export { default as apiClient } from './client';

// Export services
export { default as authService } from './authService';
export { default as customerService } from './customerService';
export { default as orderService } from './orderService';
export { default as inventoryService } from './inventoryService';

// Export types
export type { ApiResponse } from './client';
export type { 
  LoginCredentials, 
  RegisterData, 
  User, 
  AuthResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
  ProfileUpdateData,
  PasswordChangeData
} from './authService';

export type {
  Customer,
  CustomerMeasurements,
  CustomerCreateData,
  CustomerUpdateData,
  CustomerFilters
} from './customerService';

export type {
  Order,
  OrderStatus,
  OrderItem,
  OrderPayment,
  OrderCreateData,
  OrderUpdateData,
  OrderFilters,
  OrderItemCreateData,
  OrderItemUpdateData,
  OrderPaymentCreateData
} from './orderService';

export type {
  InventoryItem,
  InventoryItemCreateData,
  InventoryItemUpdateData,
  InventoryAdjustment,
  InventoryAdjustmentData,
  InventoryFilters
} from './inventoryService';
