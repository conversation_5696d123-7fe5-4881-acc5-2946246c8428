import apiClient, { ApiResponse } from './client';

// Define interfaces
export interface InventoryItem {
  id: number;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  unit_price: number;
  reorder_level: number;
  supplier?: string;
  location?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface InventoryItemCreateData {
  name: string;
  category: string;
  quantity: number;
  unit: string;
  unit_price: number;
  reorder_level: number;
  supplier?: string;
  location?: string;
  description?: string;
}

export interface InventoryItemUpdateData {
  name?: string;
  category?: string;
  quantity?: number;
  unit?: string;
  unit_price?: number;
  reorder_level?: number;
  supplier?: string;
  location?: string;
  description?: string;
}

export interface InventoryAdjustment {
  id: number;
  inventory_item_id: number;
  quantity_before: number;
  quantity_after: number;
  adjustment: number;
  reason: string;
  created_by: number;
  created_at: string;
}

export interface InventoryAdjustmentData {
  quantity: number;
  reason: string;
}

export interface InventoryFilters {
  search?: string;
  category?: string;
  stock_status?: 'in_stock' | 'low_stock' | 'out_of_stock' | null;
  sort_by?: string;
  sort_direction?: 'asc' | 'desc';
  page?: number;
  per_page?: number;
}

// Inventory service class
class InventoryService {
  // Get all inventory items
  async getInventoryItems(filters: InventoryFilters = {}): Promise<ApiResponse<InventoryItem[]>> {
    return apiClient.get('/inventory', { params: filters });
  }
  
  // Get inventory item by ID
  async getInventoryItem(id: number): Promise<InventoryItem> {
    const response = await apiClient.get<InventoryItem>(`/inventory/${id}`);
    return response.data;
  }
  
  // Create inventory item
  async createInventoryItem(data: InventoryItemCreateData): Promise<InventoryItem> {
    const response = await apiClient.post<InventoryItem>('/inventory', data);
    return response.data;
  }
  
  // Update inventory item
  async updateInventoryItem(id: number, data: InventoryItemUpdateData): Promise<InventoryItem> {
    const response = await apiClient.put<InventoryItem>(`/inventory/${id}`, data);
    return response.data;
  }
  
  // Delete inventory item
  async deleteInventoryItem(id: number): Promise<ApiResponse> {
    return apiClient.delete(`/inventory/${id}`);
  }
  
  // Adjust inventory quantity
  async adjustInventory(
    id: number, 
    data: InventoryAdjustmentData
  ): Promise<InventoryItem> {
    const response = await apiClient.post<InventoryItem>(
      `/inventory/${id}/adjust`, 
      data
    );
    return response.data;
  }
  
  // Get inventory adjustment history
  async getInventoryAdjustments(
    id: number
  ): Promise<ApiResponse<InventoryAdjustment[]>> {
    return apiClient.get(`/inventory/${id}/adjustments`);
  }
  
  // Get inventory categories
  async getCategories(): Promise<ApiResponse<string[]>> {
    return apiClient.get('/inventory/categories');
  }
  
  // Get inventory statistics
  async getInventoryStats(): Promise<ApiResponse> {
    return apiClient.get('/inventory/stats');
  }
  
  // Get low stock items
  async getLowStockItems(): Promise<ApiResponse<InventoryItem[]>> {
    return apiClient.get('/inventory/low-stock');
  }
}

// Export singleton instance
export const inventoryService = new InventoryService();

// Export default for convenience
export default inventoryService;
