import { User, AuthResponse } from './authService';

// Mock user data
export const mockUsers: User[] = [
  {
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    is_active: true,
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
  },
  {
    id: 2,
    name: 'Manager User',
    email: '<EMAIL>',
    role: 'manager',
    is_active: true,
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
  },
  {
    id: 3,
    name: 'Employee User',
    email: '<EMAIL>',
    role: 'employee',
    is_active: true,
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
  },
];

// Mock auth responses
export const mockAuthResponses: Record<string, AuthResponse> = {
  '<EMAIL>': {
    user: mockUsers[0],
    token: 'mock-admin-token',
  },
  '<EMAIL>': {
    user: mockUsers[1],
    token: 'mock-manager-token',
  },
  '<EMAIL>': {
    user: mockUsers[2],
    token: 'mock-employee-token',
  },
};

// Mock customers data
export const mockCustomers = [
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main St, New York, NY',
    totalOrders: 5,
    totalSpent: 2500,
    status: 'active',
    measurements: {
      chest: 42,
      waist: 36,
      hips: 40,
      inseam: 32,
      sleeve: 25,
    },
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Elm St, Los Angeles, CA',
    totalOrders: 3,
    totalSpent: 1800,
    status: 'active',
    measurements: {
      chest: 36,
      waist: 28,
      hips: 38,
      inseam: 30,
      sleeve: 22,
    },
  },
  {
    id: 3,
    name: 'Robert Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Oak St, Chicago, IL',
    totalOrders: 7,
    totalSpent: 3200,
    status: 'active',
    measurements: {
      chest: 44,
      waist: 38,
      hips: 42,
      inseam: 34,
      sleeve: 26,
    },
  },
  {
    id: 4,
    name: 'Emily Davis',
    email: '<EMAIL>',
    phone: '+****************',
    address: '321 Pine St, San Francisco, CA',
    totalOrders: 2,
    totalSpent: 950,
    status: 'inactive',
    measurements: {
      chest: 34,
      waist: 26,
      hips: 36,
      inseam: 28,
      sleeve: 21,
    },
  },
  {
    id: 5,
    name: 'Michael Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '654 Maple St, Boston, MA',
    totalOrders: 4,
    totalSpent: 1750,
    status: 'active',
    measurements: {
      chest: 40,
      waist: 34,
      hips: 38,
      inseam: 32,
      sleeve: 24,
    },
  },
];

// Mock orders data
export const mockOrders = [
  {
    id: 1,
    customer_id: 1,
    customer_name: 'John Doe',
    order_number: 'ORD-001',
    status: 'completed',
    total: 550,
    payment_status: 'paid',
    created_at: '2023-05-15T10:30:00.000Z',
    updated_at: '2023-05-20T14:20:00.000Z',
    items: [
      {
        id: 1,
        name: 'Custom Suit',
        quantity: 1,
        price: 450,
        total: 450,
      },
      {
        id: 2,
        name: 'Shirt Alteration',
        quantity: 2,
        price: 50,
        total: 100,
      },
    ],
  },
  {
    id: 2,
    customer_id: 2,
    customer_name: 'Jane Smith',
    order_number: 'ORD-002',
    status: 'in_progress',
    total: 320,
    payment_status: 'partial',
    created_at: '2023-06-20T09:15:00.000Z',
    updated_at: '2023-06-22T11:45:00.000Z',
    items: [
      {
        id: 3,
        name: 'Evening Dress',
        quantity: 1,
        price: 320,
        total: 320,
      },
    ],
  },
  {
    id: 3,
    customer_id: 3,
    customer_name: 'Robert Johnson',
    order_number: 'ORD-003',
    status: 'pending',
    total: 150,
    payment_status: 'unpaid',
    created_at: '2023-07-05T16:20:00.000Z',
    updated_at: '2023-07-05T16:20:00.000Z',
    items: [
      {
        id: 4,
        name: 'Pants Hemming',
        quantity: 3,
        price: 50,
        total: 150,
      },
    ],
  },
];

// Mock inventory data
export const mockInventory = [
  {
    id: 1,
    name: 'Black Wool Fabric',
    category: 'fabric',
    quantity: 50,
    unit: 'meters',
    price: 25,
    status: 'in_stock',
    created_at: '2023-01-10T08:30:00.000Z',
    updated_at: '2023-06-15T14:20:00.000Z',
  },
  {
    id: 2,
    name: 'White Cotton Fabric',
    category: 'fabric',
    quantity: 30,
    unit: 'meters',
    price: 15,
    status: 'in_stock',
    created_at: '2023-02-05T10:15:00.000Z',
    updated_at: '2023-06-10T09:30:00.000Z',
  },
  {
    id: 3,
    name: 'Metal Buttons',
    category: 'accessory',
    quantity: 200,
    unit: 'pieces',
    price: 0.5,
    status: 'in_stock',
    created_at: '2023-03-12T11:45:00.000Z',
    updated_at: '2023-05-20T16:10:00.000Z',
  },
  {
    id: 4,
    name: 'Zippers',
    category: 'accessory',
    quantity: 100,
    unit: 'pieces',
    price: 1.2,
    status: 'low_stock',
    created_at: '2023-04-18T14:20:00.000Z',
    updated_at: '2023-07-01T10:45:00.000Z',
  },
  {
    id: 5,
    name: 'Silk Lining',
    category: 'fabric',
    quantity: 20,
    unit: 'meters',
    price: 30,
    status: 'in_stock',
    created_at: '2023-05-22T09:10:00.000Z',
    updated_at: '2023-06-28T15:30:00.000Z',
  },
];

export default {
  users: mockUsers,
  auth: mockAuthResponses,
  customers: mockCustomers,
  orders: mockOrders,
  inventory: mockInventory,
};
