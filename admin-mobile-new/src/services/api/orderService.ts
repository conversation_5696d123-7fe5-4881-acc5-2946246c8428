import apiClient, { ApiResponse } from './client';

// Define interfaces
export interface Order {
  id: number;
  order_number: string;
  customer_id: number;
  status: OrderStatus;
  amount: number;
  due_date: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  customer: {
    id: number;
    name: string;
    email: string;
    phone: string;
  };
  items: OrderItem[];
  payments: OrderPayment[];
}

export type OrderStatus = 'pending' | 'in-progress' | 'completed' | 'cancelled';

export interface OrderItem {
  id: number;
  order_id: number;
  name: string;
  description?: string;
  quantity: number;
  price: number;
  inventory_item_id?: number;
  created_at: string;
  updated_at: string;
}

export interface OrderPayment {
  id: number;
  order_id: number;
  amount: number;
  method: string;
  reference?: string;
  date: string;
  created_at: string;
  updated_at: string;
}

export interface OrderCreateData {
  customer_id: number;
  due_date: string;
  notes?: string;
  items: {
    name: string;
    description?: string;
    quantity: number;
    price: number;
    inventory_item_id?: number;
  }[];
  payments?: {
    amount: number;
    method: string;
    reference?: string;
    date: string;
  }[];
}

export interface OrderUpdateData {
  customer_id?: number;
  status?: OrderStatus;
  due_date?: string;
  notes?: string;
}

export interface OrderFilters {
  search?: string;
  status?: OrderStatus | null;
  customer_id?: number;
  date_from?: string;
  date_to?: string;
  sort_by?: string;
  sort_direction?: 'asc' | 'desc';
  page?: number;
  per_page?: number;
}

export interface OrderItemCreateData {
  name: string;
  description?: string;
  quantity: number;
  price: number;
  inventory_item_id?: number;
}

export interface OrderItemUpdateData {
  name?: string;
  description?: string;
  quantity?: number;
  price?: number;
}

export interface OrderPaymentCreateData {
  amount: number;
  method: string;
  reference?: string;
  date: string;
}

// Order service class
class OrderService {
  // Get all orders
  async getOrders(filters: OrderFilters = {}): Promise<ApiResponse<Order[]>> {
    return apiClient.get('/orders', { params: filters });
  }
  
  // Get order by ID
  async getOrder(id: number): Promise<Order> {
    const response = await apiClient.get<Order>(`/orders/${id}`);
    return response.data;
  }
  
  // Create order
  async createOrder(data: OrderCreateData): Promise<Order> {
    const response = await apiClient.post<Order>('/orders', data);
    return response.data;
  }
  
  // Update order
  async updateOrder(id: number, data: OrderUpdateData): Promise<Order> {
    const response = await apiClient.put<Order>(`/orders/${id}`, data);
    return response.data;
  }
  
  // Delete order
  async deleteOrder(id: number): Promise<ApiResponse> {
    return apiClient.delete(`/orders/${id}`);
  }
  
  // Update order status
  async updateOrderStatus(id: number, status: OrderStatus): Promise<Order> {
    const response = await apiClient.patch<Order>(`/orders/${id}/status`, { status });
    return response.data;
  }
  
  // Add order item
  async addOrderItem(orderId: number, data: OrderItemCreateData): Promise<OrderItem> {
    const response = await apiClient.post<OrderItem>(`/orders/${orderId}/items`, data);
    return response.data;
  }
  
  // Update order item
  async updateOrderItem(
    orderId: number, 
    itemId: number, 
    data: OrderItemUpdateData
  ): Promise<OrderItem> {
    const response = await apiClient.put<OrderItem>(
      `/orders/${orderId}/items/${itemId}`, 
      data
    );
    return response.data;
  }
  
  // Delete order item
  async deleteOrderItem(orderId: number, itemId: number): Promise<ApiResponse> {
    return apiClient.delete(`/orders/${orderId}/items/${itemId}`);
  }
  
  // Add order payment
  async addOrderPayment(orderId: number, data: OrderPaymentCreateData): Promise<OrderPayment> {
    const response = await apiClient.post<OrderPayment>(`/orders/${orderId}/payments`, data);
    return response.data;
  }
  
  // Delete order payment
  async deleteOrderPayment(orderId: number, paymentId: number): Promise<ApiResponse> {
    return apiClient.delete(`/orders/${orderId}/payments/${paymentId}`);
  }
  
  // Get order statistics
  async getOrderStats(): Promise<ApiResponse> {
    return apiClient.get('/orders/stats');
  }
}

// Export singleton instance
export const orderService = new OrderService();

// Export default for convenience
export default orderService;
