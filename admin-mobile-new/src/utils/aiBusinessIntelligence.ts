import { Report, ReportType, ReportData } from './reports';
import TrendAnalysisUtil from './trendAnalysis';

// Define AI Business Intelligence types
export interface BusinessInsight {
  id: string;
  type: 'opportunity' | 'risk' | 'optimization' | 'prediction' | 'anomaly';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: number; // 0-100 scale
  confidence: number; // 0-100 scale
  actionItems: string[];
  estimatedValue: number;
  timeframe: string;
  category: string;
  createdAt: Date;
  expiresAt?: Date;
}

export interface AIRecommendation {
  id: string;
  type: 'strategic' | 'operational' | 'tactical' | 'immediate';
  title: string;
  description: string;
  reasoning: string[];
  expectedOutcome: string;
  implementationSteps: string[];
  requiredResources: string[];
  timeline: string;
  riskLevel: 'low' | 'medium' | 'high';
  potentialROI: number;
  confidence: number;
  priority: number;
}

export interface MarketIntelligence {
  marketTrends: {
    direction: 'growing' | 'declining' | 'stable';
    strength: number;
    factors: string[];
  };
  competitivePosition: {
    ranking: number;
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    threats: string[];
  };
  customerBehavior: {
    segments: Array<{
      name: string;
      size: number;
      characteristics: string[];
      trends: string[];
    }>;
    preferences: string[];
    satisfactionScore: number;
  };
}

export interface PredictiveModel {
  modelType: 'linear' | 'polynomial' | 'exponential' | 'neural';
  accuracy: number;
  predictions: Array<{
    period: string;
    value: number;
    confidence: number;
    factors: string[];
  }>;
  scenarios: Array<{
    name: string;
    probability: number;
    impact: number;
    description: string;
  }>;
}

class AIBusinessIntelligence {
  private static instance: AIBusinessIntelligence;
  private trendAnalysis: TrendAnalysisUtil;
  private insights: BusinessInsight[] = [];
  private recommendations: AIRecommendation[] = [];
  private knowledgeBase: Map<string, any> = new Map();

  private constructor() {
    this.trendAnalysis = TrendAnalysisUtil.getInstance();
    this.initializeKnowledgeBase();
  }

  public static getInstance(): AIBusinessIntelligence {
    if (!AIBusinessIntelligence.instance) {
      AIBusinessIntelligence.instance = new AIBusinessIntelligence();
    }
    return AIBusinessIntelligence.instance;
  }

  /**
   * Analyze business data and generate AI insights
   */
  public async analyzeBusinessData(reports: Report[]): Promise<{
    insights: BusinessInsight[];
    recommendations: AIRecommendation[];
    marketIntelligence: MarketIntelligence;
    predictiveModel: PredictiveModel;
  }> {
    if (reports.length < 5) {
      throw new Error('Insufficient data for AI analysis. Need at least 5 reports.');
    }

    // Generate insights
    const insights = await this.generateInsights(reports);

    // Generate recommendations
    const recommendations = await this.generateRecommendations(reports, insights);

    // Analyze market intelligence
    const marketIntelligence = await this.analyzeMarketIntelligence(reports);

    // Build predictive model
    const predictiveModel = await this.buildPredictiveModel(reports);

    // Store results
    this.insights = insights;
    this.recommendations = recommendations;

    return {
      insights,
      recommendations,
      marketIntelligence,
      predictiveModel,
    };
  }

  /**
   * Generate business insights using AI algorithms
   */
  private async generateInsights(reports: Report[]): Promise<BusinessInsight[]> {
    const insights: BusinessInsight[] = [];

    // Analyze revenue patterns
    const revenueInsights = this.analyzeRevenuePatterns(reports);
    insights.push(...revenueInsights);

    // Analyze customer behavior
    const customerInsights = this.analyzeCustomerBehavior(reports);
    insights.push(...customerInsights);

    // Detect anomalies
    const anomalyInsights = this.detectBusinessAnomalies(reports);
    insights.push(...anomalyInsights);

    // Identify opportunities
    const opportunityInsights = this.identifyOpportunities(reports);
    insights.push(...opportunityInsights);

    // Assess risks
    const riskInsights = this.assessBusinessRisks(reports);
    insights.push(...riskInsights);

    return insights.sort((a, b) => b.priority.localeCompare(a.priority) || b.impact - a.impact);
  }

  /**
   * Analyze revenue patterns and trends
   */
  private analyzeRevenuePatterns(reports: Report[]): BusinessInsight[] {
    const insights: BusinessInsight[] = [];
    const salesReports = reports.filter(r => r.config.type === ReportType.SALES);

    if (salesReports.length < 3) return insights;

    const values = salesReports.map(r => r.data.summary?.total || 0);
    const trend = this.trendAnalysis.calculateTrend(values);
    const volatility = this.trendAnalysis.calculateVolatility(values);

    // Revenue growth insight
    if (trend.changePercent > 15) {
      insights.push({
        id: `revenue_growth_${Date.now()}`,
        type: 'opportunity',
        priority: 'high',
        title: 'Strong Revenue Growth Detected',
        description: `Revenue has increased by ${trend.changePercent.toFixed(1)}% with high confidence (${trend.confidence.toFixed(0)}%).`,
        impact: Math.min(90, trend.changePercent * 2),
        confidence: trend.confidence,
        actionItems: [
          'Scale successful marketing campaigns',
          'Increase inventory for high-demand items',
          'Consider expanding to new markets',
          'Optimize pricing strategy for maximum profit'
        ],
        estimatedValue: trend.current * 0.2,
        timeframe: 'Next 30 days',
        category: 'Revenue',
        createdAt: new Date(),
      });
    }

    // Revenue volatility warning
    if (volatility.volatilityIndex > 40) {
      insights.push({
        id: `revenue_volatility_${Date.now()}`,
        type: 'risk',
        priority: 'medium',
        title: 'High Revenue Volatility Detected',
        description: `Revenue shows high volatility (${volatility.volatilityIndex.toFixed(1)}%), indicating potential instability.`,
        impact: volatility.volatilityIndex,
        confidence: 85,
        actionItems: [
          'Diversify revenue streams',
          'Implement more predictable pricing models',
          'Focus on customer retention',
          'Analyze and address volatility causes'
        ],
        estimatedValue: trend.current * 0.1,
        timeframe: 'Next 60 days',
        category: 'Risk Management',
        createdAt: new Date(),
      });
    }

    return insights;
  }

  /**
   * Analyze customer behavior patterns
   */
  private analyzeCustomerBehavior(reports: Report[]): BusinessInsight[] {
    const insights: BusinessInsight[] = [];
    const customerReports = reports.filter(r => r.config.type === ReportType.CUSTOMERS);

    if (customerReports.length < 3) return insights;

    const values = customerReports.map(r => r.data.summary?.total || 0);
    const trend = this.trendAnalysis.calculateTrend(values);

    // Customer acquisition insight
    if (trend.changePercent > 10) {
      insights.push({
        id: `customer_acquisition_${Date.now()}`,
        type: 'opportunity',
        priority: 'high',
        title: 'Accelerating Customer Acquisition',
        description: `Customer base is growing at ${trend.changePercent.toFixed(1)}% rate. This indicates strong market appeal.`,
        impact: 80,
        confidence: trend.confidence,
        actionItems: [
          'Analyze successful acquisition channels',
          'Increase marketing budget for top-performing channels',
          'Implement referral programs',
          'Optimize onboarding process'
        ],
        estimatedValue: trend.current * 50, // Assume average customer value
        timeframe: 'Next 45 days',
        category: 'Customer Growth',
        createdAt: new Date(),
      });
    }

    return insights;
  }

  /**
   * Detect business anomalies
   */
  private detectBusinessAnomalies(reports: Report[]): BusinessInsight[] {
    const insights: BusinessInsight[] = [];

    reports.forEach(report => {
      const values = report.data.datasets[0].data;
      const anomalies = this.trendAnalysis.detectAnomalies(values, 2);

      if (anomalies.length > 0) {
        insights.push({
          id: `anomaly_${report.id}_${Date.now()}`,
          type: 'anomaly',
          priority: 'medium',
          title: `Unusual Pattern in ${report.config.type.toUpperCase()}`,
          description: `Detected ${anomalies.length} anomalous data points that deviate significantly from normal patterns.`,
          impact: 60,
          confidence: 75,
          actionItems: [
            'Investigate root causes of anomalies',
            'Check data quality and collection methods',
            'Review business processes during anomaly periods',
            'Implement monitoring for early detection'
          ],
          estimatedValue: 0,
          timeframe: 'Immediate',
          category: 'Data Quality',
          createdAt: new Date(),
        });
      }
    });

    return insights;
  }

  /**
   * Identify business opportunities
   */
  private identifyOpportunities(reports: Report[]): BusinessInsight[] {
    const insights: BusinessInsight[] = [];

    // Cross-sell opportunity analysis
    const salesReports = reports.filter(r => r.config.type === ReportType.SALES);
    const customerReports = reports.filter(r => r.config.type === ReportType.CUSTOMERS);

    if (salesReports.length > 0 && customerReports.length > 0) {
      const avgSalesPerCustomer = this.calculateAverageSalesPerCustomer(salesReports, customerReports);

      if (avgSalesPerCustomer < 1000) { // Threshold for cross-sell opportunity
        insights.push({
          id: `crosssell_opportunity_${Date.now()}`,
          type: 'opportunity',
          priority: 'medium',
          title: 'Cross-sell Opportunity Identified',
          description: `Average sales per customer is ${avgSalesPerCustomer.toFixed(0)}. There's potential to increase this through cross-selling.`,
          impact: 70,
          confidence: 80,
          actionItems: [
            'Analyze customer purchase patterns',
            'Develop product bundles and packages',
            'Implement recommendation system',
            'Train staff on cross-selling techniques'
          ],
          estimatedValue: avgSalesPerCustomer * 0.3,
          timeframe: 'Next 90 days',
          category: 'Sales Optimization',
          createdAt: new Date(),
        });
      }
    }

    return insights;
  }

  /**
   * Assess business risks
   */
  private assessBusinessRisks(reports: Report[]): BusinessInsight[] {
    const insights: BusinessInsight[] = [];

    // Inventory risk analysis
    const inventoryReports = reports.filter(r => r.config.type === ReportType.INVENTORY);

    if (inventoryReports.length > 0) {
      const latestInventory = inventoryReports[inventoryReports.length - 1];
      const inventoryTrend = this.trendAnalysis.calculateTrend(
        inventoryReports.map(r => r.data.summary?.total || 0)
      );

      if (inventoryTrend.trend === 'down' && Math.abs(inventoryTrend.changePercent) > 20) {
        insights.push({
          id: `inventory_risk_${Date.now()}`,
          type: 'risk',
          priority: 'high',
          title: 'Inventory Depletion Risk',
          description: `Inventory levels are declining rapidly (${inventoryTrend.changePercent.toFixed(1)}%). Risk of stockouts.`,
          impact: 85,
          confidence: inventoryTrend.confidence,
          actionItems: [
            'Review and adjust reorder points',
            'Accelerate supplier orders',
            'Implement automated inventory alerts',
            'Analyze demand forecasting accuracy'
          ],
          estimatedValue: latestInventory.data.summary?.total || 0 * 0.1,
          timeframe: 'Next 14 days',
          category: 'Inventory Management',
          createdAt: new Date(),
        });
      }
    }

    return insights;
  }

  /**
   * Generate AI-powered recommendations
   */
  private async generateRecommendations(reports: Report[], insights: BusinessInsight[]): Promise<AIRecommendation[]> {
    const recommendations: AIRecommendation[] = [];

    // Strategic recommendations based on insights
    const highImpactInsights = insights.filter(i => i.impact > 70);

    highImpactInsights.forEach(insight => {
      const recommendation = this.generateRecommendationFromInsight(insight);
      if (recommendation) {
        recommendations.push(recommendation);
      }
    });

    // Operational efficiency recommendations
    const efficiencyRecs = this.generateEfficiencyRecommendations(reports);
    recommendations.push(...efficiencyRecs);

    return recommendations.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Generate recommendation from insight
   */
  private generateRecommendationFromInsight(insight: BusinessInsight): AIRecommendation | null {
    const baseRec = {
      id: `rec_${insight.id}`,
      confidence: insight.confidence,
      potentialROI: insight.estimatedValue,
    };

    switch (insight.type) {
      case 'opportunity':
        return {
          ...baseRec,
          type: 'strategic',
          title: `Capitalize on ${insight.category} Opportunity`,
          description: `Leverage the identified opportunity in ${insight.category.toLowerCase()} to drive business growth.`,
          reasoning: [
            `High impact potential (${insight.impact}/100)`,
            `Strong confidence level (${insight.confidence.toFixed(0)}%)`,
            `Estimated value: ${insight.estimatedValue.toLocaleString()}`
          ],
          expectedOutcome: `Increase ${insight.category.toLowerCase()} performance by 15-25%`,
          implementationSteps: insight.actionItems,
          requiredResources: ['Management time', 'Budget allocation', 'Team coordination'],
          timeline: insight.timeframe,
          riskLevel: 'low',
          priority: insight.priority === 'high' ? 90 : 70,
        };

      case 'risk':
        return {
          ...baseRec,
          type: 'immediate',
          title: `Mitigate ${insight.category} Risk`,
          description: `Address the identified risk in ${insight.category.toLowerCase()} to prevent potential losses.`,
          reasoning: [
            `High risk impact (${insight.impact}/100)`,
            `Requires immediate attention`,
            `Potential loss prevention: ${insight.estimatedValue.toLocaleString()}`
          ],
          expectedOutcome: `Reduce risk exposure and prevent potential losses`,
          implementationSteps: insight.actionItems,
          requiredResources: ['Immediate action', 'Resource reallocation', 'Process changes'],
          timeline: 'Immediate to 2 weeks',
          riskLevel: 'high',
          priority: 95,
        };

      default:
        return null;
    }
  }

  /**
   * Generate operational efficiency recommendations
   */
  private generateEfficiencyRecommendations(reports: Report[]): AIRecommendation[] {
    const recommendations: AIRecommendation[] = [];

    // Process optimization recommendation
    recommendations.push({
      id: `efficiency_process_${Date.now()}`,
      type: 'operational',
      title: 'Optimize Business Processes',
      description: 'Implement process improvements to increase operational efficiency and reduce costs.',
      reasoning: [
        'Data analysis reveals optimization opportunities',
        'Process improvements can reduce operational costs by 10-15%',
        'Enhanced efficiency leads to better customer satisfaction'
      ],
      expectedOutcome: 'Reduce operational costs by 10-15% while improving service quality',
      implementationSteps: [
        'Conduct process mapping and analysis',
        'Identify bottlenecks and inefficiencies',
        'Implement automation where possible',
        'Train staff on optimized processes',
        'Monitor and measure improvements'
      ],
      requiredResources: ['Process analysis tools', 'Staff training', 'Technology upgrades'],
      timeline: '60-90 days',
      riskLevel: 'low',
      potentialROI: 15,
      confidence: 80,
      priority: 75,
    });

    return recommendations;
  }

  /**
   * Analyze market intelligence
   */
  private async analyzeMarketIntelligence(reports: Report[]): Promise<MarketIntelligence> {
    // Simulate market intelligence analysis
    return {
      marketTrends: {
        direction: 'growing',
        strength: 75,
        factors: [
          'Increasing demand for custom tailoring',
          'Growing fashion consciousness',
          'Digital transformation in retail',
          'Quality over quantity trend'
        ]
      },
      competitivePosition: {
        ranking: 2,
        strengths: [
          'Strong customer relationships',
          'Quality craftsmanship',
          'Digital presence',
          'Efficient operations'
        ],
        weaknesses: [
          'Limited marketing reach',
          'Pricing pressure',
          'Seasonal fluctuations'
        ],
        opportunities: [
          'Online expansion',
          'Corporate partnerships',
          'Premium service offerings',
          'Technology integration'
        ],
        threats: [
          'Economic uncertainty',
          'Competition from fast fashion',
          'Supply chain disruptions',
          'Changing consumer preferences'
        ]
      },
      customerBehavior: {
        segments: [
          {
            name: 'Premium Customers',
            size: 30,
            characteristics: ['High spending', 'Quality focused', 'Brand loyal'],
            trends: ['Increasing frequency', 'Premium service demand']
          },
          {
            name: 'Regular Customers',
            size: 50,
            characteristics: ['Moderate spending', 'Value conscious', 'Seasonal buyers'],
            trends: ['Price sensitivity', 'Digital engagement']
          },
          {
            name: 'Occasional Customers',
            size: 20,
            characteristics: ['Low frequency', 'Event-driven', 'Price sensitive'],
            trends: ['Promotional response', 'Social media influence']
          }
        ],
        preferences: [
          'Quick turnaround time',
          'Competitive pricing',
          'Quality materials',
          'Personalized service',
          'Digital convenience'
        ],
        satisfactionScore: 85
      }
    };
  }

  /**
   * Build predictive model
   */
  private async buildPredictiveModel(reports: Report[]): Promise<PredictiveModel> {
    const salesData = reports
      .filter(r => r.config.type === ReportType.SALES)
      .map(r => r.data.summary?.total || 0);

    const trend = this.trendAnalysis.calculateTrend(salesData);

    return {
      modelType: 'linear',
      accuracy: trend.confidence,
      predictions: [
        {
          period: 'Next Month',
          value: trend.forecast[0] || trend.current,
          confidence: trend.confidence,
          factors: ['Historical trends', 'Seasonal patterns', 'Market conditions']
        },
        {
          period: 'Next Quarter',
          value: (trend.forecast[1] || trend.current) * 3,
          confidence: Math.max(50, trend.confidence - 20),
          factors: ['Long-term trends', 'Economic indicators', 'Business strategy']
        }
      ],
      scenarios: [
        {
          name: 'Optimistic',
          probability: 25,
          impact: 120,
          description: 'Strong market growth and successful initiatives'
        },
        {
          name: 'Most Likely',
          probability: 50,
          impact: 100,
          description: 'Current trends continue with normal variations'
        },
        {
          name: 'Conservative',
          probability: 25,
          impact: 80,
          description: 'Market challenges and slower growth'
        }
      ]
    };
  }

  /**
   * Initialize knowledge base with business rules
   */
  private initializeKnowledgeBase(): void {
    this.knowledgeBase.set('revenue_growth_threshold', 10);
    this.knowledgeBase.set('volatility_warning_threshold', 30);
    this.knowledgeBase.set('customer_acquisition_threshold', 5);
    this.knowledgeBase.set('inventory_risk_threshold', 20);
    this.knowledgeBase.set('crosssell_threshold', 1000);
  }

  /**
   * Helper method to calculate average sales per customer
   */
  private calculateAverageSalesPerCustomer(salesReports: Report[], customerReports: Report[]): number {
    const totalSales = salesReports.reduce((sum, report) => sum + (report.data.summary?.total || 0), 0);
    const totalCustomers = customerReports.reduce((sum, report) => sum + (report.data.summary?.total || 0), 0);

    return totalCustomers > 0 ? totalSales / totalCustomers : 0;
  }

  /**
   * Get current insights
   */
  public getCurrentInsights(): BusinessInsight[] {
    return [...this.insights];
  }

  /**
   * Get current recommendations
   */
  public getCurrentRecommendations(): AIRecommendation[] {
    return [...this.recommendations];
  }

  /**
   * Update knowledge base
   */
  public updateKnowledgeBase(key: string, value: any): void {
    this.knowledgeBase.set(key, value);
  }

  /**
   * Export AI analysis results
   */
  public exportAnalysis(): {
    insights: BusinessInsight[];
    recommendations: AIRecommendation[];
    knowledgeBase: Record<string, any>;
    timestamp: Date;
  } {
    return {
      insights: this.getCurrentInsights(),
      recommendations: this.getCurrentRecommendations(),
      knowledgeBase: Object.fromEntries(this.knowledgeBase),
      timestamp: new Date(),
    };
  }
}

export default AIBusinessIntelligence;
