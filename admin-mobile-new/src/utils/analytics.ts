import * as Amplitude from 'expo-analytics-amplitude';
import { Platform } from 'react-native';
import { isExpoGo, isDevelopment } from './environment';
import StorageUtil from './storage';

// Define analytics event types
export type AnalyticsEventType =
  // Auth events
  | 'login'
  | 'logout'
  | 'signup'
  | 'password_reset'
  | 'password_change'
  | 'profile_update'

  // Navigation events
  | 'screen_view'
  | 'tab_change'
  | 'modal_open'
  | 'modal_close'

  // Customer events
  | 'customer_create'
  | 'customer_update'
  | 'customer_delete'
  | 'customer_view'
  | 'customer_search'

  // Order events
  | 'order_create'
  | 'order_update'
  | 'order_delete'
  | 'order_status_change'
  | 'order_payment'
  | 'order_view'
  | 'order_search'

  // Inventory events
  | 'inventory_create'
  | 'inventory_update'
  | 'inventory_delete'
  | 'inventory_adjust'
  | 'inventory_view'
  | 'inventory_search'

  // App events
  | 'app_start'
  | 'app_background'
  | 'app_foreground'
  | 'app_crash'
  | 'app_error'

  // Feature usage events
  | 'feature_use'
  | 'setting_change'
  | 'notification_receive'
  | 'notification_open'
  | 'search'
  | 'filter'
  | 'sort'
  | 'export'
  | 'import'
  | 'share'
  | 'print'
  | 'download'
  | 'upload';

// Define analytics user properties
export interface AnalyticsUserProperties {
  userId?: string | number;
  email?: string;
  name?: string;
  role?: string;
  language?: string;
  theme?: string;
  deviceId?: string;
  appVersion?: string;
  osVersion?: string;
  deviceModel?: string;
  [key: string]: any;
}

// Define analytics event properties
export interface AnalyticsEventProperties {
  [key: string]: any;
}

// Define analytics configuration
interface AnalyticsConfig {
  amplitudeApiKey: string;
  enabled: boolean;
  debug: boolean;
}

// Default analytics configuration
const DEFAULT_CONFIG: AnalyticsConfig = {
  amplitudeApiKey: 'YOUR_AMPLITUDE_API_KEY', // Replace with your actual API key
  enabled: true,
  debug: __DEV__,
};

// Storage keys
const ANALYTICS_ENABLED_KEY = 'analytics_enabled';
const ANALYTICS_USER_ID_KEY = 'analytics_user_id';

/**
 * Analytics utility class for tracking user behavior
 */
class AnalyticsUtil {
  private static instance: AnalyticsUtil;
  private config: AnalyticsConfig;
  private isInitialized: boolean = false;
  private userId: string | null = null;
  private sessionId: string | null = null;
  private userProperties: AnalyticsUserProperties = {};

  private constructor(config: Partial<AnalyticsConfig> = {}) {
    // Merge default config with provided config
    this.config = {
      ...DEFAULT_CONFIG,
      ...config,
    };

    // Disable analytics in development if not explicitly enabled
    if (isDevelopment() && config.enabled === undefined) {
      this.config.enabled = false;
    }
  }

  /**
   * Get the singleton instance
   * @param config Analytics configuration
   * @returns AnalyticsUtil instance
   */
  public static getInstance(config: Partial<AnalyticsConfig> = {}): AnalyticsUtil {
    if (!AnalyticsUtil.instance) {
      AnalyticsUtil.instance = new AnalyticsUtil(config);
    }
    return AnalyticsUtil.instance;
  }

  /**
   * Initialize analytics
   * @returns Promise that resolves when analytics is initialized
   */
  public async init(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Check if analytics is enabled
      const enabled = await this.isEnabled();
      if (!enabled) {
        console.log('Analytics is disabled');
        return;
      }

      // Initialize Amplitude
      try {
        // Check if Amplitude is available
        if (typeof Amplitude === 'undefined' || !Amplitude || typeof Amplitude.initializeAsync !== 'function') {
          console.warn('Amplitude is not available, skipping initialization');
          return;
        }

        await Amplitude.initializeAsync(this.config.amplitudeApiKey);
      } catch (error) {
        console.warn('Error initializing Amplitude:', error);
        // Continue without Amplitude
      }

      // Set debug mode
      try {
        if (this.config.debug) {
          await Amplitude.setLogLevelAsync(Amplitude.LogLevel.DEBUG);
        }
      } catch (error) {
        console.warn('Error setting Amplitude log level:', error);
        // Continue without setting log level
      }

      // Set device info
      await this.setDeviceInfo();

      // Get user ID from storage
      try {
        const userId = await StorageUtil.retrieve<string>(ANALYTICS_USER_ID_KEY);
        if (userId) {
          this.userId = userId;
          await Amplitude.setUserIdAsync(userId);
        }
      } catch (error) {
        console.warn('Error setting user ID:', error);
        // Continue without setting user ID
      }

      // Generate session ID
      this.sessionId = this.generateSessionId();

      // Track app start
      try {
        await this.trackEvent('app_start', {
          sessionId: this.sessionId,
        });
      } catch (error) {
        console.warn('Error tracking app start:', error);
        // Continue without tracking app start
      }

      this.isInitialized = true;

      if (this.config.debug) {
        console.log('Analytics initialized');
      }
    } catch (error) {
      console.error('Error initializing analytics:', error);
    }
  }

  /**
   * Check if analytics is enabled
   * @returns Promise that resolves to a boolean indicating if analytics is enabled
   */
  public async isEnabled(): Promise<boolean> {
    try {
      // Check if running in Expo Go
      if (isExpoGo()) {
        console.log('Analytics is disabled in Expo Go');
        return false;
      }

      // Check if enabled in config
      if (!this.config.enabled) {
        return false;
      }

      // Check if enabled in storage
      const enabled = await StorageUtil.retrieve<boolean>(ANALYTICS_ENABLED_KEY);
      return enabled !== false; // Default to true if not set
    } catch (error) {
      console.error('Error checking if analytics is enabled:', error);
      return false;
    }
  }

  /**
   * Enable or disable analytics
   * @param enabled Whether analytics should be enabled
   * @returns Promise that resolves when the setting is saved
   */
  public async setEnabled(enabled: boolean): Promise<void> {
    try {
      await StorageUtil.store(ANALYTICS_ENABLED_KEY, enabled);

      if (this.config.debug) {
        console.log(`Analytics ${enabled ? 'enabled' : 'disabled'}`);
      }

      // Initialize if enabled and not already initialized
      if (enabled && !this.isInitialized) {
        await this.init();
      }
    } catch (error) {
      console.error('Error setting analytics enabled:', error);
    }
  }

  /**
   * Set user ID
   * @param userId User ID
   * @returns Promise that resolves when the user ID is set
   */
  public async setUserId(userId: string | number): Promise<void> {
    try {
      const userIdStr = userId.toString();

      // Save user ID
      this.userId = userIdStr;
      await StorageUtil.store(ANALYTICS_USER_ID_KEY, userIdStr);

      // Set user ID in Amplitude
      if (this.isInitialized) {
        try {
          await Amplitude.setUserIdAsync(userIdStr);

          if (this.config.debug) {
            console.log(`Analytics user ID set: ${userIdStr}`);
          }
        } catch (error) {
          console.warn('Error setting user ID in Amplitude:', error);
          // Continue without setting user ID
        }
      }
    } catch (error) {
      console.error('Error setting analytics user ID:', error);
    }
  }

  /**
   * Clear user ID
   * @returns Promise that resolves when the user ID is cleared
   */
  public async clearUserId(): Promise<void> {
    try {
      // Clear user ID
      this.userId = null;
      await StorageUtil.remove(ANALYTICS_USER_ID_KEY);

      // Clear user ID in Amplitude
      if (this.isInitialized) {
        try {
          await Amplitude.clearUserPropertiesAsync();
          await Amplitude.setUserIdAsync(null);

          if (this.config.debug) {
            console.log('Analytics user ID cleared');
          }
        } catch (error) {
          console.warn('Error clearing user ID in Amplitude:', error);
          // Continue without clearing user ID
        }
      }
    } catch (error) {
      console.error('Error clearing analytics user ID:', error);
    }
  }

  /**
   * Set user properties
   * @param properties User properties
   * @returns Promise that resolves when the user properties are set
   */
  public async setUserProperties(properties: AnalyticsUserProperties): Promise<void> {
    try {
      // Save user properties
      this.userProperties = {
        ...this.userProperties,
        ...properties,
      };

      // Set user properties in Amplitude
      if (this.isInitialized) {
        try {
          await Amplitude.setUserPropertiesAsync(properties);

          if (this.config.debug) {
            console.log('Analytics user properties set:', properties);
          }
        } catch (error) {
          console.warn('Error setting user properties in Amplitude:', error);
          // Continue without setting user properties
        }
      }
    } catch (error) {
      console.error('Error setting analytics user properties:', error);
    }
  }

  /**
   * Track event
   * @param eventType Event type
   * @param properties Event properties
   * @returns Promise that resolves when the event is tracked
   */
  public async trackEvent(
    eventType: AnalyticsEventType,
    properties: AnalyticsEventProperties = {}
  ): Promise<void> {
    try {
      // Check if analytics is enabled
      const enabled = await this.isEnabled();
      if (!enabled) {
        return;
      }

      // Initialize if not already initialized
      if (!this.isInitialized) {
        await this.init();
      }

      // Add session ID to properties
      const eventProperties = {
        ...properties,
        sessionId: this.sessionId,
      };

      // Track event in Amplitude
      try {
        await Amplitude.logEventWithPropertiesAsync(eventType, eventProperties);
      } catch (error) {
        console.warn(`Error tracking event ${eventType} in Amplitude:`, error);
        // Continue without tracking event
      }

      if (this.config.debug) {
        console.log(`Analytics event tracked: ${eventType}`, eventProperties);
      }
    } catch (error) {
      console.error(`Error tracking analytics event ${eventType}:`, error);
    }
  }

  /**
   * Set device info
   * @returns Promise that resolves when device info is set
   */
  private async setDeviceInfo(): Promise<void> {
    try {
      // Set platform
      await this.setUserProperties({
        platform: Platform.OS,
        platformVersion: Platform.Version,
      });
    } catch (error) {
      console.error('Error setting device info:', error);
    }
  }

  /**
   * Generate session ID
   * @returns Session ID
   */
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  }
}

// Export singleton instance
export default AnalyticsUtil.getInstance();
