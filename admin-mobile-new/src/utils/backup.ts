import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as DocumentPicker from 'expo-document-picker';
import { Platform } from 'react-native';
import StorageUtil from './storage';

// Define backup data structure
export interface BackupData {
  timestamp: number;
  version: string;
  data: Record<string, any>;
}

// Define backup metadata
export interface BackupMetadata {
  timestamp: number;
  version: string;
  size: number;
  itemCount: number;
}

// Define backup options
export interface BackupOptions {
  includeSettings?: boolean;
  includeUserData?: boolean;
  includeCustomers?: boolean;
  includeOrders?: boolean;
  includeInventory?: boolean;
}

// Define restore options
export interface RestoreOptions {
  overwriteExisting?: boolean;
  restoreSettings?: boolean;
  restoreUserData?: boolean;
  restoreCustomers?: boolean;
  restoreOrders?: boolean;
  restoreInventory?: boolean;
}

// Define backup storage keys
const BACKUP_DIRECTORY = `${FileSystem.documentDirectory}backups/`;
const LAST_BACKUP_KEY = 'last_backup_metadata';

/**
 * Backup utility class for handling data backup and restore
 */
class BackupUtil {
  private static instance: BackupUtil;

  private constructor() {
    this.init();
  }

  /**
   * Get the singleton instance
   * @returns BackupUtil instance
   */
  public static getInstance(): BackupUtil {
    if (!BackupUtil.instance) {
      BackupUtil.instance = new BackupUtil();
    }
    return BackupUtil.instance;
  }

  /**
   * Initialize backup utility
   */
  private async init(): Promise<void> {
    try {
      // Create backup directory if it doesn't exist
      const dirInfo = await FileSystem.getInfoAsync(BACKUP_DIRECTORY);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(BACKUP_DIRECTORY, { intermediates: true });
      }
    } catch (error) {
      console.error('Error initializing backup utility:', error);
    }
  }

  /**
   * Create a backup of the app data
   * @param options Backup options
   * @returns Promise that resolves to backup metadata
   */
  public async createBackup(options: BackupOptions = {}): Promise<BackupMetadata> {
    try {
      // Set default options
      const backupOptions: Required<BackupOptions> = {
        includeSettings: options.includeSettings !== false,
        includeUserData: options.includeUserData !== false,
        includeCustomers: options.includeCustomers !== false,
        includeOrders: options.includeOrders !== false,
        includeInventory: options.includeInventory !== false,
      };

      // Get all keys from AsyncStorage
      const allKeys = await AsyncStorage.getAllKeys();

      // Filter keys based on options
      const keysToBackup = allKeys.filter(key => {
        if (!backupOptions.includeSettings && key.startsWith('setting_')) {
          return false;
        }
        if (!backupOptions.includeUserData && (key === 'user' || key === 'auth_token')) {
          return false;
        }
        if (!backupOptions.includeCustomers && key.startsWith('customer_')) {
          return false;
        }
        if (!backupOptions.includeOrders && key.startsWith('order_')) {
          return false;
        }
        if (!backupOptions.includeInventory && key.startsWith('inventory_')) {
          return false;
        }
        return true;
      });

      // Get data for each key
      const data: Record<string, any> = {};
      for (const key of keysToBackup) {
        const value = await AsyncStorage.getItem(key);
        if (value !== null) {
          try {
            // Try to parse JSON
            data[key] = JSON.parse(value);
          } catch (e) {
            // If not JSON, store as string
            data[key] = value;
          }
        }
      }

      // Create backup data
      const timestamp = Date.now();
      const version = '1.0.0'; // App version
      const backupData: BackupData = {
        timestamp,
        version,
        data,
      };

      // Convert to JSON
      const backupJson = JSON.stringify(backupData);

      // Create backup file
      const backupFileName = `backup_${timestamp}.json`;
      const backupFilePath = `${BACKUP_DIRECTORY}${backupFileName}`;

      // Write backup file
      await FileSystem.writeAsStringAsync(backupFilePath, backupJson, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Create backup metadata
      const metadata: BackupMetadata = {
        timestamp,
        version,
        size: backupJson.length,
        itemCount: Object.keys(data).length,
      };

      // Save last backup metadata
      await StorageUtil.store(LAST_BACKUP_KEY, metadata);

      return metadata;
    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  /**
   * Restore a backup from a file
   * @param filePath Path to backup file
   * @param options Restore options
   * @returns Promise that resolves to backup metadata
   */
  public async restoreBackup(
    filePath: string,
    options: RestoreOptions = {}
  ): Promise<BackupMetadata> {
    try {
      // Set default options
      const restoreOptions: Required<RestoreOptions> = {
        overwriteExisting: options.overwriteExisting !== false,
        restoreSettings: options.restoreSettings !== false,
        restoreUserData: options.restoreUserData !== false,
        restoreCustomers: options.restoreCustomers !== false,
        restoreOrders: options.restoreOrders !== false,
        restoreInventory: options.restoreInventory !== false,
      };

      // Read backup file
      const backupJson = await FileSystem.readAsStringAsync(filePath, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Parse backup data
      const backupData: BackupData = JSON.parse(backupJson);

      // Get existing keys
      const existingKeys = await AsyncStorage.getAllKeys();

      // Restore data
      for (const [key, value] of Object.entries(backupData.data)) {
        // Skip based on options
        if (!restoreOptions.restoreSettings && key.startsWith('setting_')) {
          continue;
        }
        if (!restoreOptions.restoreUserData && (key === 'user' || key === 'auth_token')) {
          continue;
        }
        if (!restoreOptions.restoreCustomers && key.startsWith('customer_')) {
          continue;
        }
        if (!restoreOptions.restoreOrders && key.startsWith('order_')) {
          continue;
        }
        if (!restoreOptions.restoreInventory && key.startsWith('inventory_')) {
          continue;
        }

        // Skip if key exists and overwriteExisting is false
        if (!restoreOptions.overwriteExisting && existingKeys.includes(key)) {
          continue;
        }

        // Store value
        if (typeof value === 'string') {
          await AsyncStorage.setItem(key, value);
        } else {
          await AsyncStorage.setItem(key, JSON.stringify(value));
        }
      }

      // Create backup metadata
      const metadata: BackupMetadata = {
        timestamp: backupData.timestamp,
        version: backupData.version,
        size: backupJson.length,
        itemCount: Object.keys(backupData.data).length,
      };

      return metadata;
    } catch (error) {
      console.error('Error restoring backup:', error);
      throw error;
    }
  }

  /**
   * Get the last backup metadata
   * @returns Promise that resolves to backup metadata or null if no backup exists
   */
  public async getLastBackupMetadata(): Promise<BackupMetadata | null> {
    try {
      return await StorageUtil.retrieve<BackupMetadata>(LAST_BACKUP_KEY);
    } catch (error) {
      console.error('Error getting last backup metadata:', error);
      return null;
    }
  }

  /**
   * Get all backups
   * @returns Promise that resolves to an array of backup file paths
   */
  public async getAllBackups(): Promise<string[]> {
    try {
      const backupFiles = await FileSystem.readDirectoryAsync(BACKUP_DIRECTORY);
      return backupFiles
        .filter(file => file.endsWith('.json'))
        .map(file => `${BACKUP_DIRECTORY}${file}`);
    } catch (error) {
      console.error('Error getting all backups:', error);
      return [];
    }
  }

  /**
   * Share a backup file
   * @param filePath Path to backup file
   * @returns Promise that resolves when the file is shared
   */
  public async shareBackup(filePath: string): Promise<void> {
    try {
      // Check if sharing is available
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        throw new Error('Sharing is not available on this device');
      }

      // Share file
      await Sharing.shareAsync(filePath, {
        mimeType: 'application/json',
        dialogTitle: 'Share Backup',
        UTI: 'public.json',
      });
    } catch (error) {
      console.error('Error sharing backup:', error);
      throw error;
    }
  }

  /**
   * Import a backup file
   * @returns Promise that resolves to the imported file path
   */
  public async importBackup(): Promise<string> {
    try {
      // Pick document
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/json',
        copyToCacheDirectory: true,
      });

      // Check if document was picked
      if (result.canceled) {
        throw new Error('Document picking was canceled');
      }

      // Get file URI
      const fileUri = result.assets[0].uri;

      // Copy file to backup directory
      const fileName = `backup_imported_${Date.now()}.json`;
      const destUri = `${BACKUP_DIRECTORY}${fileName}`;

      await FileSystem.copyAsync({
        from: fileUri,
        to: destUri,
      });

      return destUri;
    } catch (error) {
      console.error('Error importing backup:', error);
      throw error;
    }
  }

  /**
   * Delete a backup file
   * @param filePath Path to backup file
   * @returns Promise that resolves when the file is deleted
   */
  public async deleteBackup(filePath: string): Promise<void> {
    try {
      await FileSystem.deleteAsync(filePath);
    } catch (error) {
      console.error('Error deleting backup:', error);
      throw error;
    }
  }

  /**
   * Format backup date
   * @param timestamp Backup timestamp
   * @returns Formatted date string
   */
  public formatBackupDate(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toLocaleString();
  }
}

// Export singleton instance
export default BackupUtil.getInstance();
