import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';

// Define constants
const BIOMETRICS_ENABLED_KEY = 'biometrics_enabled';
const BIOMETRICS_USERNAME_KEY = 'biometrics_username';
const BIOMETRICS_PASSWORD_KEY = 'biometrics_password';

/**
 * Biometrics utility class for handling biometric authentication
 */
class BiometricsUtil {
  /**
   * Check if biometric authentication is available on the device
   * @returns Promise that resolves to a boolean indicating if biometrics is available
   */
  static async isBiometricsAvailable(): Promise<boolean> {
    try {
      const isHardwareSupported = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      
      return isHardwareSupported && isEnrolled;
    } catch (error) {
      console.error('Error checking biometrics availability:', error);
      return false;
    }
  }
  
  /**
   * Check if biometric authentication is enabled for the app
   * @returns Promise that resolves to a boolean indicating if biometrics is enabled
   */
  static async isBiometricsEnabled(): Promise<boolean> {
    try {
      const value = await SecureStore.getItemAsync(BIOMETRICS_ENABLED_KEY);
      return value === 'true';
    } catch (error) {
      console.error('Error checking if biometrics is enabled:', error);
      return false;
    }
  }
  
  /**
   * Enable biometric authentication for the app
   * @param username Username to store
   * @param password Password to store
   * @returns Promise that resolves to a boolean indicating if biometrics was enabled
   */
  static async enableBiometrics(username: string, password: string): Promise<boolean> {
    try {
      // Check if biometrics is available
      const isAvailable = await this.isBiometricsAvailable();
      if (!isAvailable) {
        return false;
      }
      
      // Authenticate with biometrics
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to enable biometric login',
        fallbackLabel: 'Use passcode',
      });
      
      if (!result.success) {
        return false;
      }
      
      // Store credentials in secure storage
      await SecureStore.setItemAsync(BIOMETRICS_USERNAME_KEY, username);
      await SecureStore.setItemAsync(BIOMETRICS_PASSWORD_KEY, password);
      await SecureStore.setItemAsync(BIOMETRICS_ENABLED_KEY, 'true');
      
      return true;
    } catch (error) {
      console.error('Error enabling biometrics:', error);
      return false;
    }
  }
  
  /**
   * Disable biometric authentication for the app
   * @returns Promise that resolves when biometrics is disabled
   */
  static async disableBiometrics(): Promise<void> {
    try {
      // Remove credentials from secure storage
      await SecureStore.deleteItemAsync(BIOMETRICS_USERNAME_KEY);
      await SecureStore.deleteItemAsync(BIOMETRICS_PASSWORD_KEY);
      await SecureStore.setItemAsync(BIOMETRICS_ENABLED_KEY, 'false');
    } catch (error) {
      console.error('Error disabling biometrics:', error);
    }
  }
  
  /**
   * Authenticate with biometrics
   * @returns Promise that resolves to an object with username and password if authentication is successful
   */
  static async authenticateWithBiometrics(): Promise<{ username: string; password: string } | null> {
    try {
      // Check if biometrics is enabled
      const isEnabled = await this.isBiometricsEnabled();
      if (!isEnabled) {
        return null;
      }
      
      // Authenticate with biometrics
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to log in',
        fallbackLabel: 'Use passcode',
      });
      
      if (!result.success) {
        return null;
      }
      
      // Get credentials from secure storage
      const username = await SecureStore.getItemAsync(BIOMETRICS_USERNAME_KEY);
      const password = await SecureStore.getItemAsync(BIOMETRICS_PASSWORD_KEY);
      
      if (!username || !password) {
        return null;
      }
      
      return { username, password };
    } catch (error) {
      console.error('Error authenticating with biometrics:', error);
      return null;
    }
  }
  
  /**
   * Get available biometric authentication types
   * @returns Promise that resolves to an array of biometric authentication types
   */
  static async getBiometricTypes(): Promise<LocalAuthentication.AuthenticationType[]> {
    try {
      return await LocalAuthentication.supportedAuthenticationTypesAsync();
    } catch (error) {
      console.error('Error getting biometric types:', error);
      return [];
    }
  }
  
  /**
   * Get biometric authentication type name
   * @param type Biometric authentication type
   * @returns String representing the biometric type
   */
  static getBiometricTypeName(type: LocalAuthentication.AuthenticationType): string {
    switch (type) {
      case LocalAuthentication.AuthenticationType.FINGERPRINT:
        return 'Fingerprint';
      case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
        return 'Face ID';
      case LocalAuthentication.AuthenticationType.IRIS:
        return 'Iris';
      default:
        return 'Biometric';
    }
  }
}

export default BiometricsUtil;
