import NetInfo, { NetInfoState, NetInfoSubscription } from '@react-native-community/netinfo';

// Define network info type
export interface NetworkInfo {
  isConnected: boolean;
  connectionType: string | null;
  isInternetReachable: boolean | null;
  isWifi: boolean;
  isCellular: boolean;
  isOfflineMode: boolean;
  details: any;
  timestamp: number;
}

// Define network speed type
export enum NetworkSpeed {
  UNKNOWN = 'unknown',
  SLOW = 'slow',
  MEDIUM = 'medium',
  FAST = 'fast',
}

/**
 * Enhanced Network utility class for handling online/offline status with advanced features
 */
class EnhancedNetworkUtil {
  private static isConnected: boolean = true;
  private static isInternetReachable: boolean | null = null;
  private static connectionType: string | null = null;
  private static isWifi: boolean = false;
  private static isCellular: boolean = false;
  private static isOfflineMode: boolean = false;
  private static networkDetails: any = null;
  private static lastChecked: number = 0;
  private static networkSpeed: NetworkSpeed = NetworkSpeed.UNKNOWN;
  private static listeners: Array<(networkInfo: NetworkInfo) => void> = [];
  private static subscription: NetInfoSubscription | null = null;
  private static checkInterval: NodeJS.Timeout | null = null;
  private static retryCount: number = 0;
  private static maxRetries: number = 3;
  private static retryDelay: number = 5000; // 5 seconds

  /**
   * Initialize network monitoring
   */
  static init(): void {
    // Subscribe to network state changes
    this.subscription = NetInfo.addEventListener(this.handleNetInfoChange);

    // Get initial network state
    NetInfo.fetch().then(this.handleNetInfoChange);

    // Set up periodic network check
    this.startPeriodicCheck();
  }

  /**
   * Start periodic network check
   */
  private static startPeriodicCheck(): void {
    // Check network every 30 seconds
    this.checkInterval = setInterval(() => {
      this.fetchNetworkInfo();
    }, 30000);
  }

  /**
   * Stop periodic network check
   */
  private static stopPeriodicCheck(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Clean up network monitoring
   */
  static cleanup(): void {
    this.stopPeriodicCheck();
    
    if (this.subscription) {
      this.subscription();
      this.subscription = null;
    }
    
    this.listeners = [];
  }

  /**
   * Handle network state changes
   * @param state Network state
   */
  private static handleNetInfoChange = (state: NetInfoState): void => {
    const wasConnected = this.isConnected;
    const wasReachable = this.isInternetReachable;
    
    this.isConnected = state.isConnected === true;
    this.isInternetReachable = state.isInternetReachable;
    this.connectionType = state.type;
    this.isWifi = state.type === 'wifi';
    this.isCellular = state.type === 'cellular';
    this.networkDetails = state.details;
    this.lastChecked = Date.now();

    // Determine network speed based on connection type
    this.updateNetworkSpeed();

    // Notify listeners if connection state changed
    if (wasConnected !== this.isConnected || wasReachable !== this.isInternetReachable) {
      this.notifyListeners();
    }

    // If connection was restored, retry failed requests
    if (!wasConnected && this.isConnected && this.isInternetReachable) {
      this.retryFailedRequests();
    }
  };

  /**
   * Update network speed based on connection type
   */
  private static updateNetworkSpeed(): void {
    if (!this.isConnected) {
      this.networkSpeed = NetworkSpeed.UNKNOWN;
      return;
    }

    if (this.isWifi) {
      this.networkSpeed = NetworkSpeed.FAST;
    } else if (this.isCellular) {
      // Check cellular generation
      const cellularGen = this.networkDetails?.cellularGeneration;
      
      if (cellularGen === '4g' || cellularGen === '5g') {
        this.networkSpeed = NetworkSpeed.FAST;
      } else if (cellularGen === '3g') {
        this.networkSpeed = NetworkSpeed.MEDIUM;
      } else {
        this.networkSpeed = NetworkSpeed.SLOW;
      }
    } else {
      this.networkSpeed = NetworkSpeed.UNKNOWN;
    }
  }

  /**
   * Retry failed requests
   */
  private static retryFailedRequests(): void {
    // This would be implemented in the sync queue
    console.log('Network restored, retrying failed requests');
    
    // Dispatch an event that the network is back online
    const event = new CustomEvent('network:online', {
      detail: this.getNetworkInfo()
    });
    
    // Dispatch the event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(event);
    }
  }

  /**
   * Fetch network info
   */
  private static async fetchNetworkInfo(): Promise<void> {
    try {
      const state = await NetInfo.fetch();
      
      this.isConnected = state.isConnected === true;
      this.isInternetReachable = state.isInternetReachable;
      this.connectionType = state.type;
      this.isWifi = state.type === 'wifi';
      this.isCellular = state.type === 'cellular';
      this.networkDetails = state.details;
      this.lastChecked = Date.now();

      // Update network speed
      this.updateNetworkSpeed();

      // Notify listeners
      this.notifyListeners();
    } catch (error) {
      console.error('Error fetching network info:', error);
      
      // Retry if failed
      this.retryFetchNetworkInfo();
    }
  }

  /**
   * Retry fetching network info
   */
  private static retryFetchNetworkInfo(): void {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      
      setTimeout(() => {
        this.fetchNetworkInfo();
      }, this.retryDelay);
    } else {
      this.retryCount = 0;
      console.error('Max retries reached for fetching network info');
    }
  }

  /**
   * Notify all listeners of network state changes
   */
  private static notifyListeners(): void {
    const networkInfo = this.getNetworkInfo();
    
    this.listeners.forEach(listener => {
      try {
        listener(networkInfo);
      } catch (error) {
        console.error('Error in network listener:', error);
      }
    });
  }

  /**
   * Get network info
   * @returns Network info object
   */
  static getNetworkInfo(): NetworkInfo {
    return {
      isConnected: this.isConnected,
      connectionType: this.connectionType,
      isInternetReachable: this.isInternetReachable,
      isWifi: this.isWifi,
      isCellular: this.isCellular,
      isOfflineMode: this.isOfflineMode,
      details: this.networkDetails,
      timestamp: this.lastChecked,
    };
  }

  /**
   * Check if the device is currently connected to the internet
   * @returns Promise that resolves to a boolean indicating connection status
   */
  static async isNetworkAvailable(): Promise<boolean> {
    try {
      const state = await NetInfo.fetch();
      return state.isConnected === true && state.isInternetReachable !== false;
    } catch (error) {
      console.error('Error checking network status:', error);
      return false;
    }
  }

  /**
   * Check if internet is reachable
   * @returns Promise that resolves to a boolean indicating if internet is reachable
   */
  static async isInternetAvailable(): Promise<boolean> {
    try {
      const state = await NetInfo.fetch();
      return state.isInternetReachable === true;
    } catch (error) {
      console.error('Error checking internet availability:', error);
      return false;
    }
  }

  /**
   * Get the current connection status
   * @returns Boolean indicating if the device is connected
   */
  static getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Get connection type
   * @returns Connection type
   */
  static getConnectionType(): string | null {
    return this.connectionType;
  }

  /**
   * Get network speed
   * @returns Network speed
   */
  static getNetworkSpeed(): NetworkSpeed {
    return this.networkSpeed;
  }

  /**
   * Enable offline mode
   */
  static enableOfflineMode(): void {
    this.isOfflineMode = true;
    this.notifyListeners();
  }

  /**
   * Disable offline mode
   */
  static disableOfflineMode(): void {
    this.isOfflineMode = false;
    this.notifyListeners();
  }

  /**
   * Check if offline mode is enabled
   * @returns Boolean indicating if offline mode is enabled
   */
  static isOfflineModeEnabled(): boolean {
    return this.isOfflineMode;
  }

  /**
   * Add a listener for network status changes
   * @param listener Function to call when network status changes
   * @returns Function to remove the listener
   */
  static addListener(listener: (networkInfo: NetworkInfo) => void): () => void {
    this.listeners.push(listener);

    // Immediately notify the new listener of the current status
    try {
      listener(this.getNetworkInfo());
    } catch (error) {
      console.error('Error in network listener:', error);
    }

    // Return a function to remove this listener
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }
}

export default EnhancedNetworkUtil;
