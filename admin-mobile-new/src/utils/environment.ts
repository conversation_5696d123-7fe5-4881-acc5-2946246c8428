import Constants from 'expo-constants';
import { Platform } from 'react-native';

/**
 * Check if the app is running in Expo Go
 * @returns Boolean indicating if the app is running in Expo Go
 */
export const isExpoGo = (): boolean => {
  const { executionEnvironment } = Constants;
  return executionEnvironment === 'storeClient';
};

/**
 * Check if the app is running in development
 * @returns Boolean indicating if the app is running in development
 */
export const isDevelopment = (): boolean => {
  return __DEV__;
};

/**
 * Check if the app is running on a physical device
 * @returns Boolean indicating if the app is running on a physical device
 */
export const isPhysicalDevice = (): boolean => {
  return !isExpoGo() && Platform.OS !== 'web';
};

/**
 * Get the platform name
 * @returns String indicating the platform name
 */
export const getPlatformName = (): string => {
  return Platform.OS;
};

/**
 * Get the app version
 * @returns String indicating the app version
 */
export const getAppVersion = (): string => {
  return Constants.expoConfig?.version || '1.0.0';
};

/**
 * Get the build number
 * @returns String indicating the build number
 */
export const getBuildNumber = (): string => {
  if (Platform.OS === 'ios') {
    return Constants.expoConfig?.ios?.buildNumber || '1';
  } else if (Platform.OS === 'android') {
    return Constants.expoConfig?.android?.versionCode?.toString() || '1';
  }
  return '1';
};

/**
 * Get the device name
 * @returns String indicating the device name
 */
export const getDeviceName = (): string => {
  return Platform.OS === 'web' ? 'Web' : Platform.OS === 'ios' ? 'iOS' : 'Android';
};

/**
 * Get the environment name
 * @returns String indicating the environment name
 */
export const getEnvironmentName = (): string => {
  if (isExpoGo()) {
    return 'Expo Go';
  } else if (isDevelopment()) {
    return 'Development';
  } else {
    return 'Production';
  }
};

export default {
  isExpoGo,
  isDevelopment,
  isPhysicalDevice,
  getPlatformName,
  getAppVersion,
  getBuildNumber,
  getDeviceName,
  getEnvironmentName,
};
