import * as Localization from 'expo-localization';
import { I18n } from 'i18n-js';
import StorageUtil from './storage';

// Import translations
import en from '../locales/en.json';
import bn from '../locales/bn.json';

// Define supported languages
export const SUPPORTED_LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    rtl: false,
  },
  bn: {
    code: 'bn',
    name: 'Bengali',
    nativeName: 'বাংলা',
    rtl: false,
  },
};

// Define language type
export type LanguageCode = keyof typeof SUPPORTED_LANGUAGES;

// Define storage key
const LANGUAGE_KEY = 'app_language';

// Create i18n instance
const i18n = new I18n({
  en,
  bn,
});

// Set default locale
i18n.defaultLocale = 'en';

// Set fallback locale
i18n.enableFallback = true;

// Set initial locale
i18n.locale = Localization.locale.split('-')[0];

/**
 * Language utility class for handling translations
 */
class I18nUtil {
  private static instance: I18nUtil;
  private i18n: I18n;
  private currentLocale: LanguageCode = 'en';

  private constructor() {
    this.i18n = i18n;
    this.init();
  }

  /**
   * Get the singleton instance
   * @returns I18nUtil instance
   */
  public static getInstance(): I18nUtil {
    if (!I18nUtil.instance) {
      I18nUtil.instance = new I18nUtil();
    }
    return I18nUtil.instance;
  }

  /**
   * Initialize language
   */
  private async init(): Promise<void> {
    try {
      // Get stored language
      const storedLanguage = await StorageUtil.retrieve<LanguageCode>(LANGUAGE_KEY);

      if (storedLanguage && SUPPORTED_LANGUAGES[storedLanguage]) {
        // Use stored language
        this.setLocale(storedLanguage);
      } else {
        // Use device language if supported
        const deviceLanguage = Localization.locale.split('-')[0] as LanguageCode;

        if (SUPPORTED_LANGUAGES[deviceLanguage]) {
          this.setLocale(deviceLanguage);
        } else {
          // Fallback to English
          this.setLocale('en');
        }
      }
    } catch (error) {
      console.error('Error initializing language:', error);
      // Fallback to English
      this.setLocale('en');
    }
  }

  /**
   * Set locale
   * @param locale Language code
   */
  public setLocale(locale: LanguageCode): void {
    try {
      // Check if locale is supported
      if (!SUPPORTED_LANGUAGES[locale]) {
        console.warn(`Language ${locale} is not supported, falling back to English`);
        locale = 'en';
      }

      // Set locale
      this.i18n.locale = locale;
      this.currentLocale = locale;

      // Store locale
      StorageUtil.store(LANGUAGE_KEY, locale);

      // Update RTL status
      this.updateRTL();
    } catch (error) {
      console.error('Error setting locale:', error);
    }
  }

  /**
   * Get current locale
   * @returns Current locale
   */
  public getLocale(): LanguageCode {
    return this.currentLocale;
  }

  /**
   * Get language name
   * @param locale Language code
   * @returns Language name
   */
  public getLanguageName(locale: LanguageCode): string {
    return SUPPORTED_LANGUAGES[locale]?.name || 'Unknown';
  }

  /**
   * Get native language name
   * @param locale Language code
   * @returns Native language name
   */
  public getNativeLanguageName(locale: LanguageCode): string {
    return SUPPORTED_LANGUAGES[locale]?.nativeName || 'Unknown';
  }

  /**
   * Check if language is RTL
   * @param locale Language code
   * @returns True if language is RTL
   */
  public isRTL(locale?: LanguageCode): boolean {
    const lang = locale || this.currentLocale;
    return SUPPORTED_LANGUAGES[lang]?.rtl || false;
  }

  /**
   * Update RTL status
   */
  private updateRTL(): void {
    // This method can be used to update the app's RTL status
    // For now, it's just a placeholder
  }

  /**
   * Translate a key
   * @param key Translation key
   * @param options Translation options
   * @returns Translated string
   */
  public t(key: string, options?: Record<string, any>): string {
    return this.i18n.t(key, options);
  }

  /**
   * Get all supported languages
   * @returns Array of supported languages
   */
  public getSupportedLanguages(): Array<{
    code: LanguageCode;
    name: string;
    nativeName: string;
    rtl: boolean;
  }> {
    return Object.values(SUPPORTED_LANGUAGES);
  }
}

// Export singleton instance
export default I18nUtil.getInstance();
