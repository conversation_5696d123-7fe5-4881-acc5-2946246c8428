import NetInfo, { NetInfoState, NetInfoSubscription } from '@react-native-community/netinfo';

/**
 * Network utility class for handling online/offline status
 */
class NetworkUtil {
  private static isConnected: boolean = true;
  private static listeners: Array<(isConnected: boolean) => void> = [];
  private static subscription: NetInfoSubscription | null = null;

  /**
   * Initialize network monitoring
   */
  static init(): void {
    // Subscribe to network state changes
    this.subscription = NetInfo.addEventListener(this.handleNetInfoChange);

    // Get initial network state
    NetInfo.fetch().then(this.handleNetInfoChange);
  }

  /**
   * Clean up network monitoring
   */
  static cleanup(): void {
    if (this.subscription) {
      this.subscription();
      this.subscription = null;
    }
    this.listeners = [];
  }

  /**
   * Handle network state changes
   * @param state Network state
   */
  private static handleNetInfoChange = (state: NetInfoState): void => {
    const connected = state.isConnected === true && state.isInternetReachable !== false;
    
    // Only notify if the connection status has changed
    if (connected !== this.isConnected) {
      this.isConnected = connected;
      this.notifyListeners();
    }
  };

  /**
   * Notify all listeners of connection status change
   */
  private static notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.isConnected);
      } catch (error) {
        console.error('Error in network listener:', error);
      }
    });
  }

  /**
   * Add a listener for network status changes
   * @param listener Function to call when network status changes
   * @returns Function to remove the listener
   */
  static addListener(listener: (isConnected: boolean) => void): () => void {
    this.listeners.push(listener);
    
    // Immediately notify the new listener of the current status
    try {
      listener(this.isConnected);
    } catch (error) {
      console.error('Error in network listener:', error);
    }
    
    // Return a function to remove this listener
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Check if the device is currently connected to the internet
   * @returns Promise that resolves to a boolean indicating connection status
   */
  static async isNetworkAvailable(): Promise<boolean> {
    try {
      const state = await NetInfo.fetch();
      return state.isConnected === true && state.isInternetReachable !== false;
    } catch (error) {
      console.error('Error checking network status:', error);
      return false;
    }
  }

  /**
   * Get the current connection status
   * @returns Boolean indicating if the device is connected
   */
  static getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Get detailed network information
   * @returns Promise that resolves to detailed network state
   */
  static async getNetworkInfo(): Promise<NetInfoState> {
    return await NetInfo.fetch();
  }
}

export default NetworkUtil;
