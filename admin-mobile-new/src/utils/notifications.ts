import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { Platform, Alert } from 'react-native';
import Constants from 'expo-constants';
import { isExpoGo } from './environment';

// Configure notifications
try {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });
} catch (error) {
  console.warn('Error setting notification handler:', error);
}

// Define notification types
export type NotificationType =
  | 'order_new'
  | 'order_update'
  | 'order_completed'
  | 'customer_new'
  | 'inventory_low'
  | 'payment_received'
  | 'reminder'
  | 'system';

export interface NotificationData {
  type: NotificationType;
  title: string;
  body: string;
  data?: Record<string, any>;
}

// This section was moved to the top of the file

/**
 * Notifications utility class for handling push notifications
 */
class NotificationsUtil {
  private static pushToken: string | null = null;
  private static isInitialized: boolean = false;
  private static notificationListener: any = null;
  private static responseListener: any = null;

  /**
   * Initialize notifications
   * @returns Promise that resolves when notifications are initialized
   */
  static async init(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Check if running in Expo Go
      if (isExpoGo()) {
        console.warn('Push notifications are not fully supported in Expo Go');

        // We can still set up local notifications in Expo Go
        this.setupListeners();
        this.isInitialized = true;
        return;
      }

      // Check if device is physical (not simulator/emulator)
      if (!Device.isDevice) {
        console.warn('Push notifications are not available on simulator/emulator');
        return;
      }

      // Request permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Failed to get push token for push notification!');
        return;
      }

      // Get push token (only if not in Expo Go)
      if (!isExpoGo()) {
        try {
          const token = await this.getPushToken();
          this.pushToken = token;
        } catch (error) {
          console.warn('Failed to get push token:', error);
          // Continue without push token
        }
      }

      // Set up notification listeners
      this.setupListeners();

      this.isInitialized = true;
    } catch (error) {
      console.error('Error initializing notifications:', error);
    }
  }

  /**
   * Get push token
   * @returns Promise that resolves to push token
   */
  static async getPushToken(): Promise<string> {
    try {
      // Check if running in Expo Go
      if (isExpoGo()) {
        console.warn('Push tokens are not available in Expo Go');
        return 'expo-go-token-not-available';
      }

      // Get project ID
      const projectId = Constants.expoConfig?.extra?.eas?.projectId;

      // Get push token
      const { data: token } = await Notifications.getExpoPushTokenAsync({
        projectId,
      });

      // Log token for development
      if (__DEV__) {
        console.log('Push token:', token);
      }

      // Return token
      return token;
    } catch (error) {
      console.error('Error getting push token:', error);
      return 'token-error';
    }
  }

  /**
   * Set up notification listeners
   */
  private static setupListeners(): void {
    // Remove existing listeners
    this.removeListeners();

    // Set up notification received listener
    this.notificationListener = Notifications.addNotificationReceivedListener(
      notification => {
        console.log('Notification received:', notification);
      }
    );

    // Set up notification response listener
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      response => {
        console.log('Notification response:', response);
        // Handle notification response (e.g., navigate to screen)
        this.handleNotificationResponse(response);
      }
    );
  }

  /**
   * Remove notification listeners
   */
  private static removeListeners(): void {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
      this.notificationListener = null;
    }

    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
      this.responseListener = null;
    }
  }

  /**
   * Handle notification response
   * @param response Notification response
   */
  private static handleNotificationResponse(
    response: Notifications.NotificationResponse
  ): void {
    const data = response.notification.request.content.data;

    // Handle different notification types
    switch (data.type) {
      case 'order_new':
      case 'order_update':
      case 'order_completed':
        // Navigate to order details
        if (data.orderId) {
          // Navigation would be handled by the app's navigation system
          console.log('Navigate to order:', data.orderId);
        }
        break;

      case 'customer_new':
        // Navigate to customer details
        if (data.customerId) {
          console.log('Navigate to customer:', data.customerId);
        }
        break;

      case 'inventory_low':
        // Navigate to inventory
        console.log('Navigate to inventory');
        break;

      case 'payment_received':
        // Navigate to payment details
        if (data.paymentId) {
          console.log('Navigate to payment:', data.paymentId);
        }
        break;

      default:
        // Default action
        console.log('No specific action for notification type:', data.type);
    }
  }

  /**
   * Schedule a local notification
   * @param notification Notification data
   * @returns Promise that resolves to notification ID
   */
  static async scheduleLocalNotification(
    notification: NotificationData
  ): Promise<string> {
    try {
      // Initialize if not already initialized
      if (!this.isInitialized) {
        await this.init();
      }

      const { type, title, body, data } = notification;

      // Schedule notification
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: { type, ...data },
        },
        trigger: null, // Immediate notification
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling local notification:', error);
      return 'notification-error';
    }
  }

  /**
   * Cancel a scheduled notification
   * @param notificationId Notification ID
   */
  static async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
      throw error;
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  static async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
      throw error;
    }
  }

  /**
   * Get all scheduled notifications
   * @returns Promise that resolves to array of scheduled notifications
   */
  static async getAllScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      throw error;
    }
  }

  /**
   * Get push token
   * @returns Push token or null if not available
   */
  static getToken(): string | null {
    return this.pushToken;
  }

  /**
   * Clean up resources
   */
  static cleanup(): void {
    this.removeListeners();
    this.isInitialized = false;
  }
}

export default NotificationsUtil;
