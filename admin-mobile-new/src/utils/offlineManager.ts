import AsyncStorage from '@react-native-async-storage/async-storage';
import { v4 as uuidv4 } from 'uuid';
import EnhancedNetworkUtil from './enhancedNetwork';
import StorageUtil from './storage';

// Define offline action type
export enum OfflineActionType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  CUSTOM = 'custom',
}

// Define offline action
export interface OfflineAction {
  id: string;
  type: OfflineActionType;
  endpoint: string;
  method: string;
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  priority: number;
  status: 'pending' | 'processing' | 'failed' | 'completed';
  error?: string;
}

// Define offline data
export interface OfflineData {
  [key: string]: any;
}

// Define conflict resolution strategy
export enum ConflictResolutionStrategy {
  CLIENT_WINS = 'client_wins',
  SERVER_WINS = 'server_wins',
  MANUAL = 'manual',
}

// Define conflict
export interface Conflict {
  id: string;
  entityType: string;
  entityId: string;
  clientData: any;
  serverData: any;
  timestamp: number;
  resolved: boolean;
  resolution?: ConflictResolutionStrategy;
  resolvedData?: any;
}

// Storage keys
const OFFLINE_ACTIONS_KEY = 'offline_actions';
const OFFLINE_DATA_KEY = 'offline_data';
const CONFLICTS_KEY = 'offline_conflicts';
const LAST_SYNC_KEY = 'last_sync_timestamp';

/**
 * Offline Manager utility class for handling offline data and synchronization
 */
class OfflineManager {
  private static instance: OfflineManager;
  private offlineActions: OfflineAction[] = [];
  private offlineData: OfflineData = {};
  private conflicts: Conflict[] = [];
  private lastSyncTimestamp: number = 0;
  private isInitialized: boolean = false;
  private isSyncing: boolean = false;
  private syncListeners: Array<(isSyncing: boolean) => void> = [];
  private conflictListeners: Array<(conflicts: Conflict[]) => void> = [];
  private defaultConflictResolution: ConflictResolutionStrategy = ConflictResolutionStrategy.MANUAL;

  private constructor() {
    this.init();
  }

  /**
   * Get the singleton instance
   * @returns OfflineManager instance
   */
  public static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager();
    }
    return OfflineManager.instance;
  }

  /**
   * Initialize offline manager
   */
  private async init(): Promise<void> {
    try {
      // Load offline actions
      const actionsJson = await StorageUtil.retrieve<string>(OFFLINE_ACTIONS_KEY);
      if (actionsJson) {
        this.offlineActions = JSON.parse(actionsJson);
      }

      // Load offline data
      const dataJson = await StorageUtil.retrieve<string>(OFFLINE_DATA_KEY);
      if (dataJson) {
        this.offlineData = JSON.parse(dataJson);
      }

      // Load conflicts
      const conflictsJson = await StorageUtil.retrieve<string>(CONFLICTS_KEY);
      if (conflictsJson) {
        this.conflicts = JSON.parse(conflictsJson);
      }

      // Load last sync timestamp
      const lastSyncStr = await StorageUtil.retrieve<string>(LAST_SYNC_KEY);
      if (lastSyncStr) {
        this.lastSyncTimestamp = parseInt(lastSyncStr, 10);
      }

      // Add event listener for network changes
      // Use React Native's event system instead of window.addEventListener
      // which is not available in React Native

      this.isInitialized = true;
      console.log('Offline Manager initialized');
    } catch (error) {
      console.error('Error initializing offline manager:', error);
    }
  }

  /**
   * Handle network online event
   * This method will be called by the EnhancedNetworkContext when network comes back online
   */
  public handleNetworkOnline = () => {
    // Sync when network comes back online
    this.sync();
  };

  /**
   * Save offline actions to storage
   */
  private async saveOfflineActions(): Promise<void> {
    try {
      await StorageUtil.store(OFFLINE_ACTIONS_KEY, JSON.stringify(this.offlineActions));
    } catch (error) {
      console.error('Error saving offline actions:', error);
    }
  }

  /**
   * Save offline data to storage
   */
  private async saveOfflineData(): Promise<void> {
    try {
      await StorageUtil.store(OFFLINE_DATA_KEY, JSON.stringify(this.offlineData));
    } catch (error) {
      console.error('Error saving offline data:', error);
    }
  }

  /**
   * Save conflicts to storage
   */
  private async saveConflicts(): Promise<void> {
    try {
      await StorageUtil.store(CONFLICTS_KEY, JSON.stringify(this.conflicts));
    } catch (error) {
      console.error('Error saving conflicts:', error);
    }
  }

  /**
   * Save last sync timestamp to storage
   */
  private async saveLastSyncTimestamp(): Promise<void> {
    try {
      await StorageUtil.store(LAST_SYNC_KEY, this.lastSyncTimestamp.toString());
    } catch (error) {
      console.error('Error saving last sync timestamp:', error);
    }
  }

  /**
   * Queue an offline action
   * @param type Action type
   * @param endpoint API endpoint
   * @param method HTTP method
   * @param data Action data
   * @param priority Action priority (higher number = higher priority)
   * @returns Promise that resolves to the queued action
   */
  public async queueAction(
    type: OfflineActionType,
    endpoint: string,
    method: string,
    data: any,
    priority: number = 1
  ): Promise<OfflineAction> {
    const action: OfflineAction = {
      id: uuidv4(),
      type,
      endpoint,
      method,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3,
      priority,
      status: 'pending',
    };

    this.offlineActions.push(action);
    await this.saveOfflineActions();

    // If we're online, try to sync immediately
    if (EnhancedNetworkUtil.getConnectionStatus() && !EnhancedNetworkUtil.isOfflineModeEnabled()) {
      this.sync();
    }

    return action;
  }

  /**
   * Store offline data
   * @param key Data key
   * @param data Data to store
   * @returns Promise that resolves when data is stored
   */
  public async storeData(key: string, data: any): Promise<void> {
    this.offlineData[key] = {
      data,
      timestamp: Date.now(),
    };

    await this.saveOfflineData();
  }

  /**
   * Retrieve offline data
   * @param key Data key
   * @returns Data or null if not found
   */
  public getData(key: string): any {
    const entry = this.offlineData[key];
    return entry ? entry.data : null;
  }

  /**
   * Remove offline data
   * @param key Data key
   * @returns Promise that resolves when data is removed
   */
  public async removeData(key: string): Promise<void> {
    if (this.offlineData[key]) {
      delete this.offlineData[key];
      await this.saveOfflineData();
    }
  }

  /**
   * Get all pending offline actions
   * @returns Array of pending offline actions
   */
  public getPendingActions(): OfflineAction[] {
    return this.offlineActions.filter(action => action.status === 'pending');
  }

  /**
   * Get all failed offline actions
   * @returns Array of failed offline actions
   */
  public getFailedActions(): OfflineAction[] {
    return this.offlineActions.filter(action => action.status === 'failed');
  }

  /**
   * Get all conflicts
   * @returns Array of conflicts
   */
  public getConflicts(): Conflict[] {
    return this.conflicts;
  }

  /**
   * Get unresolved conflicts
   * @returns Array of unresolved conflicts
   */
  public getUnresolvedConflicts(): Conflict[] {
    return this.conflicts.filter(conflict => !conflict.resolved);
  }

  /**
   * Resolve a conflict
   * @param conflictId Conflict ID
   * @param strategy Resolution strategy
   * @param customData Custom resolution data (for manual resolution)
   * @returns Promise that resolves when conflict is resolved
   */
  public async resolveConflict(
    conflictId: string,
    strategy: ConflictResolutionStrategy,
    customData?: any
  ): Promise<void> {
    const conflict = this.conflicts.find(c => c.id === conflictId);

    if (!conflict) {
      throw new Error(`Conflict with ID ${conflictId} not found`);
    }

    conflict.resolved = true;
    conflict.resolution = strategy;

    switch (strategy) {
      case ConflictResolutionStrategy.CLIENT_WINS:
        conflict.resolvedData = conflict.clientData;
        break;
      case ConflictResolutionStrategy.SERVER_WINS:
        conflict.resolvedData = conflict.serverData;
        break;
      case ConflictResolutionStrategy.MANUAL:
        conflict.resolvedData = customData;
        break;
    }

    await this.saveConflicts();

    // Notify conflict listeners
    this.notifyConflictListeners();
  }

  /**
   * Set default conflict resolution strategy
   * @param strategy Resolution strategy
   */
  public setDefaultConflictResolution(strategy: ConflictResolutionStrategy): void {
    this.defaultConflictResolution = strategy;
  }

  /**
   * Sync offline actions with server
   * @returns Promise that resolves when sync is complete
   */
  public async sync(): Promise<void> {
    if (this.isSyncing || !this.isInitialized) {
      return;
    }

    // Check if we're online
    if (!EnhancedNetworkUtil.getConnectionStatus() || EnhancedNetworkUtil.isOfflineModeEnabled()) {
      console.log('Cannot sync: offline or in offline mode');
      return;
    }

    try {
      this.isSyncing = true;
      this.notifySyncListeners();

      // Get pending actions sorted by priority (highest first)
      const pendingActions = this.getPendingActions()
        .sort((a, b) => b.priority - a.priority);

      if (pendingActions.length === 0) {
        console.log('No pending actions to sync');
        this.isSyncing = false;
        this.notifySyncListeners();
        return;
      }

      console.log(`Syncing ${pendingActions.length} pending actions`);

      // Process each action
      for (const action of pendingActions) {
        try {
          action.status = 'processing';
          await this.saveOfflineActions();

          // TODO: Implement actual API call here
          // For now, we'll just simulate success

          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 500));

          // Mark as completed
          action.status = 'completed';
          await this.saveOfflineActions();

          console.log(`Action ${action.id} completed`);
        } catch (error) {
          console.error(`Error processing action ${action.id}:`, error);

          action.retryCount++;
          action.error = error.message;

          if (action.retryCount >= action.maxRetries) {
            action.status = 'failed';
          } else {
            action.status = 'pending';
          }

          await this.saveOfflineActions();
        }
      }

      // Update last sync timestamp
      this.lastSyncTimestamp = Date.now();
      await this.saveLastSyncTimestamp();

      console.log('Sync completed');
    } catch (error) {
      console.error('Error during sync:', error);
    } finally {
      this.isSyncing = false;
      this.notifySyncListeners();
    }
  }

  /**
   * Add sync listener
   * @param listener Listener function
   * @returns Function to remove listener
   */
  public addSyncListener(listener: (isSyncing: boolean) => void): () => void {
    this.syncListeners.push(listener);

    // Return function to remove listener
    return () => {
      this.syncListeners = this.syncListeners.filter(l => l !== listener);
    };
  }

  /**
   * Add conflict listener
   * @param listener Listener function
   * @returns Function to remove listener
   */
  public addConflictListener(listener: (conflicts: Conflict[]) => void): () => void {
    this.conflictListeners.push(listener);

    // Return function to remove listener
    return () => {
      this.conflictListeners = this.conflictListeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify sync listeners
   */
  private notifySyncListeners(): void {
    this.syncListeners.forEach(listener => {
      try {
        listener(this.isSyncing);
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  /**
   * Notify conflict listeners
   */
  private notifyConflictListeners(): void {
    this.conflictListeners.forEach(listener => {
      try {
        listener(this.conflicts);
      } catch (error) {
        console.error('Error in conflict listener:', error);
      }
    });
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    // No need to remove event listener since we're not using window.addEventListener
    this.syncListeners = [];
    this.conflictListeners = [];
  }
}

// Export singleton instance
export default OfflineManager.getInstance();
