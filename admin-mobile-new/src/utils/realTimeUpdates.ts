import { Report, ReportType, TimePeriod } from './reports';

// Simple EventEmitter implementation for React Native
class SimpleEventEmitter {
  private events: Map<string, Function[]> = new Map();

  on(event: string, listener: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.events.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event: string, ...args: any[]): void {
    const listeners = this.events.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(...args));
    }
  }
}

// Define real-time update types
export interface DataUpdate {
  id: string;
  type: 'sales' | 'customers' | 'inventory' | 'orders' | 'payments';
  timestamp: Date;
  data: any;
  source: string;
}

export interface RealtimeMetrics {
  totalSales: number;
  totalCustomers: number;
  totalOrders: number;
  totalInventory: number;
  lastUpdated: Date;
  changesSinceLastHour: {
    sales: number;
    customers: number;
    orders: number;
    inventory: number;
  };
}

export interface LiveDataPoint {
  timestamp: Date;
  value: number;
  type: string;
}

class RealTimeUpdatesUtil extends SimpleEventEmitter {
  private static instance: RealTimeUpdatesUtil;
  private isActive: boolean = false;
  private updateInterval: NodeJS.Timeout | null = null;
  private subscribers: Map<string, (data: any) => void> = new Map();
  private currentMetrics: RealtimeMetrics;
  private dataHistory: LiveDataPoint[] = [];
  private maxHistorySize: number = 100;

  private constructor() {
    super();
    this.currentMetrics = {
      totalSales: 0,
      totalCustomers: 0,
      totalOrders: 0,
      totalInventory: 0,
      lastUpdated: new Date(),
      changesSinceLastHour: {
        sales: 0,
        customers: 0,
        orders: 0,
        inventory: 0,
      },
    };
  }

  public static getInstance(): RealTimeUpdatesUtil {
    if (!RealTimeUpdatesUtil.instance) {
      RealTimeUpdatesUtil.instance = new RealTimeUpdatesUtil();
    }
    return RealTimeUpdatesUtil.instance;
  }

  /**
   * Start real-time updates
   */
  public startUpdates(intervalMs: number = 30000): void {
    if (this.isActive) {
      console.warn('Real-time updates already active');
      return;
    }

    this.isActive = true;
    console.log('Starting real-time updates...');

    // Initial data fetch
    this.fetchLatestData();

    // Set up periodic updates
    this.updateInterval = setInterval(() => {
      this.fetchLatestData();
    }, intervalMs);

    this.emit('updates_started');
  }

  /**
   * Stop real-time updates
   */
  public stopUpdates(): void {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;

    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }

    console.log('Stopped real-time updates');
    this.emit('updates_stopped');
  }

  /**
   * Subscribe to specific data updates
   */
  public subscribe(key: string, callback: (data: any) => void): void {
    this.subscribers.set(key, callback);
  }

  /**
   * Unsubscribe from data updates
   */
  public unsubscribe(key: string): void {
    this.subscribers.delete(key);
  }

  /**
   * Get current real-time metrics
   */
  public getCurrentMetrics(): RealtimeMetrics {
    return { ...this.currentMetrics };
  }

  /**
   * Get live data history
   */
  public getDataHistory(type?: string): LiveDataPoint[] {
    if (type) {
      return this.dataHistory.filter(point => point.type === type);
    }
    return [...this.dataHistory];
  }

  /**
   * Simulate real-time data updates (for demo purposes)
   */
  public simulateDataUpdate(type: ReportType): DataUpdate {
    const update: DataUpdate = {
      id: `update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: type as any,
      timestamp: new Date(),
      data: this.generateMockData(type),
      source: 'simulation',
    };

    this.processDataUpdate(update);
    return update;
  }

  /**
   * Process incoming data update
   */
  private processDataUpdate(update: DataUpdate): void {
    // Update current metrics
    this.updateMetrics(update);

    // Add to history
    this.addToHistory({
      timestamp: update.timestamp,
      value: this.extractValue(update),
      type: update.type,
    });

    // Notify subscribers
    this.notifySubscribers(update);

    // Emit update event
    this.emit('data_update', update);
  }

  /**
   * Fetch latest data from various sources
   */
  private async fetchLatestData(): Promise<void> {
    try {
      // Simulate fetching data from different sources
      const updates = [
        this.simulateDataUpdate(ReportType.SALES),
        this.simulateDataUpdate(ReportType.CUSTOMERS),
        this.simulateDataUpdate(ReportType.ORDERS),
        this.simulateDataUpdate(ReportType.INVENTORY),
      ];

      // Process each update
      updates.forEach(update => {
        // Already processed in simulateDataUpdate
      });

      this.currentMetrics.lastUpdated = new Date();
      this.emit('metrics_updated', this.currentMetrics);

    } catch (error) {
      console.error('Error fetching real-time data:', error);
      this.emit('update_error', error);
    }
  }

  /**
   * Update current metrics with new data
   */
  private updateMetrics(update: DataUpdate): void {
    const previousMetrics = { ...this.currentMetrics };

    switch (update.type) {
      case 'sales':
        this.currentMetrics.totalSales += update.data.amount || 0;
        this.currentMetrics.changesSinceLastHour.sales += update.data.amount || 0;
        break;
      case 'customers':
        this.currentMetrics.totalCustomers += update.data.count || 0;
        this.currentMetrics.changesSinceLastHour.customers += update.data.count || 0;
        break;
      case 'orders':
        this.currentMetrics.totalOrders += update.data.count || 0;
        this.currentMetrics.changesSinceLastHour.orders += update.data.count || 0;
        break;
      case 'inventory':
        this.currentMetrics.totalInventory = update.data.total || this.currentMetrics.totalInventory;
        this.currentMetrics.changesSinceLastHour.inventory += update.data.change || 0;
        break;
    }

    // Calculate percentage changes
    const changes = this.calculateChanges(previousMetrics, this.currentMetrics);
    this.emit('metrics_changed', changes);
  }

  /**
   * Add data point to history
   */
  private addToHistory(dataPoint: LiveDataPoint): void {
    this.dataHistory.push(dataPoint);

    // Maintain maximum history size
    if (this.dataHistory.length > this.maxHistorySize) {
      this.dataHistory = this.dataHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Extract numeric value from update data
   */
  private extractValue(update: DataUpdate): number {
    switch (update.type) {
      case 'sales':
        return update.data.amount || 0;
      case 'customers':
      case 'orders':
        return update.data.count || 0;
      case 'inventory':
        return update.data.total || 0;
      default:
        return 0;
    }
  }

  /**
   * Notify all subscribers of data update
   */
  private notifySubscribers(update: DataUpdate): void {
    this.subscribers.forEach((callback, key) => {
      try {
        callback(update);
      } catch (error) {
        console.error(`Error notifying subscriber ${key}:`, error);
      }
    });
  }

  /**
   * Calculate changes between metrics
   */
  private calculateChanges(previous: RealtimeMetrics, current: RealtimeMetrics): any {
    return {
      sales: {
        absolute: current.totalSales - previous.totalSales,
        percentage: previous.totalSales > 0 ?
          ((current.totalSales - previous.totalSales) / previous.totalSales) * 100 : 0,
      },
      customers: {
        absolute: current.totalCustomers - previous.totalCustomers,
        percentage: previous.totalCustomers > 0 ?
          ((current.totalCustomers - previous.totalCustomers) / previous.totalCustomers) * 100 : 0,
      },
      orders: {
        absolute: current.totalOrders - previous.totalOrders,
        percentage: previous.totalOrders > 0 ?
          ((current.totalOrders - previous.totalOrders) / previous.totalOrders) * 100 : 0,
      },
      inventory: {
        absolute: current.totalInventory - previous.totalInventory,
        percentage: previous.totalInventory > 0 ?
          ((current.totalInventory - previous.totalInventory) / previous.totalInventory) * 100 : 0,
      },
    };
  }

  /**
   * Generate mock data for simulation
   */
  private generateMockData(type: ReportType): any {
    const baseValue = Math.random() * 1000;

    switch (type) {
      case ReportType.SALES:
        return {
          amount: Math.floor(baseValue * 10),
          currency: 'BDT',
          transactionId: `TXN_${Date.now()}`,
        };
      case ReportType.CUSTOMERS:
        return {
          count: Math.floor(Math.random() * 5) + 1,
          type: 'new_registration',
        };
      case ReportType.ORDERS:
        return {
          count: Math.floor(Math.random() * 10) + 1,
          status: 'completed',
        };
      case ReportType.INVENTORY:
        return {
          total: Math.floor(baseValue * 100),
          change: Math.floor((Math.random() - 0.5) * 20),
        };
      default:
        return {
          value: baseValue,
        };
    }
  }

  /**
   * Reset hourly changes (should be called every hour)
   */
  public resetHourlyChanges(): void {
    this.currentMetrics.changesSinceLastHour = {
      sales: 0,
      customers: 0,
      orders: 0,
      inventory: 0,
    };
    this.emit('hourly_reset');
  }

  /**
   * Get update frequency statistics
   */
  public getUpdateStats(): {
    totalUpdates: number;
    updatesPerMinute: number;
    lastUpdateTime: Date;
    isActive: boolean;
  } {
    const recentUpdates = this.dataHistory.filter(
      point => Date.now() - point.timestamp.getTime() < 60000 // Last minute
    );

    return {
      totalUpdates: this.dataHistory.length,
      updatesPerMinute: recentUpdates.length,
      lastUpdateTime: this.currentMetrics.lastUpdated,
      isActive: this.isActive,
    };
  }

  /**
   * Export current state for debugging
   */
  public exportState(): {
    metrics: RealtimeMetrics;
    history: LiveDataPoint[];
    stats: any;
    isActive: boolean;
  } {
    return {
      metrics: this.getCurrentMetrics(),
      history: this.getDataHistory(),
      stats: this.getUpdateStats(),
      isActive: this.isActive,
    };
  }
}

export default RealTimeUpdatesUtil;
