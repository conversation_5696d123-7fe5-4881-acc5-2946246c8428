import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';
import { StorageUtil } from './index';

// Define report types
export enum ReportType {
  SALES = 'sales',
  CUSTOMERS = 'customers',
  INVENTORY = 'inventory',
  ORDERS = 'orders',
  PAYMENTS = 'payments',
  CUSTOM = 'custom',
}

// Define time periods
export enum TimePeriod {
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  THIS_WEEK = 'this_week',
  LAST_WEEK = 'last_week',
  THIS_MONTH = 'this_month',
  LAST_MONTH = 'last_month',
  THIS_YEAR = 'this_year',
  LAST_YEAR = 'last_year',
  CUSTOM = 'custom',
}

// Define chart types
export enum ChartType {
  BAR = 'bar',
  LINE = 'line',
  PIE = 'pie',
  PROGRESS = 'progress',
  TABLE = 'table',
}

// Define report data
export interface ReportData {
  labels: string[];
  datasets: {
    data: number[];
    colors?: string[];
    legend?: string[];
  }[];
  summary?: {
    total: number;
    average: number;
    min: number;
    max: number;
    count: number;
  };
  details?: any[];
}

// Define report config
export interface ReportConfig {
  type: ReportType;
  title: string;
  description?: string;
  timePeriod: TimePeriod;
  chartType: ChartType;
  startDate?: Date;
  endDate?: Date;
  filters?: {
    [key: string]: any;
  };
  groupBy?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  limit?: number;
}

// Define report
export interface Report {
  id: string;
  config: ReportConfig;
  data: ReportData;
  createdAt: Date;
  updatedAt: Date;
  isFavorite: boolean;
}

// Define mock data
const MOCK_SALES_DATA = {
  daily: [1200, 1500, 800, 1300, 1800, 1100, 1600],
  weekly: [8500, 9200, 7800, 8900],
  monthly: [32000, 35000, 28000, 30000, 33000, 36000],
  yearly: [380000, 420000, 450000],
};

const MOCK_CUSTOMERS_DATA = {
  new: [5, 3, 7, 4, 6, 8, 5],
  total: [120, 123, 130, 134, 140, 148, 153],
  returning: [2, 1, 3, 2, 4, 3, 2],
};

const MOCK_INVENTORY_DATA = {
  stock: [50, 45, 40, 38, 35, 30, 25],
  sold: [5, 5, 2, 3, 5, 5, 5],
  categories: ['Fabric', 'Buttons', 'Zippers', 'Thread', 'Needles'],
  categoryStock: [120, 500, 300, 200, 150],
};

const MOCK_ORDERS_DATA = {
  new: [3, 5, 2, 4, 6, 3, 5],
  completed: [2, 3, 4, 3, 5, 4, 3],
  pending: [8, 10, 8, 9, 10, 9, 11],
  statuses: ['New', 'In Progress', 'Ready', 'Delivered', 'Cancelled'],
  statusCounts: [15, 25, 10, 45, 5],
};

const MOCK_PAYMENTS_DATA = {
  received: [800, 1200, 600, 900, 1500, 700, 1100],
  pending: [400, 300, 200, 400, 300, 400, 500],
  methods: ['Cash', 'Credit Card', 'Mobile Payment', 'Bank Transfer'],
  methodAmounts: [12000, 18000, 8000, 2000],
};

/**
 * Reports utility class for generating and managing reports
 */
class ReportsUtil {
  private static instance: ReportsUtil;

  private constructor() {}

  /**
   * Get the singleton instance
   * @returns ReportsUtil instance
   */
  public static getInstance(): ReportsUtil {
    if (!ReportsUtil.instance) {
      ReportsUtil.instance = new ReportsUtil();
    }
    return ReportsUtil.instance;
  }

  /**
   * Generate a report based on the provided config
   * @param config Report configuration
   * @returns Promise that resolves to the generated report
   */
  public async generateReport(config: ReportConfig): Promise<Report> {
    // In a real app, this would fetch data from an API or database
    // For now, we'll use mock data

    const reportData = await this.getMockData(config);

    const report: Report = {
      id: `report_${Date.now()}`,
      config,
      data: reportData,
      createdAt: new Date(),
      updatedAt: new Date(),
      isFavorite: false,
    };

    return report;
  }

  /**
   * Get mock data for a report
   * @param config Report configuration
   * @returns Promise that resolves to the report data
   */
  private async getMockData(config: ReportConfig): Promise<ReportData> {
    // Generate labels based on time period
    const labels = this.generateLabels(config.timePeriod);

    // Generate data based on report type
    let data: number[] = [];
    let colors: string[] = [];
    let legend: string[] = [];
    let details: any[] = [];

    switch (config.type) {
      case ReportType.SALES:
        data = this.getSalesData(config.timePeriod);
        break;
      case ReportType.CUSTOMERS:
        data = this.getCustomersData(config.timePeriod, config.filters?.type || 'total');
        break;
      case ReportType.INVENTORY:
        if (config.chartType === ChartType.PIE) {
          data = MOCK_INVENTORY_DATA.categoryStock;
          labels = MOCK_INVENTORY_DATA.categories;
          colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'];
        } else {
          data = this.getInventoryData(config.timePeriod, config.filters?.type || 'stock');
        }
        break;
      case ReportType.ORDERS:
        if (config.chartType === ChartType.PIE) {
          data = MOCK_ORDERS_DATA.statusCounts;
          labels = MOCK_ORDERS_DATA.statuses;
          colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'];
        } else {
          data = this.getOrdersData(config.timePeriod, config.filters?.type || 'new');
        }
        break;
      case ReportType.PAYMENTS:
        if (config.chartType === ChartType.PIE) {
          data = MOCK_PAYMENTS_DATA.methodAmounts;
          labels = MOCK_PAYMENTS_DATA.methods;
          colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'];
        } else {
          data = this.getPaymentsData(config.timePeriod, config.filters?.type || 'received');
        }
        break;
      default:
        data = [0, 0, 0, 0, 0, 0, 0];
    }

    // Calculate summary
    const total = data.reduce((sum, value) => sum + value, 0);
    const average = total / data.length;
    const min = Math.min(...data);
    const max = Math.max(...data);

    return {
      labels,
      datasets: [{
        data,
        colors,
        legend,
      }],
      summary: {
        total,
        average,
        min,
        max,
        count: data.length,
      },
      details,
    };
  }

  /**
   * Generate labels based on time period
   * @param timePeriod Time period
   * @returns Array of labels
   */
  private generateLabels(timePeriod: TimePeriod): string[] {
    switch (timePeriod) {
      case TimePeriod.TODAY:
        return ['Morning', 'Afternoon', 'Evening'];
      case TimePeriod.THIS_WEEK:
      case TimePeriod.LAST_WEEK:
        return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      case TimePeriod.THIS_MONTH:
      case TimePeriod.LAST_MONTH:
        return ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
      case TimePeriod.THIS_YEAR:
      case TimePeriod.LAST_YEAR:
        return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      default:
        return ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'];
    }
  }

  /**
   * Get sales data based on time period
   * @param timePeriod Time period
   * @returns Array of sales data
   */
  private getSalesData(timePeriod: TimePeriod): number[] {
    switch (timePeriod) {
      case TimePeriod.TODAY:
      case TimePeriod.YESTERDAY:
        return MOCK_SALES_DATA.daily.slice(0, 3);
      case TimePeriod.THIS_WEEK:
      case TimePeriod.LAST_WEEK:
        return MOCK_SALES_DATA.daily;
      case TimePeriod.THIS_MONTH:
      case TimePeriod.LAST_MONTH:
        return MOCK_SALES_DATA.weekly;
      case TimePeriod.THIS_YEAR:
      case TimePeriod.LAST_YEAR:
        return MOCK_SALES_DATA.monthly;
      default:
        return MOCK_SALES_DATA.daily;
    }
  }

  /**
   * Get customers data based on time period and type
   * @param timePeriod Time period
   * @param type Data type (new, total, returning)
   * @returns Array of customers data
   */
  private getCustomersData(timePeriod: TimePeriod, type: string): number[] {
    let data: number[] = [];

    switch (type) {
      case 'new':
        data = MOCK_CUSTOMERS_DATA.new;
        break;
      case 'total':
        data = MOCK_CUSTOMERS_DATA.total;
        break;
      case 'returning':
        data = MOCK_CUSTOMERS_DATA.returning;
        break;
      default:
        data = MOCK_CUSTOMERS_DATA.total;
    }

    switch (timePeriod) {
      case TimePeriod.TODAY:
      case TimePeriod.YESTERDAY:
        return data.slice(0, 3);
      case TimePeriod.THIS_WEEK:
      case TimePeriod.LAST_WEEK:
        return data;
      case TimePeriod.THIS_MONTH:
      case TimePeriod.LAST_MONTH:
        return data.slice(0, 4);
      case TimePeriod.THIS_YEAR:
      case TimePeriod.LAST_YEAR:
        return data.slice(0, 6);
      default:
        return data;
    }
  }

  /**
   * Get inventory data based on time period and type
   * @param timePeriod Time period
   * @param type Data type (stock, sold)
   * @returns Array of inventory data
   */
  private getInventoryData(timePeriod: TimePeriod, type: string): number[] {
    let data: number[] = [];

    switch (type) {
      case 'stock':
        data = MOCK_INVENTORY_DATA.stock;
        break;
      case 'sold':
        data = MOCK_INVENTORY_DATA.sold;
        break;
      default:
        data = MOCK_INVENTORY_DATA.stock;
    }

    switch (timePeriod) {
      case TimePeriod.TODAY:
      case TimePeriod.YESTERDAY:
        return data.slice(0, 3);
      case TimePeriod.THIS_WEEK:
      case TimePeriod.LAST_WEEK:
        return data;
      case TimePeriod.THIS_MONTH:
      case TimePeriod.LAST_MONTH:
        return data.slice(0, 4);
      case TimePeriod.THIS_YEAR:
      case TimePeriod.LAST_YEAR:
        return data.slice(0, 6);
      default:
        return data;
    }
  }

  /**
   * Get orders data based on time period and type
   * @param timePeriod Time period
   * @param type Data type (new, completed, pending)
   * @returns Array of orders data
   */
  private getOrdersData(timePeriod: TimePeriod, type: string): number[] {
    let data: number[] = [];

    switch (type) {
      case 'new':
        data = MOCK_ORDERS_DATA.new;
        break;
      case 'completed':
        data = MOCK_ORDERS_DATA.completed;
        break;
      case 'pending':
        data = MOCK_ORDERS_DATA.pending;
        break;
      default:
        data = MOCK_ORDERS_DATA.new;
    }

    switch (timePeriod) {
      case TimePeriod.TODAY:
      case TimePeriod.YESTERDAY:
        return data.slice(0, 3);
      case TimePeriod.THIS_WEEK:
      case TimePeriod.LAST_WEEK:
        return data;
      case TimePeriod.THIS_MONTH:
      case TimePeriod.LAST_MONTH:
        return data.slice(0, 4);
      case TimePeriod.THIS_YEAR:
      case TimePeriod.LAST_YEAR:
        return data.slice(0, 6);
      default:
        return data;
    }
  }

  /**
   * Get payments data based on time period and type
   * @param timePeriod Time period
   * @param type Data type (received, pending)
   * @returns Array of payments data
   */
  private getPaymentsData(timePeriod: TimePeriod, type: string): number[] {
    let data: number[] = [];

    switch (type) {
      case 'received':
        data = MOCK_PAYMENTS_DATA.received;
        break;
      case 'pending':
        data = MOCK_PAYMENTS_DATA.pending;
        break;
      default:
        data = MOCK_PAYMENTS_DATA.received;
    }

    switch (timePeriod) {
      case TimePeriod.TODAY:
      case TimePeriod.YESTERDAY:
        return data.slice(0, 3);
      case TimePeriod.THIS_WEEK:
      case TimePeriod.LAST_WEEK:
        return data;
      case TimePeriod.THIS_MONTH:
      case TimePeriod.LAST_MONTH:
        return data.slice(0, 4);
      case TimePeriod.THIS_YEAR:
      case TimePeriod.LAST_YEAR:
        return data.slice(0, 6);
      default:
        return data;
    }
  }

  /**
   * Export a report to CSV
   * @param report Report to export
   * @returns Promise that resolves to the file path
   */
  public async exportToCsv(report: Report): Promise<string> {
    try {
      // Generate CSV content
      let csvContent = `${report.config.title}\n`;
      csvContent += `Generated on: ${report.createdAt.toLocaleString()}\n\n`;

      // Add headers
      csvContent += `Label,Value\n`;

      // Add data
      for (let i = 0; i < report.data.labels.length; i++) {
        const label = report.data.labels[i];
        const value = report.data.datasets[0].data[i];
        csvContent += `${label},${value}\n`;
      }

      // Add summary
      csvContent += `\nSummary\n`;
      csvContent += `Total,${report.data.summary?.total}\n`;
      csvContent += `Average,${report.data.summary?.average}\n`;
      csvContent += `Min,${report.data.summary?.min}\n`;
      csvContent += `Max,${report.data.summary?.max}\n`;

      // Save to file
      const fileName = `report_${report.id}_${Date.now()}.csv`;
      const filePath = `${FileSystem.documentDirectory}${fileName}`;

      await FileSystem.writeAsStringAsync(filePath, csvContent);

      return filePath;
    } catch (error) {
      console.error('Error exporting report to CSV:', error);
      throw error;
    }
  }

  /**
   * Export a report to PDF
   * @param report Report to export
   * @returns Promise that resolves to the file path
   */
  public async exportToPdf(report: Report): Promise<string> {
    try {
      // Create HTML content for PDF
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>${report.config.title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #6200ee;
              padding-bottom: 20px;
            }
            .title {
              font-size: 24px;
              font-weight: bold;
              color: #6200ee;
              margin-bottom: 10px;
            }
            .subtitle {
              font-size: 14px;
              color: #666;
            }
            .info-section {
              margin-bottom: 30px;
            }
            .info-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              padding: 8px;
              background-color: #f5f5f5;
              border-radius: 4px;
            }
            .info-label {
              font-weight: bold;
              color: #333;
            }
            .info-value {
              color: #666;
            }
            .data-section {
              margin-bottom: 30px;
            }
            .section-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 15px;
              color: #6200ee;
              border-bottom: 1px solid #e0e0e0;
              padding-bottom: 5px;
            }
            .data-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            .data-table th,
            .data-table td {
              border: 1px solid #ddd;
              padding: 12px;
              text-align: left;
            }
            .data-table th {
              background-color: #6200ee;
              color: white;
              font-weight: bold;
            }
            .data-table tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .summary-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 15px;
              margin-top: 20px;
            }
            .summary-item {
              background-color: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              border-left: 4px solid #6200ee;
            }
            .summary-label {
              font-size: 12px;
              color: #666;
              margin-bottom: 5px;
            }
            .summary-value {
              font-size: 20px;
              font-weight: bold;
              color: #333;
            }
            .footer {
              margin-top: 40px;
              text-align: center;
              font-size: 12px;
              color: #999;
              border-top: 1px solid #e0e0e0;
              padding-top: 20px;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">${report.config.title}</div>
            <div class="subtitle">Generated on ${report.createdAt.toLocaleString()}</div>
          </div>

          <div class="info-section">
            <div class="info-row">
              <span class="info-label">Report Type:</span>
              <span class="info-value">${report.config.type.toUpperCase()}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Time Period:</span>
              <span class="info-value">${report.config.timePeriod.replace('_', ' ').toUpperCase()}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Chart Type:</span>
              <span class="info-value">${report.config.chartType.toUpperCase()}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Report ID:</span>
              <span class="info-value">${report.id}</span>
            </div>
          </div>

          <div class="data-section">
            <div class="section-title">Data</div>
            <table class="data-table">
              <thead>
                <tr>
                  <th>Label</th>
                  <th>Value</th>
                </tr>
              </thead>
              <tbody>
                ${report.data.labels.map((label, index) => `
                  <tr>
                    <td>${label}</td>
                    <td>${report.data.datasets[0].data[index].toLocaleString()}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          ${report.data.summary ? `
            <div class="data-section">
              <div class="section-title">Summary</div>
              <div class="summary-grid">
                <div class="summary-item">
                  <div class="summary-label">Total</div>
                  <div class="summary-value">${report.data.summary.total.toLocaleString()}</div>
                </div>
                <div class="summary-item">
                  <div class="summary-label">Average</div>
                  <div class="summary-value">${report.data.summary.average.toLocaleString(undefined, { maximumFractionDigits: 2 })}</div>
                </div>
                <div class="summary-item">
                  <div class="summary-label">Minimum</div>
                  <div class="summary-value">${report.data.summary.min.toLocaleString()}</div>
                </div>
                <div class="summary-item">
                  <div class="summary-label">Maximum</div>
                  <div class="summary-value">${report.data.summary.max.toLocaleString()}</div>
                </div>
              </div>
            </div>
          ` : ''}

          <div class="footer">
            <p>This report was generated by Tailor Management System</p>
            <p>© ${new Date().getFullYear()} All rights reserved</p>
          </div>
        </body>
        </html>
      `;

      // Generate PDF
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      return uri;
    } catch (error) {
      console.error('Error exporting report to PDF:', error);
      throw error;
    }
  }

  /**
   * Share a report
   * @param report Report to share
   * @returns Promise that resolves when the report is shared
   */
  public async shareReport(report: Report): Promise<void> {
    try {
      // Export to CSV
      const filePath = await this.exportToCsv(report);

      // Check if sharing is available
      const isSharingAvailable = await Sharing.isAvailableAsync();

      if (isSharingAvailable) {
        await Sharing.shareAsync(filePath);
      } else {
        throw new Error('Sharing is not available on this device');
      }
    } catch (error) {
      console.error('Error sharing report:', error);
      throw error;
    }
  }
}

// Export singleton instance
export default ReportsUtil.getInstance();
