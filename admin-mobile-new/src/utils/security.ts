import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';
import { isExpoGo } from './environment';

// Define secure storage options
const SECURE_STORE_OPTIONS: SecureStore.SecureStoreOptions = {
  keychainService: 'TailorManagementKeychain',
  keychainAccessible: SecureStore.WHEN_UNLOCKED,
};

// Define encryption key
const ENCRYPTION_KEY = 'tailor_management_encryption_key';

/**
 * Security utility class for handling secure storage and encryption
 */
class SecurityUtil {
  private static instance: SecurityUtil;
  private isSecureStoreAvailable: boolean = false;
  
  private constructor() {
    this.init();
  }
  
  /**
   * Get the singleton instance
   * @returns SecurityUtil instance
   */
  public static getInstance(): SecurityUtil {
    if (!SecurityUtil.instance) {
      SecurityUtil.instance = new SecurityUtil();
    }
    return SecurityUtil.instance;
  }
  
  /**
   * Initialize security utility
   */
  private async init(): Promise<void> {
    try {
      // Check if secure store is available
      this.isSecureStoreAvailable = await SecureStore.isAvailableAsync();
      
      if (!this.isSecureStoreAvailable) {
        console.warn('Secure store is not available on this device');
      }
      
      // Check if running in Expo Go
      if (isExpoGo()) {
        console.warn('Some security features may not be fully supported in Expo Go');
      }
    } catch (error) {
      console.error('Error initializing security utility:', error);
    }
  }
  
  /**
   * Check if secure store is available
   * @returns Boolean indicating if secure store is available
   */
  public isSecureStoreEnabled(): boolean {
    return this.isSecureStoreAvailable;
  }
  
  /**
   * Store a value securely
   * @param key Key to store value under
   * @param value Value to store
   * @returns Promise that resolves when the value is stored
   */
  public async secureStore(key: string, value: string): Promise<void> {
    try {
      if (!this.isSecureStoreAvailable) {
        throw new Error('Secure store is not available on this device');
      }
      
      await SecureStore.setItemAsync(key, value, SECURE_STORE_OPTIONS);
    } catch (error) {
      console.error(`Error storing ${key} securely:`, error);
      throw error;
    }
  }
  
  /**
   * Retrieve a value securely
   * @param key Key to retrieve value for
   * @returns Promise that resolves to the value or null if not found
   */
  public async secureRetrieve(key: string): Promise<string | null> {
    try {
      if (!this.isSecureStoreAvailable) {
        throw new Error('Secure store is not available on this device');
      }
      
      return await SecureStore.getItemAsync(key, SECURE_STORE_OPTIONS);
    } catch (error) {
      console.error(`Error retrieving ${key} securely:`, error);
      throw error;
    }
  }
  
  /**
   * Delete a value securely
   * @param key Key to delete value for
   * @returns Promise that resolves when the value is deleted
   */
  public async secureDelete(key: string): Promise<void> {
    try {
      if (!this.isSecureStoreAvailable) {
        throw new Error('Secure store is not available on this device');
      }
      
      await SecureStore.deleteItemAsync(key, SECURE_STORE_OPTIONS);
    } catch (error) {
      console.error(`Error deleting ${key} securely:`, error);
      throw error;
    }
  }
  
  /**
   * Generate a hash of a string
   * @param data String to hash
   * @param algorithm Hash algorithm to use
   * @returns Promise that resolves to the hash
   */
  public async generateHash(
    data: string,
    algorithm: Crypto.CryptoDigestAlgorithm = Crypto.CryptoDigestAlgorithm.SHA256
  ): Promise<string> {
    try {
      return await Crypto.digestStringAsync(algorithm, data);
    } catch (error) {
      console.error('Error generating hash:', error);
      throw error;
    }
  }
  
  /**
   * Generate a random string
   * @param length Length of the random string
   * @returns Promise that resolves to the random string
   */
  public async generateRandomString(length: number = 32): Promise<string> {
    try {
      const randomBytes = await Crypto.getRandomBytesAsync(length);
      return Array.from(randomBytes)
        .map(byte => byte.toString(16).padStart(2, '0'))
        .join('');
    } catch (error) {
      console.error('Error generating random string:', error);
      throw error;
    }
  }
  
  /**
   * Encrypt a string
   * @param data String to encrypt
   * @returns Promise that resolves to the encrypted string
   */
  public async encrypt(data: string): Promise<string> {
    try {
      // For simplicity, we're using a hash-based approach
      // In a production app, you would use a more robust encryption method
      const key = await this.secureRetrieve(ENCRYPTION_KEY) || 
        await this.generateAndStoreEncryptionKey();
      
      const hash = await this.generateHash(key + data);
      return `${hash}:${data}`;
    } catch (error) {
      console.error('Error encrypting data:', error);
      throw error;
    }
  }
  
  /**
   * Decrypt a string
   * @param encryptedData String to decrypt
   * @returns Promise that resolves to the decrypted string
   */
  public async decrypt(encryptedData: string): Promise<string> {
    try {
      // For simplicity, we're using a hash-based approach
      // In a production app, you would use a more robust encryption method
      const [hash, data] = encryptedData.split(':');
      
      const key = await this.secureRetrieve(ENCRYPTION_KEY);
      if (!key) {
        throw new Error('Encryption key not found');
      }
      
      const expectedHash = await this.generateHash(key + data);
      if (hash !== expectedHash) {
        throw new Error('Data integrity check failed');
      }
      
      return data;
    } catch (error) {
      console.error('Error decrypting data:', error);
      throw error;
    }
  }
  
  /**
   * Generate and store an encryption key
   * @returns Promise that resolves to the encryption key
   */
  private async generateAndStoreEncryptionKey(): Promise<string> {
    try {
      const key = await this.generateRandomString(32);
      await this.secureStore(ENCRYPTION_KEY, key);
      return key;
    } catch (error) {
      console.error('Error generating and storing encryption key:', error);
      throw error;
    }
  }
  
  /**
   * Validate app integrity
   * @returns Promise that resolves to a boolean indicating if the app integrity is valid
   */
  public async validateAppIntegrity(): Promise<boolean> {
    try {
      // In a production app, you would implement app integrity validation
      // For example, checking if the app is running in a secure environment
      // For now, we'll just return true
      return true;
    } catch (error) {
      console.error('Error validating app integrity:', error);
      return false;
    }
  }
  
  /**
   * Check if the device is rooted/jailbroken
   * @returns Boolean indicating if the device is rooted/jailbroken
   */
  public isDeviceRooted(): boolean {
    try {
      // In a production app, you would implement root detection
      // For now, we'll just return false
      return false;
    } catch (error) {
      console.error('Error checking if device is rooted:', error);
      return false;
    }
  }
  
  /**
   * Check if the app is running in a secure environment
   * @returns Boolean indicating if the app is running in a secure environment
   */
  public isSecureEnvironment(): boolean {
    try {
      // Check if running in Expo Go
      if (isExpoGo()) {
        return false;
      }
      
      // Check if secure store is available
      if (!this.isSecureStoreAvailable) {
        return false;
      }
      
      // Check if device is rooted/jailbroken
      if (this.isDeviceRooted()) {
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error checking if environment is secure:', error);
      return false;
    }
  }
}

// Export singleton instance
export default SecurityUtil.getInstance();
