import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Utility class for handling offline storage
 * Provides methods for storing, retrieving, and managing data in AsyncStorage
 */
class StorageUtil {
  /**
   * Store data in AsyncStorage
   * @param key Storage key
   * @param data Data to store (will be JSON stringified)
   */
  static async store(key: string, data: any): Promise<void> {
    try {
      const jsonValue = JSON.stringify(data);
      await AsyncStorage.setItem(key, jsonValue);
    } catch (error) {
      console.error(`Error storing data for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Retrieve data from AsyncStorage
   * @param key Storage key
   * @returns Parsed data or null if not found
   */
  static async retrieve<T>(key: string): Promise<T | null> {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error(`Error retrieving data for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove data from AsyncStorage
   * @param key Storage key
   */
  static async remove(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing data for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Clear all data from AsyncStorage
   */
  static async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error clearing storage:', error);
      throw error;
    }
  }

  /**
   * Get all keys from AsyncStorage
   * @returns Array of keys
   */
  static async getAllKeys(): Promise<string[]> {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('Error getting all keys:', error);
      return [];
    }
  }

  /**
   * Store data with timestamp for cache expiration
   * @param key Storage key
   * @param data Data to store
   * @param expiryInMinutes Expiry time in minutes
   */
  static async storeWithExpiry(key: string, data: any, expiryInMinutes: number): Promise<void> {
    try {
      const item = {
        data,
        timestamp: Date.now(),
        expiry: expiryInMinutes * 60 * 1000, // Convert minutes to milliseconds
      };
      await this.store(key, item);
    } catch (error) {
      console.error(`Error storing data with expiry for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Retrieve data with expiry check
   * @param key Storage key
   * @returns Data if not expired, null otherwise
   */
  static async retrieveWithExpiry<T>(key: string): Promise<T | null> {
    try {
      const item = await this.retrieve<{
        data: T;
        timestamp: number;
        expiry: number;
      }>(key);

      if (!item) {
        return null;
      }

      const now = Date.now();
      const isExpired = now - item.timestamp > item.expiry;

      if (isExpired) {
        await this.remove(key);
        return null;
      }

      return item.data;
    } catch (error) {
      console.error(`Error retrieving data with expiry for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Store array data with pagination support
   * @param key Base storage key
   * @param data Array data to store
   * @param pageSize Size of each page
   */
  static async storeArrayWithPagination<T>(
    key: string,
    data: T[],
    pageSize: number
  ): Promise<void> {
    try {
      // Store metadata
      await this.store(`${key}_meta`, {
        totalItems: data.length,
        pageSize,
        totalPages: Math.ceil(data.length / pageSize),
        timestamp: Date.now(),
      });

      // Split data into pages and store each page
      const totalPages = Math.ceil(data.length / pageSize);
      for (let page = 0; page < totalPages; page++) {
        const start = page * pageSize;
        const end = start + pageSize;
        const pageData = data.slice(start, end);
        await this.store(`${key}_page_${page}`, pageData);
      }
    } catch (error) {
      console.error(`Error storing array with pagination for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Retrieve paginated array data
   * @param key Base storage key
   * @param page Page number (0-based)
   * @returns Page data and metadata
   */
  static async retrieveArrayPage<T>(
    key: string,
    page: number
  ): Promise<{ data: T[]; meta: any } | null> {
    try {
      const meta = await this.retrieve<{
        totalItems: number;
        pageSize: number;
        totalPages: number;
        timestamp: number;
      }>(`${key}_meta`);

      if (!meta) {
        return null;
      }

      if (page >= meta.totalPages) {
        return { data: [], meta };
      }

      const data = await this.retrieve<T[]>(`${key}_page_${page}`);
      return { data: data || [], meta };
    } catch (error) {
      console.error(`Error retrieving array page for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Retrieve all paginated array data
   * @param key Base storage key
   * @returns All data and metadata
   */
  static async retrieveFullArray<T>(key: string): Promise<{ data: T[]; meta: any } | null> {
    try {
      const meta = await this.retrieve<{
        totalItems: number;
        pageSize: number;
        totalPages: number;
        timestamp: number;
      }>(`${key}_meta`);

      if (!meta) {
        return null;
      }

      const allData: T[] = [];
      for (let page = 0; page < meta.totalPages; page++) {
        const pageData = await this.retrieve<T[]>(`${key}_page_${page}`);
        if (pageData) {
          allData.push(...pageData);
        }
      }

      return { data: allData, meta };
    } catch (error) {
      console.error(`Error retrieving full array for key ${key}:`, error);
      return null;
    }
  }
}

export default StorageUtil;
