import AsyncStorage from '@react-native-async-storage/async-storage';
import { v4 as uuidv4 } from 'uuid';
import NetworkUtil from './network';

// Define queue item types
export type QueueItemStatus = 'pending' | 'processing' | 'failed' | 'completed';

export interface QueueItem {
  id: string;
  action: string;
  payload: any;
  timestamp: number;
  status: QueueItemStatus;
  retryCount: number;
  error?: string;
}

export interface SyncQueueOptions {
  maxRetries?: number;
  retryDelay?: number;
  storageKey?: string;
}

/**
 * SyncQueue class for handling offline actions
 * Stores actions in a queue and processes them when online
 */
class SyncQueue {
  private queue: QueueItem[] = [];
  private isProcessing: boolean = false;
  private options: Required<SyncQueueOptions>;
  private actionHandlers: Record<string, (payload: any) => Promise<any>> = {};
  private networkUnsubscribe: (() => void) | null = null;

  constructor(options?: SyncQueueOptions) {
    // Set default options
    this.options = {
      maxRetries: 3,
      retryDelay: 5000, // 5 seconds
      storageKey: 'sync_queue',
      ...options,
    };

    // Load queue from storage
    this.loadQueue();

    // Subscribe to network changes
    this.networkUnsubscribe = NetworkUtil.addListener(this.handleNetworkChange);
  }

  /**
   * Add an action to the queue
   * @param action Action type
   * @param payload Action payload
   * @returns Promise that resolves when the action is added to the queue
   */
  async add(action: string, payload: any): Promise<string> {
    // Generate a unique ID for the action
    const id = uuidv4();

    // Create queue item
    const queueItem: QueueItem = {
      id,
      action,
      payload,
      timestamp: Date.now(),
      status: 'pending',
      retryCount: 0,
    };

    // Add to queue
    this.queue.push(queueItem);

    // Save queue to storage
    await this.saveQueue();

    // If online, process queue immediately
    if (NetworkUtil.getConnectionStatus()) {
      this.processQueue();
    }

    return id;
  }

  /**
   * Register an action handler
   * @param action Action type
   * @param handler Function to handle the action
   */
  registerHandler(action: string, handler: (payload: any) => Promise<any>): void {
    this.actionHandlers[action] = handler;
  }

  /**
   * Get the current queue
   * @returns Array of queue items
   */
  getQueue(): QueueItem[] {
    return [...this.queue];
  }

  /**
   * Get a specific queue item by ID
   * @param id Queue item ID
   * @returns Queue item or undefined if not found
   */
  getItem(id: string): QueueItem | undefined {
    return this.queue.find(item => item.id === id);
  }

  /**
   * Clear the queue
   * @returns Promise that resolves when the queue is cleared
   */
  async clearQueue(): Promise<void> {
    this.queue = [];
    await this.saveQueue();
  }

  /**
   * Process the queue
   * @returns Promise that resolves when queue processing is complete
   */
  async processQueue(): Promise<void> {
    // If already processing or offline, return
    if (this.isProcessing || !NetworkUtil.getConnectionStatus()) {
      return;
    }

    this.isProcessing = true;

    try {
      // Get pending items
      const pendingItems = this.queue.filter(item => item.status === 'pending');

      // Process each item
      for (const item of pendingItems) {
        await this.processItem(item);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a specific queue item
   * @param item Queue item to process
   * @returns Promise that resolves when the item is processed
   */
  private async processItem(item: QueueItem): Promise<void> {
    // Skip if not pending
    if (item.status !== 'pending') {
      return;
    }

    // Update status to processing
    item.status = 'processing';
    await this.saveQueue();

    try {
      // Get handler for action
      const handler = this.actionHandlers[item.action];
      if (!handler) {
        throw new Error(`No handler registered for action: ${item.action}`);
      }

      // Call handler
      await handler(item.payload);

      // Update status to completed
      item.status = 'completed';
    } catch (error: any) {
      // Update status to failed
      item.status = 'failed';
      item.error = error.message || 'Unknown error';

      // Increment retry count
      item.retryCount++;

      // If retry count is less than max retries, set status back to pending
      if (item.retryCount < this.options.maxRetries) {
        // Schedule retry after delay
        setTimeout(() => {
          item.status = 'pending';
          this.saveQueue();
          this.processQueue();
        }, this.options.retryDelay);
      }
    }

    // Save queue
    await this.saveQueue();

    // Remove completed items
    this.cleanupQueue();
  }

  /**
   * Handle network status change
   * @param isConnected Whether the device is connected to the internet
   */
  private handleNetworkChange = (isConnected: boolean): void => {
    if (isConnected) {
      // Process queue when we come back online
      this.processQueue();
    }
  };

  /**
   * Load queue from storage
   */
  private async loadQueue(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(this.options.storageKey);
      if (data) {
        this.queue = JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading sync queue:', error);
    }
  }

  /**
   * Save queue to storage
   */
  private async saveQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.options.storageKey, JSON.stringify(this.queue));
    } catch (error) {
      console.error('Error saving sync queue:', error);
    }
  }

  /**
   * Clean up the queue by removing old completed items
   */
  private cleanupQueue(): void {
    // Remove completed items
    this.queue = this.queue.filter(item => item.status !== 'completed');
    this.saveQueue();
  }

  /**
   * Clean up resources
   */
  cleanup(): void {
    if (this.networkUnsubscribe) {
      this.networkUnsubscribe();
      this.networkUnsubscribe = null;
    }
  }
}

// Export singleton instance
export const syncQueue = new SyncQueue();

// Export default for convenience
export default syncQueue;
