import { Report, ReportData } from './reports';

// Define trend analysis types
export interface TrendData {
  period: string;
  value: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'stable';
}

export interface TrendAnalysis {
  current: number;
  previous: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'stable';
  forecast: number[];
  confidence: number;
}

export interface ComparisonData {
  currentPeriod: ReportData;
  previousPeriod: ReportData;
  comparison: {
    totalChange: number;
    totalChangePercent: number;
    averageChange: number;
    averageChangePercent: number;
    trend: 'up' | 'down' | 'stable';
  };
}

export interface PredictiveAnalysis {
  nextPeriodForecast: number;
  confidence: number;
  factors: string[];
  recommendations: string[];
}

export interface SeasonalPattern {
  pattern: number[];
  strength: number;
  peaks: number[];
  valleys: number[];
}

class TrendAnalysisUtil {
  private static instance: TrendAnalysisUtil;

  public static getInstance(): TrendAnalysisUtil {
    if (!TrendAnalysisUtil.instance) {
      TrendAnalysisUtil.instance = new TrendAnalysisUtil();
    }
    return TrendAnalysisUtil.instance;
  }

  /**
   * Calculate comprehensive trend analysis for a dataset
   */
  public calculateTrend(data: number[]): TrendAnalysis {
    if (data.length < 2) {
      return {
        current: data[0] || 0,
        previous: 0,
        change: 0,
        changePercent: 0,
        trend: 'stable',
        forecast: [],
        confidence: 0,
      };
    }

    const current = data[data.length - 1];
    const previous = data[data.length - 2];
    const change = current - previous;
    const changePercent = previous !== 0 ? (change / previous) * 100 : 0;

    // Determine trend based on multiple factors
    let trend: 'up' | 'down' | 'stable' = 'stable';
    const recentTrend = this.calculateRecentTrend(data.slice(-5)); // Last 5 data points
    
    if (Math.abs(changePercent) > 5 || Math.abs(recentTrend) > 3) {
      trend = (changePercent + recentTrend) > 0 ? 'up' : 'down';
    }

    // Generate forecast using multiple methods
    const forecast = this.generateAdvancedForecast(data, 3);
    const confidence = this.calculateConfidence(data);

    return {
      current,
      previous,
      change,
      changePercent,
      trend,
      forecast,
      confidence,
    };
  }

  /**
   * Compare two time periods with detailed analysis
   */
  public compareTimePeriods(current: ReportData, previous: ReportData): ComparisonData {
    const currentTotal = current.summary?.total || 0;
    const previousTotal = previous.summary?.total || 0;
    const currentAverage = current.summary?.average || 0;
    const previousAverage = previous.summary?.average || 0;

    const totalChange = currentTotal - previousTotal;
    const totalChangePercent = previousTotal !== 0 ? (totalChange / previousTotal) * 100 : 0;
    const averageChange = currentAverage - previousAverage;
    const averageChangePercent = previousAverage !== 0 ? (averageChange / previousAverage) * 100 : 0;

    // Determine overall trend
    let trend: 'up' | 'down' | 'stable' = 'stable';
    const combinedChange = (totalChangePercent + averageChangePercent) / 2;
    
    if (Math.abs(combinedChange) > 5) {
      trend = combinedChange > 0 ? 'up' : 'down';
    }

    return {
      currentPeriod: current,
      previousPeriod: previous,
      comparison: {
        totalChange,
        totalChangePercent,
        averageChange,
        averageChangePercent,
        trend,
      },
    };
  }

  /**
   * Generate predictive analysis with AI-like insights
   */
  public generatePredictiveAnalysis(reports: Report[]): PredictiveAnalysis {
    if (reports.length < 3) {
      return {
        nextPeriodForecast: 0,
        confidence: 0,
        factors: ['Insufficient data for prediction'],
        recommendations: ['Generate more reports to enable predictive analysis'],
      };
    }

    // Extract and analyze data
    const values = reports.map(r => r.data.summary?.total || 0);
    const trend = this.calculateTrend(values);
    
    // Advanced forecast considering seasonality and trends
    const seasonality = this.calculateSeasonality(values);
    const anomalies = this.detectAnomalies(values);
    
    // Adjust forecast based on patterns
    let nextPeriodForecast = trend.forecast[0] || trend.current;
    
    // Apply seasonal adjustment if strong seasonality detected
    if (seasonality.strength > 0.3) {
      const seasonalIndex = values.length % seasonality.pattern.length;
      const seasonalFactor = seasonality.pattern[seasonalIndex] / 
        (seasonality.pattern.reduce((a, b) => a + b, 0) / seasonality.pattern.length);
      nextPeriodForecast *= seasonalFactor;
    }

    // Generate comprehensive factors and recommendations
    const factors = this.identifyAdvancedFactors(trend, seasonality, anomalies);
    const recommendations = this.generateAdvancedRecommendations(trend, seasonality, anomalies);

    return {
      nextPeriodForecast: Math.max(0, nextPeriodForecast),
      confidence: trend.confidence,
      factors,
      recommendations,
    };
  }

  /**
   * Calculate moving averages with different windows
   */
  public calculateMovingAverages(data: number[]): { 
    short: number[], 
    medium: number[], 
    long: number[] 
  } {
    return {
      short: this.calculateMovingAverage(data, 3),
      medium: this.calculateMovingAverage(data, 7),
      long: this.calculateMovingAverage(data, 14),
    };
  }

  /**
   * Calculate volatility and risk metrics
   */
  public calculateVolatility(data: number[]): {
    standardDeviation: number;
    variance: number;
    coefficientOfVariation: number;
    volatilityIndex: number;
  } {
    if (data.length < 2) {
      return { standardDeviation: 0, variance: 0, coefficientOfVariation: 0, volatilityIndex: 0 };
    }

    const mean = data.reduce((a, b) => a + b, 0) / data.length;
    const variance = data.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / data.length;
    const standardDeviation = Math.sqrt(variance);
    const coefficientOfVariation = mean !== 0 ? (standardDeviation / mean) * 100 : 0;
    
    // Volatility index (0-100, where 100 is highly volatile)
    const volatilityIndex = Math.min(100, coefficientOfVariation);

    return {
      standardDeviation,
      variance,
      coefficientOfVariation,
      volatilityIndex,
    };
  }

  /**
   * Detect patterns and cycles in data
   */
  public detectPatterns(data: number[]): {
    cycles: number[];
    patterns: string[];
    strength: number;
  } {
    const cycles: number[] = [];
    const patterns: string[] = [];
    
    // Detect potential cycles
    for (let period = 2; period <= Math.floor(data.length / 3); period++) {
      const correlation = this.calculateAutocorrelation(data, period);
      if (correlation > 0.5) {
        cycles.push(period);
      }
    }

    // Identify patterns
    if (cycles.length > 0) {
      patterns.push(`Cyclical pattern detected with period ${cycles[0]}`);
    }

    const trend = this.calculateRecentTrend(data);
    if (Math.abs(trend) > 5) {
      patterns.push(trend > 0 ? 'Strong upward trend' : 'Strong downward trend');
    }

    const volatility = this.calculateVolatility(data);
    if (volatility.volatilityIndex > 30) {
      patterns.push('High volatility detected');
    } else if (volatility.volatilityIndex < 10) {
      patterns.push('Low volatility - stable pattern');
    }

    const strength = cycles.length > 0 ? Math.max(...cycles.map(c => this.calculateAutocorrelation(data, c))) : 0;

    return { cycles, patterns, strength };
  }

  // Private helper methods

  private calculateMovingAverage(data: number[], window: number): number[] {
    const result: number[] = [];
    for (let i = window - 1; i < data.length; i++) {
      const sum = data.slice(i - window + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / window);
    }
    return result;
  }

  private calculateRecentTrend(data: number[]): number {
    if (data.length < 3) return 0;
    
    const recent = data.slice(-3);
    const changes = [];
    for (let i = 1; i < recent.length; i++) {
      if (recent[i - 1] !== 0) {
        changes.push(((recent[i] - recent[i - 1]) / recent[i - 1]) * 100);
      }
    }
    
    return changes.length > 0 ? changes.reduce((a, b) => a + b, 0) / changes.length : 0;
  }

  private generateAdvancedForecast(data: number[], periods: number): number[] {
    if (data.length < 3) return [];

    // Combine linear regression with exponential smoothing
    const linearForecast = this.generateLinearForecast(data, periods);
    const exponentialForecast = this.generateExponentialForecast(data, periods);
    
    // Weighted average of forecasts
    const forecast: number[] = [];
    for (let i = 0; i < periods; i++) {
      const combined = (linearForecast[i] * 0.6) + (exponentialForecast[i] * 0.4);
      forecast.push(Math.max(0, combined));
    }

    return forecast;
  }

  private generateLinearForecast(data: number[], periods: number): number[] {
    const n = data.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = data;

    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    const forecast: number[] = [];
    for (let i = 1; i <= periods; i++) {
      forecast.push(slope * (n + i - 1) + intercept);
    }

    return forecast;
  }

  private generateExponentialForecast(data: number[], periods: number): number[] {
    if (data.length === 0) return [];

    const alpha = 0.3; // Smoothing parameter
    let smoothed = data[0];
    
    // Calculate exponentially smoothed values
    for (let i = 1; i < data.length; i++) {
      smoothed = alpha * data[i] + (1 - alpha) * smoothed;
    }

    // Generate forecast
    const forecast: number[] = [];
    for (let i = 0; i < periods; i++) {
      forecast.push(smoothed);
    }

    return forecast;
  }

  private calculateConfidence(data: number[]): number {
    if (data.length < 3) return 0;

    const volatility = this.calculateVolatility(data);
    const patterns = this.detectPatterns(data);
    
    // Base confidence on data consistency and pattern strength
    let confidence = 100 - volatility.volatilityIndex;
    
    // Adjust for pattern strength
    if (patterns.strength > 0.7) {
      confidence += 20;
    } else if (patterns.strength < 0.3) {
      confidence -= 20;
    }

    // Adjust for data length
    if (data.length < 5) {
      confidence -= 30;
    } else if (data.length > 10) {
      confidence += 10;
    }

    return Math.max(0, Math.min(100, confidence));
  }

  private calculateSeasonality(data: number[], period: number = 7): SeasonalPattern {
    if (data.length < period * 2) {
      return { pattern: [], strength: 0, peaks: [], valleys: [] };
    }

    const pattern: number[] = [];
    for (let i = 0; i < period; i++) {
      const values: number[] = [];
      for (let j = i; j < data.length; j += period) {
        values.push(data[j]);
      }
      const average = values.reduce((a, b) => a + b, 0) / values.length;
      pattern.push(average);
    }

    // Calculate seasonality strength
    const overallMean = data.reduce((a, b) => a + b, 0) / data.length;
    const seasonalVariance = pattern.reduce((a, b) => a + Math.pow(b - overallMean, 2), 0) / pattern.length;
    const totalVariance = data.reduce((a, b) => a + Math.pow(b - overallMean, 2), 0) / data.length;
    const strength = totalVariance > 0 ? seasonalVariance / totalVariance : 0;

    // Find peaks and valleys
    const peaks: number[] = [];
    const valleys: number[] = [];
    const threshold = overallMean * 0.1;

    pattern.forEach((value, index) => {
      if (value > overallMean + threshold) {
        peaks.push(index);
      } else if (value < overallMean - threshold) {
        valleys.push(index);
      }
    });

    return { pattern, strength, peaks, valleys };
  }

  private detectAnomalies(data: number[], threshold: number = 2): number[] {
    const mean = data.reduce((a, b) => a + b, 0) / data.length;
    const variance = data.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / data.length;
    const stdDev = Math.sqrt(variance);

    return data.map((value, index) => {
      const zScore = Math.abs((value - mean) / stdDev);
      return zScore > threshold ? index : -1;
    }).filter(index => index !== -1);
  }

  private calculateAutocorrelation(data: number[], lag: number): number {
    if (lag >= data.length) return 0;

    const n = data.length - lag;
    const mean = data.reduce((a, b) => a + b, 0) / data.length;
    
    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < n; i++) {
      numerator += (data[i] - mean) * (data[i + lag] - mean);
    }

    for (let i = 0; i < data.length; i++) {
      denominator += Math.pow(data[i] - mean, 2);
    }

    return denominator > 0 ? numerator / denominator : 0;
  }

  private identifyAdvancedFactors(trend: TrendAnalysis, seasonality: SeasonalPattern, anomalies: number[]): string[] {
    const factors: string[] = [];

    // Trend factors
    if (trend.trend === 'up') {
      factors.push('Positive growth momentum detected');
      if (trend.changePercent > 20) {
        factors.push('Exceptional growth rate observed');
      }
    } else if (trend.trend === 'down') {
      factors.push('Declining performance trend');
      if (trend.changePercent < -20) {
        factors.push('Significant performance decline');
      }
    }

    // Seasonality factors
    if (seasonality.strength > 0.5) {
      factors.push('Strong seasonal patterns identified');
      if (seasonality.peaks.length > 0) {
        factors.push(`Peak performance periods: ${seasonality.peaks.join(', ')}`);
      }
      if (seasonality.valleys.length > 0) {
        factors.push(`Low performance periods: ${seasonality.valleys.join(', ')}`);
      }
    }

    // Anomaly factors
    if (anomalies.length > 0) {
      factors.push(`${anomalies.length} anomalous data points detected`);
      if (anomalies.length > 3) {
        factors.push('High data volatility - investigate irregular patterns');
      }
    }

    // Confidence factors
    if (trend.confidence > 80) {
      factors.push('High prediction reliability');
    } else if (trend.confidence < 50) {
      factors.push('Low prediction confidence due to data inconsistency');
    }

    return factors;
  }

  private generateAdvancedRecommendations(trend: TrendAnalysis, seasonality: SeasonalPattern, anomalies: number[]): string[] {
    const recommendations: string[] = [];

    // Trend-based recommendations
    if (trend.trend === 'up') {
      recommendations.push('Capitalize on positive momentum');
      recommendations.push('Scale successful strategies');
      if (trend.changePercent > 30) {
        recommendations.push('Monitor sustainability and prepare for potential plateau');
      }
    } else if (trend.trend === 'down') {
      recommendations.push('Immediate intervention required');
      recommendations.push('Conduct root cause analysis');
      recommendations.push('Implement recovery strategies');
    } else {
      recommendations.push('Explore growth opportunities');
      recommendations.push('Implement performance improvement initiatives');
    }

    // Seasonality-based recommendations
    if (seasonality.strength > 0.3) {
      recommendations.push('Leverage seasonal patterns for planning');
      if (seasonality.peaks.length > 0) {
        recommendations.push('Prepare resources for peak periods');
      }
      if (seasonality.valleys.length > 0) {
        recommendations.push('Develop strategies to improve low-performance periods');
      }
    }

    // Anomaly-based recommendations
    if (anomalies.length > 2) {
      recommendations.push('Investigate and address data inconsistencies');
      recommendations.push('Implement quality control measures');
    }

    // Confidence-based recommendations
    if (trend.confidence < 60) {
      recommendations.push('Increase data collection frequency');
      recommendations.push('Standardize measurement processes');
    }

    return recommendations;
  }
}

export default TrendAnalysisUtil;
