/**
 * Form validation utility
 * Provides methods for validating common form fields
 */
class ValidationUtil {
  /**
   * Validate email address
   * @param email Email to validate
   * @returns Object with isValid and message properties
   */
  static validateEmail(email: string): { isValid: boolean; message: string } {
    if (!email || email.trim() === '') {
      return { isValid: false, message: 'Email is required' };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { isValid: false, message: 'Please enter a valid email address' };
    }

    return { isValid: true, message: '' };
  }

  /**
   * Validate password
   * @param password Password to validate
   * @param options Validation options
   * @returns Object with isValid and message properties
   */
  static validatePassword(
    password: string,
    options: {
      minLength?: number;
      requireUppercase?: boolean;
      requireLowercase?: boolean;
      requireNumbers?: boolean;
      requireSpecialChars?: boolean;
    } = {}
  ): { isValid: boolean; message: string } {
    const {
      minLength = 6,
      requireUppercase = false,
      requireLowercase = false,
      requireNumbers = false,
      requireSpecialChars = false,
    } = options;

    if (!password || password.trim() === '') {
      return { isValid: false, message: 'Password is required' };
    }

    if (password.length < minLength) {
      return {
        isValid: false,
        message: `Password must be at least ${minLength} characters`,
      };
    }

    if (requireUppercase && !/[A-Z]/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one uppercase letter',
      };
    }

    if (requireLowercase && !/[a-z]/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one lowercase letter',
      };
    }

    if (requireNumbers && !/[0-9]/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one number',
      };
    }

    if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one special character',
      };
    }

    return { isValid: true, message: '' };
  }

  /**
   * Validate password confirmation
   * @param password Password
   * @param confirmPassword Password confirmation
   * @returns Object with isValid and message properties
   */
  static validatePasswordConfirmation(
    password: string,
    confirmPassword: string
  ): { isValid: boolean; message: string } {
    if (!confirmPassword || confirmPassword.trim() === '') {
      return { isValid: false, message: 'Please confirm your password' };
    }

    if (password !== confirmPassword) {
      return { isValid: false, message: 'Passwords do not match' };
    }

    return { isValid: true, message: '' };
  }

  /**
   * Validate required field
   * @param value Field value
   * @param fieldName Field name for error message
   * @returns Object with isValid and message properties
   */
  static validateRequired(
    value: string,
    fieldName: string
  ): { isValid: boolean; message: string } {
    if (!value || value.trim() === '') {
      return { isValid: false, message: `${fieldName} is required` };
    }

    return { isValid: true, message: '' };
  }

  /**
   * Validate phone number
   * @param phone Phone number to validate
   * @returns Object with isValid and message properties
   */
  static validatePhone(phone: string): { isValid: boolean; message: string } {
    if (!phone || phone.trim() === '') {
      return { isValid: false, message: 'Phone number is required' };
    }

    // Basic phone validation - allows various formats
    const phoneRegex = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
    if (!phoneRegex.test(phone)) {
      return { isValid: false, message: 'Please enter a valid phone number' };
    }

    return { isValid: true, message: '' };
  }

  /**
   * Validate numeric value
   * @param value Value to validate
   * @param options Validation options
   * @returns Object with isValid and message properties
   */
  static validateNumeric(
    value: string,
    options: {
      fieldName?: string;
      allowEmpty?: boolean;
      min?: number;
      max?: number;
      integer?: boolean;
    } = {}
  ): { isValid: boolean; message: string } {
    const {
      fieldName = 'Value',
      allowEmpty = false,
      min,
      max,
      integer = false,
    } = options;

    if (!value || value.trim() === '') {
      return allowEmpty
        ? { isValid: true, message: '' }
        : { isValid: false, message: `${fieldName} is required` };
    }

    if (!/^-?\d*\.?\d*$/.test(value)) {
      return { isValid: false, message: `${fieldName} must be a number` };
    }

    const numValue = parseFloat(value);

    if (integer && !Number.isInteger(numValue)) {
      return { isValid: false, message: `${fieldName} must be an integer` };
    }

    if (min !== undefined && numValue < min) {
      return {
        isValid: false,
        message: `${fieldName} must be at least ${min}`,
      };
    }

    if (max !== undefined && numValue > max) {
      return {
        isValid: false,
        message: `${fieldName} must be at most ${max}`,
      };
    }

    return { isValid: true, message: '' };
  }

  /**
   * Validate date
   * @param date Date string to validate
   * @param options Validation options
   * @returns Object with isValid and message properties
   */
  static validateDate(
    date: string,
    options: {
      fieldName?: string;
      allowEmpty?: boolean;
      minDate?: Date;
      maxDate?: Date;
      format?: string;
    } = {}
  ): { isValid: boolean; message: string } {
    const {
      fieldName = 'Date',
      allowEmpty = false,
      minDate,
      maxDate,
    } = options;

    if (!date || date.trim() === '') {
      return allowEmpty
        ? { isValid: true, message: '' }
        : { isValid: false, message: `${fieldName} is required` };
    }

    // Check if it's a valid date
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      return { isValid: false, message: `${fieldName} is not a valid date` };
    }

    if (minDate && dateObj < minDate) {
      return {
        isValid: false,
        message: `${fieldName} must be on or after ${minDate.toLocaleDateString()}`,
      };
    }

    if (maxDate && dateObj > maxDate) {
      return {
        isValid: false,
        message: `${fieldName} must be on or before ${maxDate.toLocaleDateString()}`,
      };
    }

    return { isValid: true, message: '' };
  }

  /**
   * Validate URL
   * @param url URL to validate
   * @param options Validation options
   * @returns Object with isValid and message properties
   */
  static validateUrl(
    url: string,
    options: {
      fieldName?: string;
      allowEmpty?: boolean;
      requireHttps?: boolean;
    } = {}
  ): { isValid: boolean; message: string } {
    const {
      fieldName = 'URL',
      allowEmpty = false,
      requireHttps = false,
    } = options;

    if (!url || url.trim() === '') {
      return allowEmpty
        ? { isValid: true, message: '' }
        : { isValid: false, message: `${fieldName} is required` };
    }

    let urlRegex = /^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
    
    if (requireHttps) {
      urlRegex = /^(https:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
    }

    if (!urlRegex.test(url)) {
      return {
        isValid: false,
        message: requireHttps
          ? `${fieldName} must be a valid HTTPS URL`
          : `${fieldName} must be a valid URL`,
      };
    }

    return { isValid: true, message: '' };
  }
}

export default ValidationUtil;
