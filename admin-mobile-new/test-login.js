// Simple test script to verify login functionality
const axios = require('axios');

const testLogin = async () => {
  try {
    console.log('🧪 Testing login functionality...');
    
    const response = await axios.post('http://localhost:8000/api/login', {
      email: '<EMAIL>',
      password: 'password'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Login successful!');
    console.log('📊 Response data:', {
      user: response.data.user,
      token: response.data.token ? 'Token received' : 'No token',
      message: response.data.message
    });
    
    // Test authenticated request
    const dashboardResponse = await axios.get('http://localhost:8000/api/dashboard/stats', {
      headers: {
        'Authorization': `Bearer ${response.data.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Dashboard stats request successful!');
    console.log('📊 Dashboard data:', dashboardResponse.data);
    
    console.log('🎉 All tests passed! Backend is fully functional.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
};

testLogin();
