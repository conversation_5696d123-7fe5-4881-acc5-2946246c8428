import React, { useState } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
// Import hooks from context
import { useAuth } from '../../src/context/AuthContext';
import { useTheme } from '../../src/context/ThemeContext';
// Import UI components
import { Button, Input, Card } from '../../src/components/ui';

export default function LoginScreen() {
  const { login } = useAuth();
  const { isDark } = useTheme();
  const { t } = useTranslation();
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      await login({ email, password });
      router.replace('/(tabs)');
    } catch (err) {
      console.error('Login error:', err);
      setError(err.message || 'Login failed. Please try again.');

      // Check if the error is related to non-admin access
      if (err.message && err.message.includes('Only admin users')) {
        Alert.alert(
          'Access Denied',
          'This app is only for admin users. Please use the regular app if you are a regular user.',
          [{ text: 'OK' }]
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLoginWithTestUser = () => {
    setEmail('<EMAIL>');
    setPassword('password');

    // Trigger login after a short delay to show the filled fields
    setTimeout(() => {
      handleLogin();
    }, 500);
  };

  return (
    
      
        
        
          {t('app_name')}
        
      

      
        
          {t('login')}
        

        {error && (
          {error}
        )}

        

        

        
          
            {t('forgot_password')}
          
        

        

        
      

      
        This app is for admin users only.
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  contentContainer: {
    padding,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100%',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom,
  },
  logo: {
    width,
    height,
    marginBottom,
  },
  appName: {
    fontSize,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  card: {
    width: '100%',
    maxWidth,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
    textAlign: 'center',
  },
  errorText: {
    color: '#cf6679',
    marginBottom,
    textAlign: 'center',
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom,
  },
  forgotPasswordText: {
    fontSize,
  },
  loginButton: {
    marginBottom,
  },
  testUserButton: {
    marginBottom,
  },
  adminOnlyText: {
    marginTop,
    fontSize,
    fontStyle: 'italic',
  },
});
