import React, { useEffect } from 'react';
import { Tabs } from 'expo-router';
import { useRouter, useSegments } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../src/context/AuthContext';
import { useTheme } from '../../src/context/ThemeContext';

export default function TabLayout() {
  const { isAuthenticated, isLoading } = useAuth();
  const { theme, isDark } = useTheme();
  const { t } = useTranslation();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      // Check if the user is authenticated
      if (!isAuthenticated) {
        // Redirect to the login page if they're not signed in
        router.replace('/login');
      }
    }
  }, [isAuthenticated, isLoading, segments, router]);

  if (isLoading || !isAuthenticated) {
    return null;
  }

  return (
    
       (
            
          ),
        }}
      />
       (
            
          ),
        }}
      />
       (
            
          ),
        }}
      />
       (
            
          ),
        }}
      />
       (
            
          ),
        }}
      />
    
  );
}
