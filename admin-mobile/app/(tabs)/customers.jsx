import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { Button } from '../../src/components/ui';

export default function CustomersScreen() {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  
  return (
    
      
        
          {t('customers')}
        
      
      
      
        
        
          {t('feature_coming_soon')}
        
        
          This screen will allow you to manage all customer accounts, view their measurements, and track their order history.
        
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
    padding,
  },
  header: {
    marginBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
  },
  content: {
    flex,
    justifyContent: 'center',
    alignItems: 'center',
    padding,
  },
  message: {
    fontSize,
    fontWeight: 'bold',
    marginTop,
    marginBottom,
    textAlign: 'center',
  },
  description: {
    fontSize,
    textAlign: 'center',
    marginBottom,
  },
});
