import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, RefreshControl } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { useAuth } from '../../src/context/AuthContext';
import { Card } from '../../src/components/ui';
import { Ionicons } from '@expo/vector-icons';

export default function DashboardScreen() {
  const { t } = useTranslation();
  const { theme, isDark } = useTheme();
  const { user } = useAuth();
  
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalUsers,
    adminUsers,
    regularUsers,
    activeUsers,
    inactiveUsers,
    totalCustomers,
    totalOrders,
    pendingOrders,
    completedOrders,
    totalRevenue,
  });
  
  // Mock data loading
  const loadDashboardData = async () => {
    setRefreshing(true);
    
    // Simulate API call
    setTimeout(() => {
      setStats({
        totalUsers,
        adminUsers,
        regularUsers,
        activeUsers,
        inactiveUsers,
        totalCustomers,
        totalOrders,
        pendingOrders,
        completedOrders,
        totalRevenue,
      });
      setRefreshing(false);
    }, 1000);
  };
  
  useEffect(() => {
    loadDashboardData();
  }, []);
  
  const onRefresh = () => {
    loadDashboardData();
  };
  
  return (
    
      }
    >
      
        
          {t('admin_dashboard')}
        
        
          {t('welcome_back')}, {user?.name}
        
      
      
      
        
          
            
          
          
            {stats.totalUsers}
          
          
            {t('total_users')}
          
        
        
        
          
            
          
          
            {stats.totalCustomers}
          
          
            {t('customers')}
          
        
        
        
          
            
          
          
            {stats.totalOrders}
          
          
            {t('orders')}
          
        
        
        
          
            
          
          
            ৳{stats.totalRevenue.toLocaleString()}
          
          
            {t('revenue')}
          
        
      
      
      
        
          
            {t('admin_users')}
          
          
            {stats.adminUsers}
          
        
        
        
          
            {t('regular_users')}
          
          
            {stats.regularUsers}
          
        
        
        
          
            {t('active_users')}
          
          
            {stats.activeUsers}
          
        
        
        
          
            {t('inactive_users')}
          
          
            {stats.inactiveUsers}
          
        
      
      
      
        
          
            {t('pending_orders')}
          
          
            {stats.pendingOrders}
          
        
        
        
          
            {t('completed_orders')}
          
          
            {stats.completedOrders}
          
        
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  contentContainer: {
    padding,
  },
  header: {
    marginBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize,
    marginTop,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom,
  },
  statsCard: {
    width: '48%',
    marginBottom,
    alignItems: 'center',
    padding,
  },
  iconContainer: {
    marginBottom,
  },
  statValue: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  statLabel: {
    fontSize,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical,
    borderBottomWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  detailLabel: {
    fontSize,
  },
  detailValue: {
    fontSize,
    fontWeight: '500',
  },
});
