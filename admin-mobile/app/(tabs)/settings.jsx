import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { useAuth } from '../../src/context/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { Card, Button } from '../../src/components/ui';
import { changeLanguage } from '../../src/i18n';
import { useRouter } from 'expo-router';

export default function SettingsScreen() {
  const { t, i18n } = useTranslation();
  const { theme, isDark, setTheme } = useTheme();
  const { user, logout } = useAuth();
  const router = useRouter();
  
  const handleLogout = async () => {
    try {
      await logout();
      router.replace('/login');
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert(t('error'), t('logout_failed'));
    }
  };
  
  const handleLanguageChange = (language) => {
    changeLanguage(language);
  };
  
  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
  };
  
  return (
    
      
        
          {t('settings')}
        
      
      
      
        
          
            
              {user?.name}
            
            
              {user?.email}
            
            
              
                {user?.role.toUpperCase()}
              
            
          
        
        
        
      
      
      
         handleLanguageChange('en')}
        >
          
            {t('english')}
          
          {i18n.language === 'en' && (
            
          )}
        
        
         handleLanguageChange('bn')}
        >
          
            {t('bengali')}
          
          {i18n.language === 'bn' && (
            
          )}
        
      
      
      
         handleThemeChange('light')}
        >
          
            {t('light')}
          
          {theme === 'light' && (
            
          )}
        
        
         handleThemeChange('dark')}
        >
          
            {t('dark')}
          
          {theme === 'dark' && (
            
          )}
        
        
         handleThemeChange('system')}
        >
          
            {t('system')}
          
          {theme === 'system' && (
            
          )}
        
      
      
      
        
          
            {t('version')}
          
          
            1.0.0
          
        
        
        
          
            {t('build')}
          
          
            2023.05.01
          
        
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  contentContainer: {
    padding,
  },
  header: {
    marginBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom,
  },
  profileInfo: {
    flex,
  },
  profileName: {
    fontSize,
    fontWeight: 'bold',
  },
  profileEmail: {
    fontSize,
    marginTop,
  },
  roleBadge: {
    paddingHorizontal,
    paddingVertical,
    borderRadius,
    alignSelf: 'flex-start',
    marginTop,
  },
  roleText: {
    color: '#ffffff',
    fontSize,
    fontWeight: 'bold',
  },
  logoutButton: {
    marginTop,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical,
    paddingHorizontal,
    borderRadius,
    marginBottom,
  },
  selectedOption: {
    borderWidth,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  optionText: {
    fontSize,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical,
    borderBottomWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  infoLabel: {
    fontSize,
  },
  infoValue: {
    fontSize,
    fontWeight: '500',
  },
});
