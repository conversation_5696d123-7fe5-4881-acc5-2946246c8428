import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  FlatList, 
  TouchableOpacity, 
  ActivityIndicator,
  Alert,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { Card, Button } from '../../src/components/ui';
import authService, { User } from '../../src/api/authService';

export default function UsersScreen() {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  
  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // In a real app, this would be an API call
      // const response = await authService.getUsers();
      // setUsers(response);
      
      // Mock data
      setTimeout(() => {
        setUsers([
          { id, name: 'Admin User', email: '<EMAIL>', role: 'admin', is_active, language: 'en', theme: 'system' },
          { id, name: 'Regular User', email: '<EMAIL>', role: 'user', is_active, language: 'en', theme: 'light' },
          { id, name: 'Test User', email: '<EMAIL>', role: 'admin', is_active, language: 'bn', theme: 'dark' },
          { id, name: 'John Doe', email: '<EMAIL>', role: 'user', is_active, language: 'en', theme: 'light' },
          { id, name: 'Jane Smith', email: '<EMAIL>', role: 'user', is_active, language: 'en', theme: 'system' },
        ]);
        setLoading(false);
        setRefreshing(false);
      }, 1000);
    } catch (err) {
      console.error('Error loading users:', err);
      setError(t('error_loading_users'));
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  useEffect(() => {
    loadUsers();
  }, []);
  
  const onRefresh = () => {
    setRefreshing(true);
    loadUsers();
  };
  
  const handleAddUser = () => {
    Alert.alert(t('feature_coming_soon'));
  };
  
  const handleUserPress = (user) => {
    Alert.alert(
      t('user_details'),
      `${user.name} (${user.email})\n${t('role')}: ${user.role}\n${t('status')}: ${user.is_active ? t('active') : t('inactive')}`
    );
  };
  
  const handleEditUser = (user) => {
    Alert.alert(t('feature_coming_soon'));
  };
  
  const handleDeleteUser = (user) => {
    Alert.alert(
      t('delete_confirmation'),
      t('confirm_delete'),
      [
        {
          text('cancel'),
          style: 'cancel',
        },
        {
          text('delete'),
          style: 'destructive',
          onPress: () => {
            // In a real app, this would be an API call
            // authService.deleteUser(user.id);
            Alert.alert(t('user_deleted_successfully'));
            
            // Update the local state
            setUsers(users.filter(u => u.id !== user.id));
          },
        },
      ]
    );
  };
  
  const renderUserItem = ({ item }: { item }) => (
    
      
        
          
            {item.name}
          
          
            {item.email}
          
        
        
        
          
            
              {item.role.toUpperCase()}
            
          
          
          
            
            
              {item.is_active ? t('active') : t('inactive')}
            
          
        
      
      
      
         handleUserPress(item)}
        >
          
        
        
         handleEditUser(item)}
        >
          
        
        
         handleDeleteUser(item)}
        >
          
        
      
    
  );
  
  return (
    
      
        
          {t('users_management')}
        
        
        
      
      
      {loading && !refreshing ? (
        
          
          
            {t('loading_users')}
          
        
      ) : error ? (
        
          
          
            {error}
          
          
        
      ) : (
         item.id.toString()}
          contentContainerStyle={styles.listContent}
          refreshControl={
            
          }
          ListEmptyComponent={
            
              
              
                {t('no_users_found')}
              
            
          }
        />
      )}
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
    padding,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
  },
  addButton: {
    paddingHorizontal,
  },
  loadingContainer: {
    flex,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop,
    fontSize,
  },
  errorContainer: {
    flex,
    justifyContent: 'center',
    alignItems: 'center',
    padding,
  },
  errorText: {
    marginTop,
    fontSize,
    textAlign: 'center',
    marginBottom,
  },
  tryAgainButton: {
    marginTop,
  },
  listContent: {
    paddingBottom,
  },
  emptyContainer: {
    flex,
    justifyContent: 'center',
    alignItems: 'center',
    padding,
  },
  emptyText: {
    marginTop,
    fontSize,
    textAlign: 'center',
  },
  userCard: {
    marginBottom,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom,
  },
  userInfo: {
    flex,
  },
  userName: {
    fontSize,
    fontWeight: 'bold',
  },
  userEmail: {
    fontSize,
    marginTop,
  },
  userMeta: {
    alignItems: 'flex-end',
  },
  roleBadge: {
    paddingHorizontal,
    paddingVertical,
    borderRadius,
    marginBottom,
  },
  roleText: {
    color: '#ffffff',
    fontSize,
    fontWeight: 'bold',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width,
    height,
    borderRadius,
    marginRight,
  },
  statusText: {
    fontSize,
  },
  userActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    width,
    height,
    borderRadius,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft,
  },
});
