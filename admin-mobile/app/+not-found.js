import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../src/context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

export default function NotFoundScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const { isDark } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#121212' : '#f5f5f5' }]}>
      <Ionicons name="alert-circle-outline" size={100} color={isDark ? '#bb86fc' : '#6200ee'} />
      <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000' }]}>
        404 - Page Not Found
      </Text>
      <Text style={[styles.message, { color: isDark ? '#bbbbbb' : '#666666' }]}>
        The page you are looking for does not exist.
      </Text>
      <TouchableOpacity
        style={[styles.button, { backgroundColor: isDark ? '#bb86fc' : '#6200ee' }]}
        onPress={() => router.replace('/')}
      >
        <Text style={styles.buttonText}>Go to Home</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});
