{"name": "tailor-management-admin", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo": "~53.0.9", "expo-linking": "^7.1.5", "expo-router": "^5.0.7", "expo-status-bar": "~2.2.3", "i18next": "^25.2.0", "react": "19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-svg-transformer": "^1.5.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}