import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Define API base URL based on environment
const getApiBaseUrl = () => {
  // For web browser
  if (Platform.OS === 'web') {
    return 'http://localhost:8000/api';
  }
  
  // For iOS devices and simulators
  if (Platform.OS === 'ios') {
    return 'http://*************:8000/api'; // Your actual IP address
  }
  
  // For Android devices and emulators
  if (Platform.OS === 'android') {
    // Special IP for Android emulator to access host
    return 'http://********:8000/api';
  }
  
  // Default fallback
  return 'http://*************:8000/api'; // Your actual IP address
};

// Create axios instance
const apiClient = axios.create({
  baseURL: getApiBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

// Add request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting token from AsyncStorage:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for handling common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    if (error.response && error.response.status === 401) {
      // Unauthorized, clear token and user
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user');
      // You might want to redirect to login screen here
    }
    return Promise.reject(error);
  }
);

export default apiClient;
