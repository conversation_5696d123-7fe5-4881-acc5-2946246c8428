import React from 'react';
import { StyleSheet, TouchableOpacity, Text, ActivityIndicator, View } from 'react-native';
// Import the useTheme hook from ThemeContext
import { useTheme } from '../../context/ThemeContext';



const Button.FC = ({
  title,
  onPress,
  type = 'primary',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
}) => {
  const { isDark } = useTheme();

  const getButtonStyle = () => {
    switch (type) {
      case 'primary':
        return {
          backgroundColor ? '#bb86fc' : '#6200ee',
          borderColor: 'transparent',
        };
      case 'secondary':
        return {
          backgroundColor ? '#03dac6' : '#03dac6',
          borderColor: 'transparent',
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor ? '#bb86fc' : '#6200ee',
        };
      case 'danger':
        return {
          backgroundColor ? '#cf6679' : '#b00020',
          borderColor: 'transparent',
        };
      default {
          backgroundColor ? '#bb86fc' : '#6200ee',
          borderColor: 'transparent',
        };
    }
  };

  const getTextStyle = () => {
    switch (type) {
      case 'outline':
        return {
          color ? '#bb86fc' : '#6200ee',
        };
      default {
          color: '#ffffff',
        };
    }
  };

  return (
    
      {loading ? (
        
      ) : (
        {title}
      )}
    
  );
};

const styles = StyleSheet.create({
  button: {
    paddingVertical,
    paddingHorizontal,
    borderRadius,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity.5,
  },
  text: {
    fontSize,
    fontWeight: '600',
  },
});

export default Button;
