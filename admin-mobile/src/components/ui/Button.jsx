import React from 'react';
import { StyleSheet, TouchableOpacity, Text, ActivityIndicator, View } from 'react-native';
// Import the useTheme hook from ThemeContext
import { useTheme } from '../../context/ThemeContext';



const Button = ({
  title,
  onPress,
  type = 'primary',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
}) => {
  const { isDark } = useTheme();

  const getButtonStyle = () => {
    switch (type) {
      case 'primary':
        return {
          backgroundColor: isDark ? '#bb86fc' : '#6200ee',
          borderColor: 'transparent',
        };
      case 'secondary':
        return {
          backgroundColor: isDark ? '#03dac6' : '#03dac6',
          borderColor: 'transparent',
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: isDark ? '#bb86fc' : '#6200ee',
        };
      case 'danger':
        return {
          backgroundColor: isDark ? '#cf6679' : '#b00020',
          borderColor: 'transparent',
        };
      default:
        return {
          backgroundColor: isDark ? '#bb86fc' : '#6200ee',
          borderColor: 'transparent',
        };
    }
  };

  const getTextStyle = () => {
    switch (type) {
      case 'outline':
        return {
          color: isDark ? '#bb86fc' : '#6200ee',
        };
      default:
        return {
          color: '#ffffff',
        };
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        fullWidth && styles.fullWidth,
        disabled && styles.disabled,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
    >
      {loading ? (
        <ActivityIndicator size="small" color="#ffffff" />
      ) : (
        <Text style={[styles.text, getTextStyle(), textStyle]}>
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.5,
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default Button;
