import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
// Import the useTheme hook from ThemeContext
import { useTheme } from '../../context/ThemeContext';



const Card.FC = ({
  title,
  children,
  onPress,
  style,
  titleStyle,
}) => {
  const { isDark } = useTheme();

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    
      {title && (
        
          {title}
        
      )}
      {children}
    
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius,
    padding,
    marginBottom,
    shadowOffset: { width, height },
    shadowOpacity.1,
    shadowRadius,
    elevation,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
});

export default Card;
