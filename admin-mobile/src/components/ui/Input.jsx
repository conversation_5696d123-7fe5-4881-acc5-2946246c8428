import React, { useState } from 'react';
import { StyleSheet, View, TextInput, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// Import the useTheme hook from ThemeContext
import { useTheme } from '../../context/ThemeContext';



const Input.FC = ({
  label,
  placeholder,
  value,
  onChangeText,
  secureTextEntry = false,
  error,
  multiline = false,
  numberOfLines = 1,
  keyboardType = 'default',
  autoCapitalize = 'none',
  style,
  inputStyle,
}) => {
  const { isDark } = useTheme();
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    
      {label && (
        
          {label}
        
      )}

      
        

        {secureTextEntry && (
          
            
          
        )}
      

      {error && (
        
          {error}
        
      )}
    
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom,
  },
  label: {
    fontSize,
    marginBottom,
  },
  inputContainer: {
    position: 'relative',
  },
  input: {
    height,
    borderRadius,
    paddingHorizontal,
    fontSize,
  },
  eyeButton: {
    position: 'absolute',
    right,
    top,
  },
  error: {
    color: '#cf6679',
    fontSize,
    marginTop,
  },
});

export default Input;
