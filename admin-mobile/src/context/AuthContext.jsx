import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import authService, { User, LoginData } from '../api/authService';



// Create the context with a default value
const AuthContext = createContext({
  user: null,
  isLoading: false,
  isAuthenticated: false,
  login: () => {},
  logout: () => {},
  hasPermission: () => false,
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Load user from AsyncStorage on app start
    const loadUser = async () => {
      try {
        const userJson = await AsyncStorage.getItem('user');
        const token = await AsyncStorage.getItem('auth_token');

        if (userJson && token) {
          const parsedUser = JSON.parse(userJson);

          // Verify that the user is an admin
          if (parsedUser.role !== 'admin') {
            console.error('Non-admin user tried to access admin app');
            await AsyncStorage.removeItem('auth_token');
            await AsyncStorage.removeItem('user');
            setUser(null);
            setIsAuthenticated(false);
          } else {
            setUser(parsedUser);
            setIsAuthenticated(true);
          }
        }
      } catch (error) {
        console.error('Error loading user:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  const login = async (data) => {
    try {
      setIsLoading(true);
      console.log('AuthContext admin login with:', { email: data.email });

      const response = await authService.login(data);
      console.log('AuthContext login successful');

      // Verify that the user is an admin
      if (response.user.role !== 'admin') {
        throw new Error('Unauthorized: only admin users can access this application');
      }

      setUser(response.user);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('AuthContext login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user has specific permission
  const hasPermission = (permission) => {
    // For the admin app, we only need to check if the user is an admin
    if (!user) return false;

    // Only admin users should be able to access this app
    return user.role === 'admin';
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated,
        login,
        logout,
        hasPermission,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Export the context
export default AuthContext;
