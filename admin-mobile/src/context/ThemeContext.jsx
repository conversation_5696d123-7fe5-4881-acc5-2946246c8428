import React, { createContext, useState, useContext, useEffect, useMemo } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from './AuthContext';

// Theme definitions
const lightTheme = {
  colors: {
    primary: '#6200ee',
    secondary: '#03dac6',
    background: '#ffffff',
    surface: '#f5f5f5',
    text: '#000000',
    textSecondary: '#666666',
    border: '#e0e0e0',
    error: '#cf6679',
  },
};

const darkTheme = {
  colors: {
    primary: '#bb86fc',
    secondary: '#03dac6',
    background: '#121212',
    surface: '#1e1e1e',
    text: '#ffffff',
    textSecondary: '#aaaaaa',
    border: '#333333',
    error: '#cf6679',
  },
};

// Create the context with a default value
const ThemeContext = createContext({
  theme: lightTheme,
  isDark: false,
  setTheme: () => {},
  themePreference: 'system',
});

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider = ({ children }) => {
  const { user } = useAuth();
  const systemColorScheme = useColorScheme();
  const [themePreference, setThemePreference] = useState('system');

  // Load theme preference from user settings or AsyncStorage
  useEffect(() => {
    const loadTheme = async () => {
      try {
        // First check if user has theme preference
        if (user?.theme) {
          setThemePreference(user.theme);
          return;
        }

        // Otherwise load from AsyncStorage
        const savedTheme = await AsyncStorage.getItem('theme_preference');
        if (savedTheme) {
          setThemePreference(savedTheme);
        }
      } catch (error) {
        console.error('Error loading theme preference:', error);
      }
    };

    loadTheme();
  }, [user]);

  // Compute current theme based on preference and system theme
  const currentTheme = useMemo(() => {
    if (themePreference === 'system') {
      return systemColorScheme === 'dark' ? darkTheme : lightTheme;
    }
    return themePreference === 'dark' ? darkTheme : lightTheme;
  }, [themePreference, systemColorScheme]);

  const isDark = themePreference === 'dark' || (themePreference === 'system' && systemColorScheme === 'dark');

  const setTheme = async (theme) => {
    setThemePreference(theme);
    try {
      await AsyncStorage.setItem('theme_preference', theme);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  return (
    <ThemeContext.Provider
      value={{
        theme: currentTheme,
        isDark,
        setTheme,
        themePreference,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

// Export the context
export default ThemeContext;
