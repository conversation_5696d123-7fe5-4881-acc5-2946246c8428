import React, { createContext, useState, useContext, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from './AuthContext';

// Create the context with a default value
const ThemeContext = createContext({
  theme: 'light',
  isDark: false,
  setTheme: () => {},
  themePreference: 'system',
});

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider = ({ children }) => {
  const { user } = useAuth();
  const systemColorScheme = useColorScheme();
  const [themePreference, setThemePreference] = useState('system');
  const [currentTheme, setCurrentTheme] = useState('light');
  const [isDark, setIsDark] = useState(false);

  // Load theme preference from user settings or AsyncStorage
  useEffect(() => {
    const loadTheme = async () => {
      try {
        // First check if user has theme preference
        if (user?.theme) {
          setThemePreference(user.theme);
          return;
        }

        // Otherwise load from AsyncStorage
        const savedTheme = await AsyncStorage.getItem('theme_preference');
        if (savedTheme) {
          setThemePreference(savedTheme);
        }
      } catch (error) {
        console.error('Error loading theme preference:', error);
      }
    };

    loadTheme();
  }, [user]);

  // Update current theme based on preference and system theme
  useEffect(() => {
    if (themePreference === 'system') {
      setCurrentTheme(systemColorScheme || 'light');
      setIsDark(systemColorScheme === 'dark');
    } else {
      setCurrentTheme(themePreference);
      setIsDark(themePreference === 'dark');
    }
  }, [themePreference, systemColorScheme]);

  const setTheme = async (theme) => {
    setThemePreference(theme);
    try {
      await AsyncStorage.setItem('theme_preference', theme);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  return (
    <ThemeContext.Provider
      value={{
        theme: currentTheme,
        isDark,
        setTheme,
        themePreference,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

// Export the context
export default ThemeContext;
