import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import translations
import en from './locales/en.json';
import bn from './locales/bn.json';

// Initialize i18next
i18n
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v3',
    resources: {
      en: {
        translation: en,
      },
      bn: {
        translation: bn,
      },
    },
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

// Function to load language from AsyncStorage
export const loadLanguage = async () => {
  try {
    const language = await AsyncStorage.getItem('language');
    if (language) {
      i18n.changeLanguage(language);
    }
  } catch (error) {
    console.error('Error loading language:', error);
  }
};

// Function to change language
export const changeLanguage = async (language: string) => {
  try {
    await AsyncStorage.setItem('language', language);
    i18n.changeLanguage(language);
  } catch (error) {
    console.error('Error changing language:', error);
  }
};

export default i18n;
