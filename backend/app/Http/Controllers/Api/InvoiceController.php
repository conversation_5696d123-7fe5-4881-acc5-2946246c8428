<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Invoice::with(['order.customer']);

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range if provided
        if ($request->has('from_date') && $request->has('to_date')) {
            $query->whereBetween('invoice_date', [$request->from_date, $request->to_date]);
        }

        $invoices = $query->latest()->paginate(10);
        return response()->json($invoices);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date',
            'total_amount' => 'required|numeric|min:0',
            'paid_amount' => 'required|numeric|min:0',
            'status' => 'required|in:paid,partial,unpaid',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // Check if order already has an invoice
            $existingInvoice = Invoice::where('order_id', $request->order_id)->first();
            if ($existingInvoice) {
                return response()->json(['message' => 'Order already has an invoice'], 400);
            }

            // Generate invoice number
            $invoiceNumber = 'INV-' . date('Ymd') . '-' . rand(1000, 9999);

            // Create invoice
            $invoice = Invoice::create([
                'order_id' => $request->order_id,
                'invoice_number' => $invoiceNumber,
                'invoice_date' => $request->invoice_date,
                'due_date' => $request->due_date,
                'total_amount' => $request->total_amount,
                'paid_amount' => $request->paid_amount,
                'status' => $request->status,
                'notes' => $request->notes,
            ]);

            DB::commit();

            return response()->json([
                'invoice' => Invoice::with('order.customer')->find($invoice->id),
                'message' => 'Invoice created successfully'
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to create invoice: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $invoice = Invoice::with(['order.customer', 'order.orderItems.product'])->findOrFail($id);
        return response()->json($invoice);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'due_date' => 'nullable|date',
            'paid_amount' => 'nullable|numeric|min:0',
            'status' => 'nullable|in:paid,partial,unpaid',
            'notes' => 'nullable|string',
        ]);

        $invoice = Invoice::findOrFail($id);

        // Update paid amount and status
        $invoice->update($request->only(['due_date', 'paid_amount', 'status', 'notes']));

        // If paid amount equals total amount, set status to paid
        if ($invoice->paid_amount >= $invoice->total_amount) {
            $invoice->status = 'paid';
            $invoice->save();
        } else if ($invoice->paid_amount > 0) {
            $invoice->status = 'partial';
            $invoice->save();
        } else {
            $invoice->status = 'unpaid';
            $invoice->save();
        }

        return response()->json([
            'invoice' => $invoice,
            'message' => 'Invoice updated successfully'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $invoice = Invoice::findOrFail($id);
        $invoice->delete();

        return response()->json(null, 204);
    }

    /**
     * Get order invoice
     */
    public function getOrderInvoice(string $orderId)
    {
        $order = Order::findOrFail($orderId);
        $invoice = $order->invoice;

        if (!$invoice) {
            return response()->json(['message' => 'No invoice found for this order'], 404);
        }

        return response()->json($invoice);
    }
}
