<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Measurement;
use Illuminate\Http\Request;

class MeasurementController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Measurement::with('customer');

        if ($request->has('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        $measurements = $query->latest()->paginate(10);
        return response()->json($measurements);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'name' => 'nullable|string|max:255',
            'chest' => 'nullable|numeric',
            'waist' => 'nullable|numeric',
            'hip' => 'nullable|numeric',
            'shoulder' => 'nullable|numeric',
            'sleeve_length' => 'nullable|numeric',
            'neck' => 'nullable|numeric',
            'inseam' => 'nullable|numeric',
            'outseam' => 'nullable|numeric',
            'thigh' => 'nullable|numeric',
            'calf' => 'nullable|numeric',
            'arm_length' => 'nullable|numeric',
            'back_width' => 'nullable|numeric',
            'height' => 'nullable|numeric',
            'notes' => 'nullable|string',
        ]);

        $measurement = Measurement::create($request->all());
        return response()->json($measurement, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $measurement = Measurement::with('customer')->findOrFail($id);
        return response()->json($measurement);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
            'chest' => 'nullable|numeric',
            'waist' => 'nullable|numeric',
            'hip' => 'nullable|numeric',
            'shoulder' => 'nullable|numeric',
            'sleeve_length' => 'nullable|numeric',
            'neck' => 'nullable|numeric',
            'inseam' => 'nullable|numeric',
            'outseam' => 'nullable|numeric',
            'thigh' => 'nullable|numeric',
            'calf' => 'nullable|numeric',
            'arm_length' => 'nullable|numeric',
            'back_width' => 'nullable|numeric',
            'height' => 'nullable|numeric',
            'notes' => 'nullable|string',
        ]);

        $measurement = Measurement::findOrFail($id);
        $measurement->update($request->all());

        return response()->json($measurement);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $measurement = Measurement::findOrFail($id);
        $measurement->delete();

        return response()->json(null, 204);
    }

    /**
     * Get customer measurements
     */
    public function getCustomerMeasurements(string $customerId)
    {
        $customer = Customer::findOrFail($customerId);
        $measurements = $customer->measurements()->latest()->get();

        return response()->json($measurements);
    }
}
