<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Order::with(['customer', 'user']);

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range if provided
        if ($request->has('from_date') && $request->has('to_date')) {
            $query->whereBetween('order_date', [$request->from_date, $request->to_date]);
        }

        $orders = $query->latest()->paginate(10);
        return response()->json($orders);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'order_date' => 'required|date',
            'delivery_date' => 'nullable|date',
            'total_amount' => 'required|numeric|min:0',
            'paid_amount' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'status' => 'required|in:pending,processing,completed,delivered,cancelled',
            'notes' => 'nullable|string',
            'order_items' => 'required|array|min:1',
            'order_items.*.product_id' => 'required|exists:products,id',
            'order_items.*.quantity' => 'required|integer|min:1',
            'order_items.*.unit_price' => 'required|numeric|min:0',
            'order_items.*.total_price' => 'required|numeric|min:0',
            'order_items.*.notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // Generate order number
            $orderNumber = 'ORD-' . date('Ymd') . '-' . rand(1000, 9999);

            // Create order
            $order = Order::create([
                'customer_id' => $request->customer_id,
                'user_id' => $request->user()->id,
                'order_number' => $orderNumber,
                'order_date' => $request->order_date,
                'delivery_date' => $request->delivery_date,
                'total_amount' => $request->total_amount,
                'paid_amount' => $request->paid_amount,
                'discount' => $request->discount ?? 0,
                'status' => $request->status,
                'notes' => $request->notes,
            ]);

            // Create order items
            foreach ($request->order_items as $item) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['total_price'],
                    'notes' => $item['notes'] ?? null,
                ]);

                // Update product quantity
                $product = Product::find($item['product_id']);
                $product->quantity -= $item['quantity'];
                $product->save();
            }

            DB::commit();

            return response()->json([
                'order' => Order::with(['customer', 'orderItems.product'])->find($order->id),
                'message' => 'Order created successfully'
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to create order: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $order = Order::with(['customer', 'user', 'orderItems.product', 'invoice'])->findOrFail($id);
        return response()->json($order);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'delivery_date' => 'nullable|date',
            'paid_amount' => 'nullable|numeric|min:0',
            'status' => 'nullable|in:pending,processing,completed,delivered,cancelled',
            'notes' => 'nullable|string',
        ]);

        $order = Order::findOrFail($id);
        $order->update($request->only(['delivery_date', 'paid_amount', 'status', 'notes']));

        return response()->json([
            'order' => $order,
            'message' => 'Order updated successfully'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $order = Order::findOrFail($id);

        // Check if order has invoice
        if ($order->invoice) {
            return response()->json(['message' => 'Cannot delete order with an invoice'], 400);
        }

        // Restore product quantities
        foreach ($order->orderItems as $item) {
            $product = $item->product;
            $product->quantity += $item->quantity;
            $product->save();
        }

        $order->delete();

        return response()->json(null, 204);
    }

    /**
     * Get customer orders
     */
    public function getCustomerOrders(string $customerId)
    {
        $customer = Customer::findOrFail($customerId);
        $orders = $customer->orders()->with('orderItems.product')->latest()->get();

        return response()->json($orders);
    }

    /**
     * Get dashboard stats
     */
    public function getDashboardStats()
    {
        $totalOrders = Order::count();
        $totalRevenue = Order::sum('total_amount');
        $pendingOrders = Order::where('status', 'pending')->count();
        $completedOrders = Order::where('status', 'completed')->count();

        return response()->json([
            'total_orders' => $totalOrders,
            'total_revenue' => $totalRevenue,
            'pending_orders' => $pendingOrders,
            'completed_orders' => $completedOrders,
        ]);
    }

    /**
     * Get recent orders for dashboard
     */
    public function getRecentOrders()
    {
        $recentOrders = Order::with(['customer'])
            ->latest()
            ->take(5)
            ->get();

        return response()->json($recentOrders);
    }

    /**
     * Get orders report
     */
    public function getOrdersReport(Request $request)
    {
        $request->validate([
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date',
            'status' => 'nullable|in:pending,processing,completed,delivered,cancelled',
        ]);

        $query = Order::with(['customer', 'user']);

        if ($request->has('from_date') && $request->has('to_date')) {
            $query->whereBetween('order_date', [$request->from_date, $request->to_date]);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $orders = $query->latest()->get();

        // Calculate statistics
        $totalOrders = $orders->count();
        $totalRevenue = $orders->sum('total_amount');
        $avgOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;

        // Group by status for pie chart
        $statusCounts = $orders->groupBy('status')
            ->map(function ($group) {
                return $group->count();
            });

        // Group by month for bar chart
        $monthlyData = $orders->groupBy(function ($order) {
            return Carbon::parse($order->order_date)->format('Y-m');
        })->map(function ($group) {
            return [
                'count' => $group->count(),
                'revenue' => $group->sum('total_amount'),
            ];
        });

        return response()->json([
            'orders' => $orders,
            'total_orders' => $totalOrders,
            'total_revenue' => $totalRevenue,
            'avg_order_value' => $avgOrderValue,
            'status_counts' => $statusCounts,
            'monthly_data' => $monthlyData,
        ]);
    }

    /**
     * Get revenue report
     */
    public function getRevenueReport(Request $request)
    {
        $request->validate([
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date',
        ]);

        $fromDate = $request->from_date ? Carbon::parse($request->from_date) : Carbon::now()->subMonths(6);
        $toDate = $request->to_date ? Carbon::parse($request->to_date) : Carbon::now();

        $monthlyRevenue = DB::table('orders')
            ->select(DB::raw('DATE_FORMAT(order_date, "%Y-%m") as month'), DB::raw('SUM(total_amount) as revenue'))
            ->whereBetween('order_date', [$fromDate, $toDate])
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return response()->json($monthlyRevenue);
    }
}
