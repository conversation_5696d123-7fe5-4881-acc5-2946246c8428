<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Measurement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_id',
        'name',
        'chest',
        'waist',
        'hip',
        'shoulder',
        'sleeve_length',
        'neck',
        'inseam',
        'outseam',
        'thigh',
        'calf',
        'arm_length',
        'back_width',
        'height',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'chest' => 'decimal:2',
        'waist' => 'decimal:2',
        'hip' => 'decimal:2',
        'shoulder' => 'decimal:2',
        'sleeve_length' => 'decimal:2',
        'neck' => 'decimal:2',
        'inseam' => 'decimal:2',
        'outseam' => 'decimal:2',
        'thigh' => 'decimal:2',
        'calf' => 'decimal:2',
        'arm_length' => 'decimal:2',
        'back_width' => 'decimal:2',
        'height' => 'decimal:2',
    ];

    /**
     * Get the customer that owns the measurement.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }
}
