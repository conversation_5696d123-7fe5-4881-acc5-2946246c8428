<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\InvoiceController;
use App\Http\Controllers\Api\MeasurementController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ProductController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);

// Test route to check if API is working
Route::get('/test', function() {
    return response()->json([
        'message' => 'API is working!',
        'timestamp' => now()->toDateTimeString(),
    ]);
});

// Protected routes for all authenticated users
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    Route::put('/user', [AuthController::class, 'update']);
    Route::put('/user/password', [AuthController::class, 'updatePassword']);

    // Customers
    Route::apiResource('customers', CustomerController::class);

    // Measurements
    Route::apiResource('measurements', MeasurementController::class);
    Route::get('/customers/{customer}/measurements', [MeasurementController::class, 'getCustomerMeasurements']);

    // Products
    Route::get('products', [ProductController::class, 'index']);
    Route::get('products/{product}', [ProductController::class, 'show']);

    // Orders
    Route::apiResource('orders', OrderController::class);
    Route::get('/customers/{customer}/orders', [OrderController::class, 'getCustomerOrders']);

    // Invoices
    Route::apiResource('invoices', InvoiceController::class);
    Route::get('/orders/{order}/invoice', [InvoiceController::class, 'getOrderInvoice']);

    // Dashboard
    Route::get('/dashboard/stats', [OrderController::class, 'getDashboardStats']);
    Route::get('/dashboard/recent-orders', [OrderController::class, 'getRecentOrders']);
    Route::get('/dashboard/inventory-summary', [ProductController::class, 'getInventorySummary']);
});

// Admin-only routes
Route::middleware(['auth:sanctum', 'role:admin'])->prefix('admin')->group(function () {
    // User management
    Route::get('/users', [AuthController::class, 'index']);
    Route::get('/users/{user}', [AuthController::class, 'show']);
    Route::post('/users', [AuthController::class, 'store']);
    Route::put('/users/{user}', [AuthController::class, 'updateUser']);
    Route::delete('/users/{user}', [AuthController::class, 'destroy']);
    Route::put('/users/{user}/role', [AuthController::class, 'updateRole']);
    Route::put('/users/{user}/status', [AuthController::class, 'updateStatus']);

    // Product management (create, update, delete)
    Route::post('/products', [ProductController::class, 'store']);
    Route::put('/products/{product}', [ProductController::class, 'update']);
    Route::delete('/products/{product}', [ProductController::class, 'destroy']);

    // Reports
    Route::get('/reports/orders', [OrderController::class, 'getOrdersReport']);
    Route::get('/reports/revenue', [OrderController::class, 'getRevenueReport']);
    Route::get('/reports/users', [AuthController::class, 'getUsersReport']);
    Route::get('/reports/inventory', [ProductController::class, 'getInventoryReport']);
});
