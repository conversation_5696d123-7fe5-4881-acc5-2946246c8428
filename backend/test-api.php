<?php
/**
 * Simple script to test the API directly
 * Run this script from the command line: php test-api.php
 */

// Test endpoints
$endpoints = [
    'test' => 'http://localhost:8000/api/test',
    'login' => 'http://localhost:8000/api/login',
    'direct_ip_test' => 'http://*************:8000/api/test',
    'direct_ip_login' => 'http://*************:8000/api/login',
];

// Test login credentials
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password',
];

// Function to test GET endpoint
function testGetEndpoint($name, $url) {
    echo "\n\nTesting $name endpoint: $url\n";
    echo "----------------------------------------\n";
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP Status Code: $httpCode\n";
    
    if ($error) {
        echo "cURL Error: $error\n";
    } else {
        echo "Response:\n";
        $jsonResponse = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo json_encode($jsonResponse, JSON_PRETTY_PRINT) . "\n";
        } else {
            echo $response . "\n";
        }
    }
}

// Function to test POST endpoint
function testPostEndpoint($name, $url, $data) {
    echo "\n\nTesting $name endpoint: $url\n";
    echo "----------------------------------------\n";
    echo "With data: " . json_encode($data) . "\n";
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP Status Code: $httpCode\n";
    
    if ($error) {
        echo "cURL Error: $error\n";
    } else {
        echo "Response:\n";
        $jsonResponse = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo json_encode($jsonResponse, JSON_PRETTY_PRINT) . "\n";
        } else {
            echo $response . "\n";
        }
    }
}

// Run tests
echo "=================================================\n";
echo "API TEST SCRIPT\n";
echo "=================================================\n";

// Test GET endpoints
testGetEndpoint('test', $endpoints['test']);
testGetEndpoint('direct_ip_test', $endpoints['direct_ip_test']);

// Test POST endpoints
testPostEndpoint('login', $endpoints['login'], $loginData);
testPostEndpoint('direct_ip_login', $endpoints['direct_ip_login'], $loginData);

echo "\n=================================================\n";
echo "TESTS COMPLETED\n";
echo "=================================================\n";
