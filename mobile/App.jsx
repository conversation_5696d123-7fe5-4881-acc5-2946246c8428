import React, { useEffect } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AuthProvider } from './src/context/AuthContext';
import { ThemeProvider } from './src/context/ThemeContext';
import { loadSavedLanguage } from './src/i18n';
import { ExpoRoot } from 'expo-router';

export default function App() {
  useEffect(() => {
    // Load saved language
    loadSavedLanguage();
  }, []);

  return (
    
      
        
          
        
      
    
  );
}
