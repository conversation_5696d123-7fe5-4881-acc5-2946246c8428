import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Image, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../src/context/AuthContext';
import { useTheme } from '../../src/context/ThemeContext';
import { Button, Input, Loading } from '../../src/components/ui';
import { validationUtils } from '../../src/utils';
import ApiTest from '../../src/components/ApiTest';

export default function LoginScreen() {
  const { t } = useTranslation();
  const { login } = useAuth();
  const { theme } = useTheme();
  const router = useRouter();
  const isDark = theme === 'dark';

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const validate = () => {
    const newErrors: { email?: string; password?: string } = {};

    if (!validationUtils.validateRequired(email)) {
      newErrors.email = t('email_required');
    } else if (!validationUtils.validateEmail(email)) {
      newErrors.email = t('email_invalid');
    }

    if (!validationUtils.validateRequired(password)) {
      newErrors.password = t('password_required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validate()) return;

    setIsLoading(true);
    try {
      // For testing purposes, use the test user we created
      console.log('Attempting login with:', { email });

      // Use the test credentials if the form is empty (for testing)
      const loginData = {
        email || '<EMAIL>',
        password || 'password'
      };

      // For debugging, always use test credentials
      if (!email && !password) {
        console.log('Using test credentials');
      }

      console.log('Using login data:', { email.email, passwordLength.password.length });
      await login(loginData);
      console.log('Login successful');
      // Navigation will be handled by the auth layout
    } catch (error) {
      console.error('Login error:', error);

      // More detailed error logging
      if (error.response) {
        // The request w and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response:', {
          data.response.data,
          status.response.status,
          headers.response.headers,
        });

        if (error.response.data?.message) {
          alert(error.response.data.message);
        } else if (error.response.data?.errors) {
          // Laravel validation errors
          const errorMessages = Object.values(error.response.data.errors).flat();
          alert(errorMessages.join('\n'));
        } else {
          alert(`${t('login_failed')}: ${error.response.status}`);
        }
      } else if (error.request) {
        // The request w but no response w
        console.error('Error request:', error.request);
        alert(`${t('login_failed')}: No response from server. Make sure the Laravel backend is running.`);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', error.message);
        alert(`${t('login_failed')}: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    
      
        
          
            {t('app_name')}
          
        

        
          
            {t('login')}
          

          

          

          

          {/* Debug button for testing */}
           {
              setEmail('<EMAIL>');
              setPassword('password');
              setTimeout(() => {
                handleLogin();
              }, 500);
            }}
            style={[styles.button, { marginTop, backgroundColor: '#4caf50' }]}
          />

          
            
              {t('dont_have_account')}
            
             router.push('/register')}>
              {t('register')}
            
          

          {/* API Test Component */}
          
            
          
        
      

      {isLoading && }
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  scrollContent: {
    flexGrow,
    justifyContent: 'center',
    padding,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom,
  },
  logo: {
    width,
    height,
    resizeMode: 'contain',
  },
  appName: {
    fontSize,
    fontWeight: 'bold',
    marginTop,
  },
  formContainer: {
    width: '100%',
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  button: {
    marginTop,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop,
  },
  registerText: {
    color: '#6200ee',
    marginLeft,
    fontWeight: 'bold',
  },
  apiTestContainer: {
    marginTop,
    borderTopWidth,
    borderTopColor: '#ddd',
    paddingTop,
  },
});
