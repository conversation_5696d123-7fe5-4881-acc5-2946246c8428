import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../src/context/AuthContext';
import { useTheme } from '../../src/context/ThemeContext';
import { Button, Input, Loading } from '../../src/components/ui';
import { validationUtils } from '../../src/utils';

export default function RegisterScreen() {
  const { t } = useTranslation();
  const { register } = useAuth();
  const { theme } = useTheme();
  const router = useRouter();
  const isDark = theme === 'dark';

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const validate = () => {
    const newErrors: {
      name?: string;
      email?: string;
      phone?: string;
      password?: string;
      confirmPassword?: string;
    } = {};
    
    if (!validationUtils.validateRequired(name)) {
      newErrors.name = t('name_required');
    }
    
    if (!validationUtils.validateRequired(email)) {
      newErrors.email = t('email_required');
    } else if (!validationUtils.validateEmail(email)) {
      newErrors.email = t('email_invalid');
    }
    
    if (phone && !validationUtils.validatePhone(phone)) {
      newErrors.phone = t('phone_invalid');
    }
    
    if (!validationUtils.validateRequired(password)) {
      newErrors.password = t('password_required');
    } else if (!validationUtils.validateMinLength(password, 8)) {
      newErrors.password = t('password_min_length');
    }
    
    if (!validationUtils.validateRequired(confirmPassword)) {
      newErrors.confirmPassword = t('confirm_password_required');
    } else if (!validationUtils.validatePasswordMatch(password, confirmPassword)) {
      newErrors.confirmPassword = t('passwords_not_match');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validate()) return;
    
    setIsLoading(true);
    try {
      await register({
        name,
        email,
        phone,
        password,
        password_confirmation,
      });
      // Navigation will be handled by the auth layout
    } catch (error) {
      console.error('Register error:', error);
      if (error.response?.data?.message) {
        alert(error.response.data.message);
      } else {
        alert(t('register_failed'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    
      
        
          
            {t('register')}
          
          
          
          
          
          
          
          
          
          
          
          
          
          
          
            
              {t('already_have_account')}
            
             router.push('/login')}>
              {t('login')}
            
          
        
      
      
      {isLoading && }
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  scrollContent: {
    flexGrow,
    justifyContent: 'center',
    padding,
  },
  formContainer: {
    width: '100%',
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  button: {
    marginTop,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop,
    marginBottom,
  },
  loginText: {
    color: '#6200ee',
    marginLeft,
    fontWeight: 'bold',
  },
});
