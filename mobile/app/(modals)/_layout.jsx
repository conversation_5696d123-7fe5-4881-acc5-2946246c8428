import { Stack } from 'expo-router';
import { useEffect } from 'react';
import { useRouter, useSegments } from 'expo-router';
import { useAuth } from '../../src/context/AuthContext';
import { useTheme } from '../../src/context/ThemeContext';

export default function ModalsLayout() {
  const { isAuthenticated, isLoading } = useAuth();
  const { theme } = useTheme();
  const segments = useSegments();
  const router = useRouter();
  const isDark = theme === 'dark';

  useEffect(() => {
    if (!isLoading) {
      // Check if the user is authenticated
      if (!isAuthenticated) {
        // Redirect to the login page if they're not signed in
        router.replace('/login');
      }
    }
  }, [isAuthenticated, isLoading, segments, router]);

  if (isLoading || !isAuthenticated) {
    return null;
  }

  return (
    
      
      
      
      
      
      
      
      
      
      
    
  );
}
