import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, Alert, RefreshControl } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../../src/context/ThemeContext';
import { Card, Loading, Button, StatusBadge } from '../../../src/components/ui';
import { customerService, Customer } from '../../../src/api/customerService';
import { orderService, Order } from '../../../src/api/orderService';
import { measurementService, Measurement } from '../../../src/api/measurementService';
import { Ionicons } from '@expo/vector-icons';
import { currencyUtils, dateUtils } from '../../../src/utils';

export default function CustomerDetailScreen() {
  const { id } = useLocalSearchParams();
  const { t } = useTranslation();
  const { theme } = useTheme();
  const router = useRouter();
  const isDark = theme === 'dark';
  
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [customer, setCustomer] = useState(null);
  const [orders, setOrders] = useState([]);
  const [measurements, setMeasurements] = useState([]);
  const [activeTab, setActiveTab] = useState('info');

  const loadCustomerData = async () => {
    if (!id) return;
    
    try {
      setIsLoading(true);
      const [customerData, ordersData, measurementsData] = await Promise.all([
        customerService.getCustomer(Number(id)),
        customerService.getCustomerOrders(Number(id)),
        customerService.getCustomerMeasurements(Number(id)),
      ]);
      
      setCustomer(customerData);
      setOrders(ordersData);
      setMeasurements(measurementsData);
    } catch (error) {
      console.error('Error loading customer data:', error);
      Alert.alert(t('error'), t('failed_to_load_customer'));
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadCustomerData();
  }, [id]);

  const onRefresh = () => {
    setRefreshing(true);
    loadCustomerData();
  };

  const handleEditCustomer = () => {
    // Navigate to edit customer screen
    router.push(`/customers/edit/${id}`);
  };

  const handleDeleteCustomer = () => {
    Alert.alert(
      t('delete_customer'),
      t('delete_customer_confirm'),
      [
        {
          text('cancel'),
          style: 'cancel',
        },
        {
          text('delete'),
          onPress () => {
            try {
              setIsLoading(true);
              await customerService.deleteCustomer(Number(id));
              router.back();
            } catch (error) {
              console.error('Error deleting customer:', error);
              Alert.alert(t('error'), t('failed_to_delete_customer'));
              setIsLoading(false);
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  const handleAddOrder = () => {
    router.push({
      pathname: '/orders/new',
      params: { customerId },
    });
  };

  const handleAddMeasurement = () => {
    router.push({
      pathname: '/measurements/new',
      params: { customerId },
    });
  };

  const renderInfoTab = () => (
    
      
        
          
            {t('name')}:
          
          
            {customer?.name}
          
        
        
        {customer?.phone && (
          
            
              {t('phone')}:
            
            
              {customer.phone}
            
          
        )}
        
        {customer?.email && (
          
            
              {t('email')}:
            
            
              {customer.email}
            
          
        )}
        
        {customer?.address && (
          
            
              {t('address')}:
            
            
              {customer.address}
            
          
        )}
        
        {customer?.notes && (
          
            
              {t('notes')}:
            
            
              {customer.notes}
            
          
        )}
      
      
      
        }
        />
        
        }
        />
      
    
  );

  const renderOrdersTab = () => (
    
      
        
          {t('orders')}
        
        
        }
        />
      
      
      {orders.length > 0 ? (
        orders.map((order) => (
           router.push(`/orders/${order.id}`)}
          >
            
              
                
                  {order.order_number}
                
                
              
              
              
                
                  {t('order_date')}: {dateUtils.formatDate(order.order_date)}
                
                
                {order.delivery_date && (
                  
                    {t('delivery_date')}: {dateUtils.formatDate(order.delivery_date)}
                  
                )}
                
                
                  {t('total_amount')}: {currencyUtils.formatCurrency(order.total_amount)}
                
              
            
          
        ))
      ) : (
        
          {t('no_orders_for_customer')}
        
      )}
    
  );

  const renderMeasurementsTab = () => (
    
      
        
          {t('measurements')}
        
        
        }
        />
      
      
      {measurements.length > 0 ? (
        measurements.map((measurement) => (
           router.push(`/measurements/${measurement.id}`)}
          >
            
              
                {measurement.name || t('measurement')} #{measurement.id}
              
              
              
                {measurement.chest && (
                  
                    
                      {t('chest')}:
                    
                    
                      {measurement.chest}
                    
                  
                )}
                
                {measurement.waist && (
                  
                    
                      {t('waist')}:
                    
                    
                      {measurement.waist}
                    
                  
                )}
                
                {measurement.hip && (
                  
                    
                      {t('hip')}:
                    
                    
                      {measurement.hip}
                    
                  
                )}
                
                
                  {t('view_more')}
                
              
            
          
        ))
      ) : (
        
          {t('no_measurements_for_customer')}
        
      )}
    
  );

  if (isLoading && !refreshing) {
    return ;
  }

  return (
    
      
        }
      >
        
          {customer?.name}
        
        
        
           setActiveTab('info')}
          >
            
              {t('info')}
            
          
          
           setActiveTab('orders')}
          >
            
              {t('orders')} ({orders.length})
            
          
          
           setActiveTab('measurements')}
          >
            
              {t('measurements')} ({measurements.length})
            
          
        
        
        
          {activeTab === 'info' && renderInfoTab()}
          {activeTab === 'orders' && renderOrdersTab()}
          {activeTab === 'measurements' && renderMeasurementsTab()}
        
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  scrollContent: {
    padding,
  },
  customerName: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom,
    borderBottomWidth,
    borderBottomColor: '#dddddd',
  },
  tabButton: {
    flex,
    paddingVertical,
    alignItems: 'center',
  },
  tabButtonText: {
    fontSize,
    fontWeight: '500',
  },
  tabContent: {
    flex,
  },
  infoCard: {
    marginBottom,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom,
  },
  infoLabel: {
    width,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom,
  },
  actionButton: {
    flex,
    marginHorizontal,
  },
  tabHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom,
  },
  tabTitle: {
    fontSize,
    fontWeight: 'bold',
  },
  orderCard: {
    marginBottom,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom,
  },
  orderNumber: {
    fontSize,
    fontWeight: 'bold',
  },
  orderDetails: {
    gap,
  },
  measurementCard: {
    marginBottom,
  },
  measurementName: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  measurementDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  measurementItem: {
    flexDirection: 'row',
    width: '50%',
    marginBottom,
  },
  measurementLabel: {
    marginRight,
    fontWeight: '500',
  },
  viewMore: {
    marginTop,
    fontWeight: '500',
  },
  emptyText: {
    textAlign: 'center',
    marginVertical,
    fontStyle: 'italic',
  },
});
