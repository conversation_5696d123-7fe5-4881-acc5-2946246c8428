import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../../src/context/ThemeContext';
import { Button, Input, Loading } from '../../../src/components/ui';
import { customerService } from '../../../src/api/customerService';
import { validationUtils } from '../../../src/utils';

export default function AddCustomerScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const router = useRouter();
  const isDark = theme === 'dark';
  
  const [isLoading, setIsLoading] = useState(false);
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [address, setAddress] = useState('');
  const [notes, setNotes] = useState('');
  const [errors, setErrors] = useState({});

  const validate = () => {
    const newErrors: {
      name?: string;
      phone?: string;
      email?: string;
    } = {};
    
    if (!validationUtils.validateRequired(name)) {
      newErrors.name = t('name_required');
    }
    
    if (phone && !validationUtils.validatePhone(phone)) {
      newErrors.phone = t('phone_invalid');
    }
    
    if (email && !validationUtils.validateEmail(email)) {
      newErrors.email = t('email_invalid');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validate()) return;
    
    setIsLoading(true);
    try {
      await customerService.createCustomer({
        name,
        phone,
        email,
        address,
        notes,
      });
      
      Alert.alert(
        t('success'),
        t('customer_added'),
        [
          {
            text('ok'),
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      console.error('Error adding customer:', error);
      if (error.response?.data?.message) {
        Alert.alert(t('error'), error.response.data.message);
      } else {
        Alert.alert(t('error'), t('failed_to_add_customer'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    
      
        
          {t('add_customer')}
        
        
        
        
        
        
        
        
        
        
        
        
        
          
          
           router.back()}
            type="outline"
            style={styles.button}
            disabled={isLoading}
          />
        
      
      
      {isLoading && }
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  scrollContent: {
    padding,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop,
  },
  button: {
    flex,
    marginHorizontal,
  },
});
