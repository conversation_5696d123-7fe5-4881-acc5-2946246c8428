import React, { useEffect } from 'react';
import { useRouter, useSegments } from 'expo-router';
import { useAuth } from '../../src/context/AuthContext';
import RoleBasedNavigator from '../../src/navigation/RoleBasedNavigator';

export default function TabLayout() {
  const { isAuthenticated, isLoading } = useAuth();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      // Check if the user is authenticated
      if (!isAuthenticated) {
        // Redirect to the login page if they're not signed in
        router.replace('/login');
      }
    }
  }, [isAuthenticated, isLoading, segments, router]);

  if (isLoading || !isAuthenticated) {
    return null;
  }

  // Use the role-based navigator which will render the appropriate tabs
  // based on the user's role (admin or regular user)
  return ;
}
