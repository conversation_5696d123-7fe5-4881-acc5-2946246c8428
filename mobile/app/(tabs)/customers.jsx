import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, FlatList, TouchableOpacity, TextInput, RefreshControl } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { Card, Loading, FloatingActionButton } from '../../src/components/ui';
import { customerService, Customer } from '../../src/api/customerService';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

export default function CustomersScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const router = useRouter();
  const isDark = theme === 'dark';
  
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMorePages, setHasMorePages] = useState(true);

  const loadCustomers = async (page = 1, refresh = false) => {
    try {
      if (refresh) {
        setIsLoading(true);
      }
      
      const response = await customerService.getCustomers(page);
      
      const newCustomers = response.data;
      
      if (page === 1) {
        setCustomers(newCustomers);
        setFilteredCustomers(newCustomers);
      } else {
        setCustomers(prevCustomers => [...prevCustomers, ...newCustomers]);
        setFilteredCustomers(prevCustomers => [...prevCustomers, ...newCustomers]);
      }
      
      setHasMorePages(response.next_page_url !== null);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadCustomers();
  }, []);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCustomers(customers);
    } else {
      const filtered = customers.filter(customer => 
        customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (customer.phone && customer.phone.includes(searchQuery)) ||
        (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredCustomers(filtered);
    }
  }, [searchQuery, customers]);

  const onRefresh = () => {
    setRefreshing(true);
    setSearchQuery('');
    loadCustomers(1, true);
  };

  const loadMoreCustomers = () => {
    if (hasMorePages && !isLoading && searchQuery.trim() === '') {
      loadCustomers(currentPage + 1);
    }
  };

  const handleCustomerPress = (customerId) => {
    router.push(`/customers/${customerId}`);
  };

  const renderCustomerItem = ({ item }: { item }) => (
     handleCustomerPress(item.id)}>
      
        
          
            {item.name}
          
        
        
        
          {item.phone && (
            
              
              {item.phone}
            
          )}
          
          {item.email && (
            
              
              {item.email}
            
          )}
          
          {item.address && (
            
              
              
                {item.address}
              
            
          )}
        
      
    
  );

  const fabActions = [
    {
      icon: 'person-add-outline',
      label('add_customer'),
      onPress: () => router.push('/customers/new'),
    },
  ];

  return (
    
      
        
          {t('customers')}
        
        
        
          
          
          {searchQuery.length > 0 && (
             setSearchQuery('')}>
              
            
          )}
        
      
      
      {isLoading && !refreshing ? (
        
      ) : (
        <>
          {filteredCustomers.length > 0 ? (
             item.id.toString()}
              contentContainerStyle={styles.listContent}
              onEndReached={loadMoreCustomers}
              onEndReachedThreshold={0.5}
              refreshControl={
                
              }
              ListFooterComponent={
                isLoading && hasMorePages ?  : null
              }
            />
          ) : (
            
              
              
                {searchQuery.length > 0
                  ? t('no_customers_found')
                  : t('no_customers_yet')}
              
            
          )}
        
      )}
      
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  header: {
    padding,
    paddingBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal,
    borderRadius,
    height,
    marginBottom,
  },
  searchInput: {
    flex,
    marginLeft,
    fontSize,
  },
  listContent: {
    padding,
    paddingTop,
    paddingBottom,
  },
  customerCard: {
    marginBottom,
  },
  customerHeader: {
    marginBottom,
  },
  customerName: {
    fontSize,
    fontWeight: 'bold',
  },
  customerDetails: {
    gap,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap,
  },
  emptyContainer: {
    flex,
    justifyContent: 'center',
    alignItems: 'center',
    padding,
  },
  emptyText: {
    fontSize,
    textAlign: 'center',
    marginTop,
  },
});
