import { Image } from 'expo-image';
import { Platform, StyleSheet } from 'react-native';

import { Collapsible } from '@/components/Collapsible';
import { ExternalLink } from '@/components/ExternalLink';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function TabTwoScreen() {
  return (
    
      }>
      
        Explore
      
      This app includes example code to help you get started.
      
        
          This app h screens:{' '}
          app/(tabs)/index.tsx and{' '}
          app/(tabs)/explore.tsx
        
        
          The layout file in app/(tabs)/_layout.tsx{' '}
          sets up the tab navigator.
        
        
          Learn more
        
      
      
        
          You can open this project on Android, iOS, and the web. To open the web version, press{' '}
          w in the terminal running this project.
        
      
      
        
          For static images, you can use the @2x and{' '}
          @3x suffixes to provide files for
          different screen densities
        
        
        
          Learn more
        
      
      
        
          Open app/_layout.tsx to see how to load{' '}
          
            custom fonts such  one.
          
        
        
          Learn more
        
      
      
        
          This template h and dark mode support. The{' '}
          useColorScheme() hook lets you inspect
          what the user&apos;s current color scheme is, and so you can adjust UI colors accordingly.
        
        
          Learn more
        
      
      
        
          This template includes an example of an animated component. The{' '}
          components/HelloWave.tsx component uses
          the powerful react-native-reanimated{' '}
          library to create a waving hand animation.
        
        {Platform.select({
          ios: (
            
              The components/ParallaxScrollView.tsx{' '}
              component provides a parallax effect for the header image.
            
          ),
        })}
      
    
  );
}

const styles = StyleSheet.create({
  headerImage: {
    color: '#808080',
    bottom: -90,
    left: -35,
    position: 'absolute',
  },
  titleContainer: {
    flexDirection: 'row',
    gap,
  },
});
