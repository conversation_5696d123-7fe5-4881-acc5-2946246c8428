import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, ScrollView, RefreshControl, Dimensions } from 'react-native';
import { useTranslation } from 'react-i18next';
import { BarChart } from 'react-native-chart-kit';
import { useTheme } from '../../src/context/ThemeContext';
import { useAuth } from '../../src/context/AuthContext';
import { Card, Loading, StatusBadge, FloatingActionButton } from '../../src/components/ui';
import { orderService, productService } from '../../src/api';
import { currencyUtils, dateUtils } from '../../src/utils';
import { useRouter } from 'expo-router';

const screenWidth = Dimensions.get('window').width;

export default function DashboardScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const router = useRouter();
  const { isAdmin, userRole } = useAuth();
  const isDark = theme === 'dark';

  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    total_orders,
    total_revenue,
    pending_orders,
    completed_orders,
  });
  const [recentOrders, setRecentOrders] = useState([]);
  const [inventorySummary, setInventorySummary] = useState([]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const [statsData, ordersData, inventoryData] = await Promise.all([
        orderService.getDashboardStats(),
        orderService.getRecentOrders(),
        productService.getInventorySummary(),
      ]);

      setStats(statsData);
      setRecentOrders(ordersData);
      setInventorySummary(inventoryData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const chartConfig = {
    backgroundGradientFrom ? '#333333' : '#ffffff',
    backgroundGradientTo ? '#333333' : '#ffffff',
    color: (opacity = 1) => isDark ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    labelColor: (opacity = 1) => isDark ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    strokeWidth,
    barPercentage.5,
    useShadowColorFromDataset,
  };

  // Define different actions based on user role
  const adminFabActions = [
    {
      icon: 'person-add-outline',
      label('add_customer'),
      onPress: () => router.push('/customers/new'),
    },
    {
      icon: 'add-circle-outline',
      label('add_order'),
      onPress: () => router.push('/orders/new'),
    },
    {
      icon: 'shirt-outline',
      label('add_product'),
      onPress: () => router.push('/products/new'),
    },
    {
      icon: 'document-text-outline',
      label('add_invoice'),
      onPress: () => router.push('/invoices/new'),
    },
    {
      icon: 'people-outline',
      label('manage_users'),
      onPress: () => router.push('/users'),
    }
  ];

  const userFabActions = [
    {
      icon: 'person-add-outline',
      label('add_customer'),
      onPress: () => router.push('/customers/new'),
    },
    {
      icon: 'add-circle-outline',
      label('add_order'),
      onPress: () => router.push('/orders/new'),
    },
    {
      icon: 'document-text-outline',
      label('add_invoice'),
      onPress: () => router.push('/invoices/new'),
    },
  ];

  // Use the appropriate actions based on user role
  const fabActions = isAdmin ? adminFabActions : userFabActions;

  if (isLoading && !refreshing) {
    return ;
  }

  return (
    
      
        }
      >
        
          {isAdmin ? t('admin_dashboard') : t('dashboard')}
        

        {isAdmin && (
          
            {t('admin_mode')}
          
        )}

        
          
            
              {stats.total_orders}
            
            
              {t('total_orders')}
            
          

          
            
              {currencyUtils.formatCurrency(stats.total_revenue)}
            
            
              {t('total_revenue')}
            
          

          
            
              {stats.pending_orders}
            
            
              {t('pending_orders')}
            
          

          
            
              {stats.completed_orders}
            
            
              {t('completed_orders')}
            
          
        

        
          {t('recent_orders')}
        

        {recentOrders.length > 0 ? (
          recentOrders.map((order) => (
            
              
                
                  {order.order_number}
                
                
              

              
                
                  {t('customer')}: {order.customer?.name}
                
                
                  {t('order_date')}: {dateUtils.formatDate(order.order_date)}
                
                
                  {t('total_amount')}: {currencyUtils.formatCurrency(order.total_amount)}
                
              
            
          ))
        ) : (
          
            {t('no_recent_orders')}
          
        )}

        
          {t('inventory_summary')}
        

        {inventorySummary.length > 0 ? (
          
             item.category),
                datasets: [
                  {
                    data.map(item => item.quantity),
                  },
                ],
              }}
              width={screenWidth - 40}
              height={220}
              chartConfig={chartConfig}
              verticalLabelRotation={30}
              fromZero
              showValuesOnTopOfBars
            />
          
        ) : (
          
            {t('no_inventory_data')}
          
        )}
      

      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  scrollContent: {
    padding,
    paddingBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  roleIndicator: {
    fontSize,
    fontWeight: '500',
    marginBottom,
    fontStyle: 'italic',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom,
  },
  statCard: {
    width: '48%',
    marginBottom,
    padding,
    alignItems: 'center',
  },
  statValue: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  statLabel: {
    fontSize,
  },
  sectionTitle: {
    fontSize,
    fontWeight: 'bold',
    marginTop,
    marginBottom,
  },
  orderCard: {
    marginBottom,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom,
  },
  orderNumber: {
    fontSize,
    fontWeight: 'bold',
  },
  orderDetails: {
    gap,
  },
  chartCard: {
    padding,
    alignItems: 'center',
  },
  emptyText: {
    textAlign: 'center',
    marginVertical,
    fontStyle: 'italic',
  },
});
