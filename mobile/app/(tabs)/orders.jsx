import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, FlatList, TouchableOpacity, TextInput, RefreshControl, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { Card, Loading, StatusBadge, FloatingActionButton } from '../../src/components/ui';
import { orderService, Order } from '../../src/api/orderService';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { currencyUtils, dateUtils } from '../../src/utils';

export default function OrdersScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const router = useRouter();
  const isDark = theme === 'dark';

  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [statusFilter, setStatusFilter] = useState(null);

  const loadOrders = async (page = 1, refresh = false) => {
    try {
      if (refresh) {
        setIsLoading(true);
      }

      const response = await orderService.getOrders(page, statusFilter || undefined);

      const newOrders = response.data;

      if (page === 1) {
        setOrders(newOrders);
        setFilteredOrders(newOrders);
      } else {
        setOrders(prevOrders => [...prevOrders, ...newOrders]);
        setFilteredOrders(prevOrders => [...prevOrders, ...newOrders]);
      }

      setHasMorePages(response.next_page_url !== null);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadOrders();
  }, [statusFilter]);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredOrders(orders);
    } else {
      const filtered = orders.filter(order =>
        order.order_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (order.customer?.name && order.customer.name.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredOrders(filtered);
    }
  }, [searchQuery, orders]);

  const onRefresh = () => {
    setRefreshing(true);
    setSearchQuery('');
    loadOrders(1, true);
  };

  const loadMoreOrders = () => {
    if (hasMorePages && !isLoading && searchQuery.trim() === '') {
      loadOrders(currentPage + 1);
    }
  };

  const handleOrderPress = (orderId) => {
    router.push(`/orders/${orderId}`);
  };

  const handleStatusFilter = (status | null) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  const renderOrderItem = ({ item }: { item }) => (
     handleOrderPress(item.id)}>
      
        
          
            {item.order_number}
          
          
        

        
          
            
            
              {item.customer?.name || t('unknown_customer')}
            
          

          
            
            
              {dateUtils.formatDate(item.order_date)}
            
          

          
            
            
              {currencyUtils.formatCurrency(item.total_amount)}
            
          

          {item.delivery_date && (
            
              
              
                {t('delivery')}: {dateUtils.formatDate(item.delivery_date)}
              
            
          )}
        
      
    
  );

  const fabActions = [
    {
      icon: 'add-circle-outline',
      label('add_order'),
      onPress: () => router.push('/orders/new'),
    },
  ];

  const statusOptions = [
    { value, label('all') },
    { value: 'pending', label('pending') },
    { value: 'processing', label('processing') },
    { value: 'completed', label('completed') },
    { value: 'delivered', label('delivered') },
    { value: 'cancelled', label('cancelled') },
  ];

  return (
    
      
        
          {t('orders')}
        

        
          
          
          {searchQuery.length > 0 && (
             setSearchQuery('')}>
              
            
          )}
        

        
          {statusOptions.map((option) => (
             handleStatusFilter(option.value)}
            >
              
                {option.label}
              
            
          ))}
        
      

      {isLoading && !refreshing ? (
        
      ) : (
        <>
          {filteredOrders.length > 0 ? (
             item.id.toString()}
              contentContainerStyle={styles.listContent}
              onEndReached={loadMoreOrders}
              onEndReachedThreshold={0.5}
              refreshControl={
                
              }
              ListFooterComponent={
                isLoading && hasMorePages ?  : null
              }
            />
          ) : (
            
              
              
                {searchQuery.length > 0
                  ? t('no_orders_found')
                  : t('no_orders_yet')}
              
            
          )}
        
      )}

      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  header: {
    padding,
    paddingBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal,
    borderRadius,
    height,
    marginBottom,
  },
  searchInput: {
    flex,
    marginLeft,
    fontSize,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingBottom,
  },
  filterButton: {
    paddingHorizontal,
    paddingVertical,
    borderRadius,
    marginRight,
    borderWidth,
  },
  filterText: {
    fontSize,
    fontWeight: '500',
  },
  listContent: {
    padding,
    paddingTop,
    paddingBottom,
  },
  orderCard: {
    marginBottom,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom,
  },
  orderNumber: {
    fontSize,
    fontWeight: 'bold',
  },
  orderDetails: {
    gap,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap,
  },
  emptyContainer: {
    flex,
    justifyContent: 'center',
    alignItems: 'center',
    padding,
  },
  emptyText: {
    fontSize,
    textAlign: 'center',
    marginTop,
  },
});
