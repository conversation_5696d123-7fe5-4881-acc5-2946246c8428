import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, FlatList, TouchableOpacity, TextInput, RefreshControl, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { Card, Loading, FloatingActionButton } from '../../src/components/ui';
import { productService, Product } from '../../src/api/productService';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { currencyUtils } from '../../src/utils';

export default function ProductsScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const router = useRouter();
  const isDark = theme === 'dark';
  
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [categoryFilter, setCategoryFilter] = useState(null);
  const [categories, setCategories] = useState([]);

  const loadProducts = async (page = 1, refresh = false) => {
    try {
      if (refresh) {
        setIsLoading(true);
      }
      
      const response = await productService.getProducts(
        page,
        categoryFilter || undefined,
        searchQuery.trim() || undefined
      );
      
      const newProducts = response.data;
      
      if (page === 1) {
        setProducts(newProducts);
        setFilteredProducts(newProducts);
      } else {
        setProducts(prevProducts => [...prevProducts, ...newProducts]);
        setFilteredProducts(prevProducts => [...prevProducts, ...newProducts]);
      }
      
      setHasMorePages(response.next_page_url !== null);
      setCurrentPage(page);
      
      // Extract unique categories
      if (page === 1) {
        const uniqueCategories = Array.from(
          new Set(
            newProducts
              .map(product => product.category)
              .filter(category => category) // Filter out null/undefined
          )
        );
        setCategories(uniqueCategories []);
      }
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadProducts();
  }, [categoryFilter]);

  const onRefresh = () => {
    setRefreshing(true);
    setSearchQuery('');
    loadProducts(1, true);
  };

  const handleSearch = () => {
    setCurrentPage(1);
    loadProducts(1, true);
  };

  const loadMoreProducts = () => {
    if (hasMorePages && !isLoading) {
      loadProducts(currentPage + 1);
    }
  };

  const handleProductPress = (productId) => {
    router.push(`/products/${productId}`);
  };

  const handleCategoryFilter = (category | null) => {
    setCategoryFilter(category);
    setCurrentPage(1);
  };

  const renderProductItem = ({ item }: { item }) => (
     handleProductPress(item.id)}>
      
        
          
            {item.name}
          
          
            {currencyUtils.formatCurrency(item.price)}
          
        
        
        
          {item.category && (
            
              
              
                {item.category}
              
            
          )}
          
          
            
            
              {t('in_stock')}: {item.quantity}
            
          
          
          {item.description && (
            
              {item.description}
            
          )}
        
      
    
  );

  const fabActions = [
    {
      icon: 'shirt-outline',
      label('add_product'),
      onPress: () => router.push('/products/new'),
    },
  ];

  return (
    
      
        
          {t('products')}
        
        
        
          
          
          {searchQuery.length > 0 && (
             {
              setSearchQuery('');
              loadProducts(1, true);
            }}>
              
            
          )}
        
        
        
           handleCategoryFilter(null)}
          >
            
              {t('all')}
            
          
          
          {categories.map((category) => (
             handleCategoryFilter(category)}
            >
              
                {category}
              
            
          ))}
        
      
      
      {isLoading && !refreshing ? (
        
      ) : (
        <>
          {filteredProducts.length > 0 ? (
             item.id.toString()}
              contentContainerStyle={styles.listContent}
              onEndReached={loadMoreProducts}
              onEndReachedThreshold={0.5}
              refreshControl={
                
              }
              ListFooterComponent={
                isLoading && hasMorePages ?  : null
              }
            />
          ) : (
            
              
              
                {searchQuery.length > 0 || categoryFilter
                  ? t('no_products_found')
                  : t('no_products_yet')}
              
            
          )}
        
      )}
      
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  header: {
    padding,
    paddingBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal,
    borderRadius,
    height,
    marginBottom,
  },
  searchInput: {
    flex,
    marginLeft,
    fontSize,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingBottom,
  },
  filterButton: {
    paddingHorizontal,
    paddingVertical,
    borderRadius,
    marginRight,
    borderWidth,
  },
  filterText: {
    fontSize,
    fontWeight: '500',
  },
  listContent: {
    padding,
    paddingTop,
    paddingBottom,
  },
  productCard: {
    marginBottom,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom,
  },
  productName: {
    fontSize,
    fontWeight: 'bold',
    flex,
  },
  productPrice: {
    fontSize,
    fontWeight: 'bold',
  },
  productDetails: {
    gap,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap,
  },
  emptyContainer: {
    flex,
    justifyContent: 'center',
    alignItems: 'center',
    padding,
  },
  emptyText: {
    fontSize,
    textAlign: 'center',
    marginTop,
  },
});
