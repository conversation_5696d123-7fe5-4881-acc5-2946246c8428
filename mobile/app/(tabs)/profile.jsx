import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image,
  ScrollView,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { useAuth } from '../../src/context/AuthContext';

// This is a user-specific screen for viewing and editing profile
export default function ProfileScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const { user, updateUser } = useAuth();
  const isDark = theme === 'dark';
  
  const [loading, setLoading] = useState(false);
  
  // Placeholder for profile photo
  const profilePhoto = user?.profile_photo || 'https://via.placeholder.com/150';
  
  const handleEditProfile = () => {
    Alert.alert(t('feature_coming_soon'));
  };
  
  const handleChangePassword = () => {
    Alert.alert(t('feature_coming_soon'));
  };
  
  const handleNotificationSettings = () => {
    Alert.alert(t('feature_coming_soon'));
  };
  
  const handlePrivacySettings = () => {
    Alert.alert(t('feature_coming_soon'));
  };
  
  if (!user) {
    return (
      
        
      
    );
  }
  
  return (
    
      
        
          {t('my_profile')}
        
      
      
      
        
          
          
          
            
              {user.name}
            
            
              {user.email}
            
            
              
                {user.role.toUpperCase()}
              
            
          
        
        
        
          
          {t('edit_profile')}
        
      
      
      
        
          {t('account_settings')}
        
        
        
          
            
          
          
            
              {t('change_password')}
            
            
              {t('update_your_password')}
            
          
          
        
        
        
          
            
          
          
            
              {t('notifications')}
            
            
              {t('manage_notifications')}
            
          
          
        
        
        
          
            
          
          
            
              {t('privacy')}
            
            
              {t('manage_privacy_settings')}
            
          
          
        
      
      
      
        
          {t('app_info')}
        
        
        
          
            {t('version')}
          
          
            1.0.0
          
        
        
        
          
            {t('build')}
          
          
            2023.05.01
          
        
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  contentContainer: {
    padding,
  },
  header: {
    marginBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
  },
  profileCard: {
    borderRadius,
    padding,
    marginBottom,
    shadowColor: '#000',
    shadowOffset: { width, height },
    shadowOpacity.2,
    shadowRadius.41,
    elevation,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom,
  },
  profileImage: {
    width,
    height,
    borderRadius,
  },
  profileInfo: {
    marginLeft,
    flex,
  },
  profileName: {
    fontSize,
    fontWeight: 'bold',
  },
  profileEmail: {
    fontSize,
    marginTop,
  },
  roleBadge: {
    paddingHorizontal,
    paddingVertical,
    borderRadius,
    alignSelf: 'flex-start',
    marginTop,
  },
  roleText: {
    color: '#ffffff',
    fontSize,
    fontWeight: 'bold',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding,
    borderRadius,
  },
  editButtonText: {
    color: '#ffffff',
    marginLeft,
    fontWeight: 'bold',
  },
  sectionCard: {
    borderRadius,
    padding,
    marginBottom,
    shadowColor: '#000',
    shadowOffset: { width, height },
    shadowOpacity.2,
    shadowRadius.41,
    elevation,
  },
  sectionTitle: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical,
    borderBottomWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  settingIcon: {
    width,
    alignItems: 'center',
  },
  settingContent: {
    flex,
    marginLeft,
  },
  settingTitle: {
    fontSize,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize,
    marginTop,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical,
    borderBottomWidth,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  infoLabel: {
    fontSize,
  },
  infoValue: {
    fontSize,
    fontWeight: '500',
  },
});
