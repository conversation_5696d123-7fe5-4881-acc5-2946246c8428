import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Switch, Alert, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { useAuth } from '../../src/context/AuthContext';
import { Card, Button, Input, Loading } from '../../src/components/ui';
import { Ionicons } from '@expo/vector-icons';
import { changeLanguage } from '../../src/i18n';
import { validationUtils } from '../../src/utils';

export default function SettingsScreen() {
  const { t, i18n } = useTranslation();
  const { theme, toggleTheme } = useTheme();
  const { user, logout, updateUser } = useAuth();
  const isDark = theme === 'dark';
  
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Profile form state
  const [name, setName] = useState(user?.name || '');
  const [email, setEmail] = useState(user?.email || '');
  const [phone, setPhone] = useState(user?.phone || '');
  const [profileErrors, setProfileErrors] = useState({});
  
  // Password form state
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordErrors, setPasswordErrors] = useState({});

  const handleLanguageChange = async (lang) => {
    try {
      await changeLanguage(lang);
      if (user) {
        await updateUser({ language });
      }
    } catch (error) {
      console.error('Error changing language:', error);
      Alert.alert(t('error'), t('language_change_failed'));
    }
  };

  const validateProfileForm = () => {
    const errors: {
      name?: string;
      email?: string;
      phone?: string;
    } = {};
    
    if (!validationUtils.validateRequired(name)) {
      errors.name = t('name_required');
    }
    
    if (!validationUtils.validateRequired(email)) {
      errors.email = t('email_required');
    } else if (!validationUtils.validateEmail(email)) {
      errors.email = t('email_invalid');
    }
    
    if (phone && !validationUtils.validatePhone(phone)) {
      errors.phone = t('phone_invalid');
    }
    
    setProfileErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validatePasswordForm = () => {
    const errors: {
      currentPassword?: string;
      newPassword?: string;
      confirmPassword?: string;
    } = {};
    
    if (!validationUtils.validateRequired(currentPassword)) {
      errors.currentPassword = t('current_password_required');
    }
    
    if (!validationUtils.validateRequired(newPassword)) {
      errors.newPassword = t('new_password_required');
    } else if (!validationUtils.validateMinLength(newPassword, 8)) {
      errors.newPassword = t('password_min_length');
    }
    
    if (!validationUtils.validateRequired(confirmPassword)) {
      errors.confirmPassword = t('confirm_password_required');
    } else if (!validationUtils.validatePasswordMatch(newPassword, confirmPassword)) {
      errors.confirmPassword = t('passwords_not_match');
    }
    
    setPasswordErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleUpdateProfile = async () => {
    if (!validateProfileForm()) return;
    
    setIsLoading(true);
    try {
      await updateUser({
        name,
        email,
        phone,
      });
      
      setIsEditingProfile(false);
      Alert.alert(t('success'), t('profile_updated'));
    } catch (error) {
      console.error('Update profile error:', error);
      if (error.response?.data?.message) {
        Alert.alert(t('error'), error.response.data.message);
      } else {
        Alert.alert(t('error'), t('profile_update_failed'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleChangePassword = async () => {
    if (!validatePasswordForm()) return;
    
    setIsLoading(true);
    try {
      await updateUser({
        current_password,
        password,
        password_confirmation,
      });
      
      setIsChangingPassword(false);
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      Alert.alert(t('success'), t('password_updated'));
    } catch (error) {
      console.error('Change password error:', error);
      if (error.response?.data?.message) {
        Alert.alert(t('error'), error.response.data.message);
      } else {
        Alert.alert(t('error'), t('password_update_failed'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    Alert.alert(
      t('logout'),
      t('logout_confirm'),
      [
        {
          text('cancel'),
          style: 'cancel',
        },
        {
          text('logout'),
          onPress () => {
            setIsLoading(true);
            try {
              await logout();
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert(t('error'), t('logout_failed'));
            } finally {
              setIsLoading(false);
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  return (
    
      
        
          {t('settings')}
        
        
        
          
            
              
                {user?.name}
              
              
                {user?.email}
              
              {user?.phone && (
                
                  {user.phone}
                
              )}
            
            
             setIsEditingProfile(!isEditingProfile)}
            >
              
            
          
          
          {isEditingProfile && (
            
              
              
              
              
              
              
              
            
          )}
        
        
        
          
            {t('appearance')}
          
          
          
            
              {t('dark_mode')}
            
            
          
          
          
            {t('language')}
          
          
          
             handleLanguageChange('en')}
            >
              
                {t('english')}
              
            
            
             handleLanguageChange('bn')}
            >
              
                {t('bengali')}
              
            
          
        
        
        
          
            {t('security')}
          
          
           setIsChangingPassword(!isChangingPassword)}
          >
            
              {t('change_password')}
            
            
          
          
          {isChangingPassword && (
            
              
              
              
              
              
              
              
            
          )}
        
        
        
      
      
      {isLoading && }
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  scrollContent: {
    padding,
    paddingBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  profileCard: {
    marginBottom,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  profileInfo: {
    flex,
  },
  profileName: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  editButton: {
    width,
    height,
    borderRadius,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    marginTop,
  },
  button: {
    marginTop,
  },
  settingsCard: {
    marginBottom,
  },
  sectionTitle: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom,
  },
  languageContainer: {
    flexDirection: 'row',
    marginBottom,
  },
  languageButton: {
    paddingHorizontal,
    paddingVertical,
    borderRadius,
    marginRight,
    borderWidth,
  },
  languageText: {
    fontSize,
    fontWeight: '500',
  },
  securityCard: {
    marginBottom,
  },
  securityButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical,
  },
  logoutButton: {
    marginBottom,
  },
});
