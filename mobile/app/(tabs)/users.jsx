import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../src/context/ThemeContext';
import { useAuth } from '../../src/context/AuthContext';
import axios from 'axios';
import apiClient from '../../src/api/apiClient';

// This is an admin-only screen for managing users
export default function UsersScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const router = useRouter();
  const { hasPermission, isAdmin } = useAuth();
  const isDark = theme === 'dark';
  
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Check if user h to access this screen
  useEffect(() => {
    if (!hasPermission('users.manage')) {
      Alert.alert(
        t('access_denied'),
        t('no_permission_users'),
        [{ text('ok'), onPress: () => router.back() }]
      );
    }
  }, [hasPermission, router, t]);
  
  // Fetch users data
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // This would be a real API call in a production app
        // For now, we'll use mock data
        // const response = await apiClient.get('/admin/users');
        // setUsers(response.data);
        
        // Mock data
        setTimeout(() => {
          setUsers([
            { id, name: 'Admin User', email: '<EMAIL>', role: 'admin', is_active },
            { id, name: 'Regular User', email: '<EMAIL>', role: 'user', is_active },
            { id, name: 'Test User', email: '<EMAIL>', role: 'user', is_active },
          ]);
          setLoading(false);
        }, 1000);
      } catch (err) {
        console.error('Error fetching users:', err);
        setError(t('error_loading_users'));
        setLoading(false);
      }
    };
    
    fetchUsers();
  }, [t]);
  
  const handleUserPress = (user) => {
    // Navigate to user details/edit screen
    // router.push(`/users/${user.id}`);
    Alert.alert(
      t('user_details'),
      `${user.name} (${user.email})\n${t('role')}: ${user.role}\n${t('status')}: ${user.is_active ? t('active') : t('inactive')}`
    );
  };
  
  const renderUserItem = ({ item }) => (
     handleUserPress(item)}
    >
      
        
          {item.name}
        
        
          {item.email}
        
      
      
      
        
          
            {item.role.toUpperCase()}
          
        
        
        
          
          
            {item.is_active ? t('active') : t('inactive')}
          
        
      
      
      
    
  );
  
  if (!isAdmin) {
    return (
      
        
          {t('admin_only')}
        
      
    );
  }
  
  return (
    
      
        
          {t('users_management')}
        
        
         Alert.alert(t('feature_coming_soon'))}
        >
          
        
      
      
      {loading ? (
        
          
          
            {t('loading_users')}
          
        
      ) : error ? (
        
          
          
            {error}
          
        
      ) : (
         item.id.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
    padding,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
  },
  addButton: {
    width,
    height,
    borderRadius,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop,
    fontSize,
  },
  errorContainer: {
    flex,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    marginTop,
    fontSize,
    textAlign: 'center',
  },
  listContent: {
    paddingBottom,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding,
    borderRadius,
    marginBottom,
    shadowColor: '#000',
    shadowOffset: { width, height },
    shadowOpacity.2,
    shadowRadius.41,
    elevation,
  },
  userInfo: {
    flex,
  },
  userName: {
    fontSize,
    fontWeight: 'bold',
  },
  userEmail: {
    fontSize,
    marginTop,
  },
  userMeta: {
    marginRight,
    alignItems: 'flex-end',
  },
  roleBadge: {
    paddingHorizontal,
    paddingVertical,
    borderRadius,
    marginBottom,
  },
  roleText: {
    color: '#ffffff',
    fontSize,
    fontWeight: 'bold',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width,
    height,
    borderRadius,
    marginRight,
  },
  statusText: {
    fontSize,
  },
});
