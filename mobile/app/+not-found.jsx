import { Link, Stack } from 'expo-router';
import { StyleSheet } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

export default function NotFoundScreen() {
  return (
    <>
      
      
        This screen does not exist.
        
          Go to home screen!
        
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
    alignItems: 'center',
    justifyContent: 'center',
    padding,
  },
  link: {
    marginTop,
    paddingVertical,
  },
});
