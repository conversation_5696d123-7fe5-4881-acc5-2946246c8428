import { DarkTheme, DefaultTheme, ThemeProvider  } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import { useEffect } from 'react';

import { AuthProvider } from '../src/context/AuthContext';
import { ThemeProvider, useTheme } from '../src/context/ThemeContext';
import { i18n, loadSavedLanguage } from '../src/i18n';

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    // Load saved language
    loadSavedLanguage();
  }, []);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    
      
        
      
    
  );
}

function RootLayoutNav() {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    
      
        
        
        
        
      
      
    
  );
}
