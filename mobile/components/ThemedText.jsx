import { StyleSheet, Text, type TextProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light, dark }, 'text');

  return (
    
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize,
    lineHeight,
  },
  defaultSemiBold: {
    fontSize,
    lineHeight,
    fontWeight: '600',
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    lineHeight,
  },
  subtitle: {
    fontSize,
    fontWeight: 'bold',
  },
  link: {
    lineHeight,
    fontSize,
    color: '#0a7ea4',
  },
});
