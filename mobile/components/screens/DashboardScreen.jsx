import React from 'react';
import { StyleSheet, View, ScrollView, SafeAreaView } from 'react-native';
import { Typography } from '../ui/Typography';
import { StatCard } from '../ui/StatCard';
import { ActionButton } from '../ui/ActionButton';
import { ListItem } from '../ui/ListItem';
import { InventoryBar } from '../ui/InventoryBar';
import { Card } from '../ui/Card';
import { FloatingActionButton } from '../ui/FloatingActionButton';
import { useThemeColor } from '@/hooks/useThemeColor';

// Import icons (you'll need to replace these with actual icons)
import { Ionicons } from '@expo/vector-icons';

export type DashboardScreenProps = {
  navigation?: any; // Replace with proper navigation type
};

export function DashboardScreen({ navigation }: DashboardScreenProps) {
  const backgroundColor = useThemeColor({}, 'background');
  const accentColor = useThemeColor({}, 'accent');
  
  return (
    
      
        
          Dashboard
        
        
        
          Today
        
        
        
          
          
          
        
        
        
          Quick Actions
        
        
        
          }
            style={styles.actionButton}
            onPress={() => {
              // Navigate to new order screen
            }}
          />
          
          }
            style={styles.actionButton}
            onPress={() => {
              // Open scanner
            }}
          />
          
          }
            style={styles.actionButton}
            onPress={() => {
              // Navigate to add customer screen
            }}
          />
        
        
        
          Recent Orders
        
        
        
          
                Due in 2 days
              
            }
            onPress={() => {
              // Navigate to order details
            }}
          />
          
          
                Due in 5 days
              
            }
            divider={false}
            onPress={() => {
              // Navigate to order details
            }}
          />
        
        
        
          Inventory Summary
        
        
        
          
            100
          
          
          
            Total Items
          
          
          
            
            
            
          
        
      
      
      }
        onPress={() => {
          // Show action menu
        }}
      />
      
      {/* Bottom Navigation */}
      
        
          
          
            Dashboard
          
        
        
        
          
          
            Orders
          
        
        
        
          
          
            Customers
          
        
        
        
          
          
            Settings
          
        
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  scrollView: {
    flex,
  },
  scrollContent: {
    padding,
    paddingBottom, // Space for bottom navigation
  },
  sectionTitle: {
    marginBottom,
  },
  sectionSubtitle: {
    marginBottom,
  },
  statsContainer: {
    flexDirection: 'row',
    marginBottom,
  },
  statCard: {
    flex,
    marginRight,
  },
  actionsContainer: {
    marginBottom,
  },
  actionButton: {
    marginBottom,
  },
  ordersCard: {
    marginBottom,
  },
  inventoryCard: {
    marginBottom,
  },
  inventoryTitle: {
    marginBottom,
  },
  inventorySubtitle: {
    marginBottom,
  },
  inventoryBars: {
    marginTop,
  },
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    position: 'absolute',
    bottom,
    left,
    right,
    height,
    backgroundColor: '#0E3B31',
    borderTopWidth,
    borderTopColor: '#2C5A4E',
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
