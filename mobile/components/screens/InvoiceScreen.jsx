import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, SafeAreaView } from 'react-native';
import { Typography } from '../ui/Typography';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { ListItem } from '../ui/ListItem';
import { FloatingActionButton } from '../ui/FloatingActionButton';
import { Header } from '../ui/Header';
import { useThemeColor } from '@/hooks/useThemeColor';

// Import icons (you'll need to replace these with actual icons)
import { Ionicons } from '@expo/vector-icons';

export type InvoiceScreenProps = {
  navigation?: any; // Replace with proper navigation type
};

export type InvoiceItem = {
  id;
  name;
  type;
  price;
};

export function InvoiceScreen({ navigation }: InvoiceScreenProps) {
  const backgroundColor = useThemeColor({}, 'background');
  const accentColor = useThemeColor({}, 'accent');
  
  const [invoiceItems, setInvoiceItems] = useState([
    { id: '1', name: 'Trouser Hem', type: 'Alteration', price },
    { id: '2', name: 'Jacket Sleeves', type: 'Alteration', price },
    { id: '3', name: 'Shirt Collar', type: 'Alteration', price },
  ]);
  
  // Calculate totals
  const subtotal = invoiceItems.reduce((sum, item) => sum + item.price, 0);
  const discount = 0;
  const total = subtotal - discount;
  
  return (
    
      }
        onLeftIconPress={() => {
          // Navigate back
          navigation?.goBack();
        }}
      />
      
      
        
          
          
          
          
          }
          />
          
          }
          />
        
        
        
          Items
        
        
        
          {invoiceItems.map((item) => (
            
                  ${item.price}
                
              }
            />
          ))}
        
        
        
          Summary
        
        
        
          
            Subtotal
            ${subtotal}
          
          
          
            Discount
            ${discount}
          
          
          
            Total
            ${total}
          
        
      
      
      }
        onPress={() => {
          // Add new item
        }}
      />
      
      
         {
            // Create invoice and navigate back
          }}
        />
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    flex,
  },
  scrollView: {
    flex,
  },
  scrollContent: {
    padding,
    paddingBottom, // Space for footer
  },
  formSection: {
    marginBottom,
  },
  sectionTitle: {
    marginBottom,
  },
  itemsSection: {
    marginBottom,
  },
  summarySection: {
    marginBottom,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom,
  },
  totalRow: {
    marginTop,
    paddingTop,
    borderTopWidth,
    borderTopColor: '#2C5A4E',
  },
  footer: {
    position: 'absolute',
    bottom,
    left,
    right,
    padding,
    backgroundColor: '#0E3B31',
    borderTopWidth,
    borderTopColor: '#2C5A4E',
  },
});
