import React from 'react';
import { StyleSheet, TouchableOpacity, TouchableOpacityProps, View } from 'react-native';
import { Typography } from './Typography';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ActionButtonProps = TouchableOpacityProps & {
  label;
  icon.ReactNode;
  variant?: 'filled' | 'outlined';
};

export function ActionButton({
  label,
  icon,
  variant = 'filled',
  style,
  ...rest
}: ActionButtonProps) {
  const cardColor = useThemeColor({}, 'card');
  const borderColor = useThemeColor({}, 'border');

  const buttonStyle = variant === 'outlined'
    ? {
        backgroundColor: 'transparent',
        borderWidth,
        borderColor,
      }
    : {
        backgroundColor,
      };

  return (
    
      {icon}
      
        {label}
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius,
    padding,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight,
  },
  iconContainer: {
    marginRight,
  },
  label: {
    flex,
  },
});
