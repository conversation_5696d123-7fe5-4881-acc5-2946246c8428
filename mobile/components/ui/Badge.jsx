import React from 'react';
import { StyleSheet, View, ViewProps, Text } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export type BadgeProps = ViewProps & {
  label;
  variant?: 'filled' | 'outlined';
  color?: 'primary' | 'accent' | 'error' | 'warning' | 'success' | 'default';
  size?: 'small' | 'medium' | 'large';
};

export function Badge({
  label,
  variant = 'filled',
  color = 'default',
  size = 'medium',
  style,
  ...rest
}: BadgeProps) {
  // Get theme colors
  const textColor = useThemeColor({}, 'text');
  const accentColor = useThemeColor({}, 'accent');
  const primaryColor = useThemeColor({}, 'primary');
  const errorColor = useThemeColor({}, 'error');
  const warningColor = useThemeColor({}, 'warning');
  const successColor = useThemeColor({}, 'success');
  const backgroundColor = useThemeColor({}, 'background');

  // Determine badge color
  let badgeColor;
  switch (color) {
    case 'primary' = primaryColor;
      break;
    case 'accent' = accentColor;
      break;
    case 'error' = errorColor;
      break;
    case 'warning' = warningColor;
      break;
    case 'success' = successColor;
      break;
    default = '#555555';
  }

  // Determine badge styling based on variant
  let badgeStyle;
  let textStyle;
  
  if (variant === 'outlined') {
    badgeStyle = {
      backgroundColor: 'transparent',
      borderWidth,
      borderColor,
    };
    textStyle = { color };
  } else {
    badgeStyle = {
      backgroundColor,
    };
    textStyle = { color };
  }

  // Determine size
  let sizeStyle;
  let fontSize;
  
  switch (size) {
    case 'small' = {
        paddingVertical,
        paddingHorizontal,
      };
      fontSize = 10;
      break;
    case 'large' = {
        paddingVertical,
        paddingHorizontal,
      };
      fontSize = 14;
      break;
    default: // medium
      sizeStyle = {
        paddingVertical,
        paddingHorizontal,
      };
      fontSize = 12;
  }

  return (
    
      
        {label}
      
    
  );
}

const styles = StyleSheet.create({
  badge: {
    borderRadius,
    alignSelf: 'flex-start',
  },
  text: {
    fontWeight: '500',
  },
});
