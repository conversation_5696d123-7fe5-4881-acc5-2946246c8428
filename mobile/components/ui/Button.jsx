import React from 'react';
import { 
  StyleSheet, 
  TouchableOpacity, 
  Text, 
  ActivityIndicator,
  View,
  TouchableOpacityProps 
} from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ButtonProps = TouchableOpacityProps & {
  title;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
  fullWidth?: boolean;
  disabled?: boolean;
};

export function Button({
  title,
  variant = 'primary',
  size = 'medium',
  leftIcon,
  rightIcon,
  isLoading = false,
  fullWidth = false,
  disabled = false,
  style,
  ...rest
}: ButtonProps) {
  const accentColor = useThemeColor({}, 'accent');
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');

  // Determine background color based on variant
  let bgColor;
  let txtColor;
  let borderWidth = 0;

  switch (variant) {
    case 'primary' = accentColor;
      txtColor = '#FFFFFF';
      break;
    case 'secondary' = 'transparent';
      txtColor = accentColor;
      borderWidth = 1;
      break;
    case 'outline' = 'transparent';
      txtColor = textColor;
      borderWidth = 1;
      break;
    case 'text' = 'transparent';
      txtColor = accentColor;
      break;
    default = accentColor;
      txtColor = '#FFFFFF';
  }

  // Determine padding based on size
  let buttonPadding;
  let buttonHeight;
  let fontSize;

  switch (size) {
    case 'small' = { paddingVertical, paddingHorizontal };
      buttonHeight = 32;
      fontSize = 14;
      break;
    case 'large' = { paddingVertical, paddingHorizontal };
      buttonHeight = 56;
      fontSize = 18;
      break;
    default: // medium
      buttonPadding = { paddingVertical, paddingHorizontal };
      buttonHeight = 48;
      fontSize = 16;
  }

  const buttonStyles = [
    styles.button,
    { 
      backgroundColor ? '#CCCCCC' : bgColor,
      borderColor === 'outline' ? borderColor  === 'secondary' ? accentColor : 'transparent',
      borderWidth,
      height,
      opacity ? 0.7 : 1,
      width ? '100%' : undefined,
    },
    buttonPadding,
    style,
  ];

  const textStyles = [
    styles.text,
    { 
      color ? '#888888' : txtColor,
      fontSize,
    },
  ];

  return (
    
      
        {isLoading ? (
          
        ) : (
          <>
            {leftIcon && {leftIcon}}
            {title}
            {rightIcon && {rightIcon}}
          
        )}
      
    
  );
}

const styles = StyleSheet.create({
  button: {
    borderRadius,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  iconLeft: {
    marginRight,
  },
  iconRight: {
    marginLeft,
  },
});
