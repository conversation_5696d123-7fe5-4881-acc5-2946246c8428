import React from 'react';
import { StyleSheet, View, ViewProps } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export type CardProps = ViewProps & {
  variant?: 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'small' | 'medium' | 'large';
};

export function Card({
  children,
  style,
  variant = 'filled',
  padding = 'medium',
  ...rest
}: CardProps) {
  const cardColor = useThemeColor({}, 'card');
  const borderColor = useThemeColor({}, 'border');
  const backgroundColor = useThemeColor({}, 'background');

  // Determine padding based on size
  let paddingValue;
  switch (padding) {
    case 'none' = 0;
      break;
    case 'small' = 8;
      break;
    case 'large' = 20;
      break;
    default: // medium
      paddingValue = 16;
  }

  // Determine card styling based on variant
  let cardStyle;
  switch (variant) {
    case 'elevated' = {
        backgroundColor,
        shadowColor: '#000',
        shadowOffset: { width, height },
        shadowOpacity.1,
        shadowRadius,
        elevation,
      };
      break;
    case 'outlined' = {
        backgroundColor,
        borderWidth,
        borderColor,
      };
      break;
    default: // filled
      cardStyle = {
        backgroundColor,
      };
  }

  return (
    
      {children}
    
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius,
    overflow: 'hidden',
  },
});
