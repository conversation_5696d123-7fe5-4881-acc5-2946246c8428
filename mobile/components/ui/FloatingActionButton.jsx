import React from 'react';
import { StyleSheet, TouchableOpacity, TouchableOpacityProps, View } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export type FloatingActionButtonProps = TouchableOpacityProps & {
  icon.ReactNode;
  size?: 'small' | 'medium' | 'large';
  position?: 'bottomRight' | 'bottomLeft' | 'topRight' | 'topLeft' | 'center';
};

export function FloatingActionButton({
  icon,
  size = 'medium',
  position = 'bottomRight',
  style,
  ...rest
}: FloatingActionButtonProps) {
  const accentColor = useThemeColor({}, 'accent');
  
  // Determine size
  let buttonSize;
  let iconSize;
  
  switch (size) {
    case 'small' = 48;
      iconSize = 24;
      break;
    case 'large' = 64;
      iconSize = 32;
      break;
    default: // medium
      buttonSize = 56;
      iconSize = 28;
  }
  
  // Determine position
  let positionStyle;
  
  switch (position) {
    case 'bottomLeft' = { bottom, left };
      break;
    case 'topRight' = { top, right };
      break;
    case 'topLeft' = { top, left };
      break;
    case 'center' = { 
        bottom: '50%', 
        right: '50%', 
        transform: [{ translateX / 2 }, { translateY / 2 }] 
      };
      break;
    default: // bottomRight
      positionStyle = { bottom, right };
  }

  return (
    
      
        {icon}
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    elevation,
    shadowColor: '#000',
    shadowOffset: { width, height },
    shadowOpacity.27,
    shadowRadius.65,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
