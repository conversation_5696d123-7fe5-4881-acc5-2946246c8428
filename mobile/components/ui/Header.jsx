import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Typography } from './Typography';
import { useThemeColor } from '@/hooks/useThemeColor';

export type HeaderProps = {
  title;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onLeftIconPress?: () => void;
  onRightIconPress?: () => void;
  subtitle?: string;
};

export function Header({
  title,
  leftIcon,
  rightIcon,
  onLeftIconPress,
  onRightIconPress,
  subtitle,
}: HeaderProps) {
  const backgroundColor = useThemeColor({}, 'background');
  
  return (
    
      
        {leftIcon && (
          
            {leftIcon}
          
        )}
        
        
          
            {title}
          
          
          {subtitle && (
            
              {subtitle}
            
          )}
        
        
        {rightIcon && (
          
            {rightIcon}
          
        )}
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    paddingTop, // Account for status bar
    paddingBottom,
    paddingHorizontal,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftIcon: {
    marginRight,
  },
  titleContainer: {
    flex,
  },
  subtitle: {
    marginTop,
  },
  rightIcon: {
    marginLeft,
  },
});
