import React, { useState } from 'react';
import { 
  StyleSheet, 
  TextInput, 
  View, 
  Text, 
  TextInputProps,
  TouchableOpacity,
} from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export type InputProps = TextInputProps & {
  label?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  variant?: 'outlined' | 'filled' | 'underlined';
};

export function Input({
  label,
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'filled',
  style,
  ...rest
}: InputProps) {
  const [isFocused, setIsFocused] = useState(false);
  
  const textColor = useThemeColor({}, 'text');
  const secondaryTextColor = useThemeColor({}, 'secondaryText');
  const backgroundColor = useThemeColor({}, 'background');
  const cardColor = useThemeColor({}, 'card');
  const borderColor = useThemeColor({}, 'border');
  const errorColor = useThemeColor({}, 'error');
  const accentColor = useThemeColor({}, 'accent');

  // Determine input styling based on variant and state
  let containerStyle;
  let inputStyle;
  
  switch (variant) {
    case 'outlined' = {
        backgroundColor: 'transparent',
        borderWidth,
        borderColor ? errorColor : isFocused ? accentColor : borderColor,
      };
      inputStyle = {
        color,
      };
      break;
    case 'underlined' = {
        backgroundColor: 'transparent',
        borderBottomWidth,
        borderRadius,
        borderColor ? errorColor : isFocused ? accentColor : borderColor,
      };
      inputStyle = {
        color,
      };
      break;
    default: // filled
      containerStyle = {
        backgroundColor,
        borderWidth || isFocused ? 1 : 0,
        borderColor ? errorColor : isFocused ? accentColor : 'transparent',
      };
      inputStyle = {
        color,
      };
  }

  return (
    
      {label && (
        
          {label}
        
      )}
      
      
        {leftIcon && {leftIcon}}
        
         setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...rest}
        />
        
        {rightIcon && (
          
            {rightIcon}
          
        )}
      
      
      {error && (
        
          {error}
        
      )}
    
  );
}

const styles = StyleSheet.create({
  wrapper: {
    marginBottom,
  },
  container: {
    borderRadius,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight,
  },
  input: {
    flex,
    height,
    paddingHorizontal,
    fontSize,
  },
  inputWithLeftIcon: {
    paddingLeft,
  },
  inputWithRightIcon: {
    paddingRight,
  },
  label: {
    marginBottom,
    fontSize,
    fontWeight: '500',
  },
  error: {
    marginTop,
    fontSize,
  },
  leftIcon: {
    paddingLeft,
  },
  rightIcon: {
    paddingRight,
  },
});
