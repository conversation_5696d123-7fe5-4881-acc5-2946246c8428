import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Typography } from './Typography';
import { ProgressBar } from './ProgressBar';

export type InventoryBarProps = {
  label;
  value;
  maxValue?: number;
  color?: 'primary' | 'accent' | 'error' | 'warning' | 'success';
};

export function InventoryBar({
  label,
  value,
  maxValue = 100,
  color = 'accent',
}: InventoryBarProps) {
  // Calculate progress  value between 0 and 1
  const progress = Math.min(value / maxValue, 1);
  
  // Determine color based on inventory level
  let barColor = color;
  if (color === 'default') {
    if (progress 
      
        
          {label}
        
      
      
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom,
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom,
  },
  progressBar: {
    borderRadius,
  },
});
