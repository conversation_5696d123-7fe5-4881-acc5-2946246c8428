import React from 'react';
import { StyleSheet, TouchableOpacity, View, ViewProps } from 'react-native';
import { Typography } from './Typography';
import { Badge } from './Badge';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ListItemProps = ViewProps & {
  title;
  subtitle?: string;
  rightContent?: React.ReactNode;
  leftIcon?: React.ReactNode;
  badge?: {
    label;
    color?: 'primary' | 'accent' | 'error' | 'warning' | 'success' | 'default';
  };
  onPress?: () => void;
  divider?: boolean;
};

export function ListItem({
  title,
  subtitle,
  rightContent,
  leftIcon,
  badge,
  onPress,
  divider = true,
  style,
  ...rest
}: ListItemProps) {
  const borderColor = useThemeColor({}, 'border');
  
  const Container = onPress ? TouchableOpacity : View;
  
  return (
    
      {leftIcon && {leftIcon}}
      
      
        
          
            {title}
          
          
          {badge && (
            
          )}
        
        
        {subtitle && (
          
            {subtitle}
          
        )}
      
      
      {rightContent && (
        
          {rightContent}
        
      )}
    
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical,
    paddingHorizontal,
  },
  leftIcon: {
    marginRight,
  },
  contentContainer: {
    flex,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom,
  },
  title: {
    flex,
    marginRight,
  },
  badge: {
    marginLeft,
  },
  rightContent: {
    marginLeft,
  },
});
