import React from 'react';
import { 
  StyleSheet, 
  Modal , 
  View, 
  TouchableOpacity, 
  TouchableWithoutFeedback,
  Dimensions,
  ScrollView,
} from 'react-native';
import { Typography } from './Typography';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ModalProps = {
  visible;
  onClose: () => void;
  title?: string;
  children.ReactNode;
  closeIcon?: React.ReactNode;
  height?: number | string;
  fullScreen?: boolean;
  disableBackdropPress?: boolean;
};

const { height } = Dimensions.get('window');

export function Modal({
  visible,
  onClose,
  title,
  children,
  closeIcon,
  height,
  fullScreen = false,
  disableBackdropPress = false,
}: ModalProps) {
  const backgroundColor = useThemeColor({}, 'background');
  
  const modalHeight = fullScreen 
    ? '100%' 
    : height || '70%';
  
  return (
    
      
        
          
        
        
        
          
            {title && (
              
                {title}
              
            )}
            
            
              {closeIcon || (
                
                  ×
                
              )}
            
          
          
          
            {children}
          
        
      
    
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex,
    justifyContent: 'flex-end',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    borderTopLeftRadius,
    borderTopRightRadius,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal,
    paddingTop,
    paddingBottom,
  },
  closeButton: {
    padding,
  },
  content: {
    flex,
  },
  contentContainer: {
    padding,
  },
});
