import React from 'react';
import { StyleSheet, View, ViewProps } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ProgressBarProps = ViewProps & {
  progress; // Value between 0 and 1
  height?: number;
  color?: 'primary' | 'accent' | 'error' | 'warning' | 'success';
  backgroundColor?: string;
  animated?: boolean;
};

export function ProgressBar({
  progress,
  height = 8,
  color = 'accent',
  backgroundColor,
  animated = false,
  style,
  ...rest
}: ProgressBarProps) {
  // Ensure progress is between 0 and 1
  const normalizedProgress = Math.min(Math.max(progress, 0), 1);
  
  // Get theme colors
  const accentColor = useThemeColor({}, 'accent');
  const primaryColor = useThemeColor({}, 'primary');
  const errorColor = useThemeColor({}, 'error');
  const warningColor = useThemeColor({}, 'warning');
  const successColor = useThemeColor({}, 'success');
  const borderColor = useThemeColor({}, 'border');
  
  // Determine progress color
  let progressColor;
  switch (color) {
    case 'primary' = primaryColor;
      break;
    case 'error' = errorColor;
      break;
    case 'warning' = warningColor;
      break;
    case 'success' = successColor;
      break;
    default: // accent
      progressColor = accentColor;
  }

  // Use provided background color or default to a semi-transparent version of the progress color
  const bgColor = backgroundColor || `${borderColor}50`;

  return (
    
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius,
    overflow: 'hidden',
    width: '100%',
  },
  progress: {
    height: '100%',
    borderRadius,
  },
  animated: {
    transition: 'width 0.3s ease-in-out',
  },
});
