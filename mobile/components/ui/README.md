# Tailor Management App UI Component System

This directory contains a comprehensive set of reusable UI components for the Tailor Management App. The components are designed to be consistent, flexible, and easy to use throughout the application.

## Component Overview

### Base Components

- **Typography**: Text components with various styles (headings, body text, captions, etc.)
- **Button**: Customizable button with different variants, sizes, and states
- **Card**: Container component with different styles (elevated, outlined, filled)
- **Input**: Form input fields with various states and configurations
- **Badge**: Small labels for status indicators
- **ProgressBar**: Visual indicator for progress or data visualization

### Composite Components

- **StatCard**: Card displaying a metric with title and value
- **ActionButton**: Button with icon and label for common actions
- **ListItem**: Row component for displaying items in a list
- **InventoryBar**: Bar showing inventory levels with label
- **FloatingActionButton**: Floating button for primary actions
- **Header**: Screen header with title and optional icons
- **Modal**: Popup dialog for forms and information

## Usage

Import components from the UI directory:

```tsx
import { Button, Card, Typography } from '@/components/ui';
```

### Example: Creating a Card with Content

```tsx
<Card padding="medium" variant="filled">
  <Typography variant="h3" weight="bold">
    Card Title
  </Typography>
  
  <Typography variant="body1" color="secondary">
    Card content goes here
  </Typography>
  
  <Button 
    title="Action" 
    variant="primary" 
    size="medium" 
    style={{ marginTop: 16 }}
  />
</Card>
```

### Example: Creating a List

```tsx
<Card padding="none">
  <ListItem
    title="Item Title"
    subtitle="Item description"
    leftIcon={<Ionicons name="document" size={24} color="#A0B0AD" />}
    rightContent={
      <Badge label="Active" color="success" />
    }
    onPress={() => {
      // Handle press
    }}
  />
  
  <ListItem
    title="Another Item"
    subtitle="Another description"
    leftIcon={<Ionicons name="document" size={24} color="#A0B0AD" />}
    rightContent={
      <Badge label="Pending" color="warning" />
    }
    divider={false}
    onPress={() => {
      // Handle press
    }}
  />
</Card>
```

## Theme Colors

The components use the application's theme system. The main colors are:

- **Primary**: Dark green (`#0E3B31`) - Main background color
- **Card**: Lighter green (`#1A5A4A`) - Card and container backgrounds
- **Accent**: Bright green (`#00A884`) - Buttons and highlights
- **Text**: White (`#FFFFFF`) - Primary text color
- **Secondary Text**: Light gray-green (`#A0B0AD`) - Secondary text

Additional status colors:
- **Success**: Green (`#4CAF50`)
- **Warning**: Yellow (`#FFC107`)
- **Error**: Red (`#F44336`)

## Screen Templates

The `components/screens` directory contains screen templates that demonstrate how to use these components together:

- **DashboardScreen**: Main dashboard with statistics, actions, and lists
- **InvoiceScreen**: Form for creating and managing invoices

These templates can be used as a starting point for building new screens in the application.
