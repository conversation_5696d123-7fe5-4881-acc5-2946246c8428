import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Card } from './Card';
import { Typography } from './Typography';

export type StatCardProps = {
  title;
  value | number;
  icon?: React.ReactNode;
  prefix?: string;
  suffix?: string;
  onPress?: () => void;
};

export function StatCard({
  title,
  value,
  icon,
  prefix,
  suffix,
  onPress,
}: StatCardProps) {
  return (
    
      
        {title}
      
      
      
        {icon && {icon}}
        
        
          
            {prefix}{value}{suffix}
          
        
      
    
  );
}

const styles = StyleSheet.create({
  container: {
    minWidth,
    flex,
  },
  title: {
    marginBottom,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight,
  },
  valueTextContainer: {
    flex,
  },
});
