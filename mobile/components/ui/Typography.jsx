import React from 'react';
import { StyleSheet, Text, TextProps } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export type TypographyProps = TextProps & {
  variant?: 
    | 'h1' 
    | 'h2' 
    | 'h3' 
    | 'subtitle1' 
    | 'subtitle2' 
    | 'body1' 
    | 'body2' 
    | 'caption' 
    | 'button' 
    | 'overline';
  color?: 'primary' | 'secondary' | 'accent' | 'error' | 'warning' | 'success';
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
  weight?: 'normal' | 'bold' | 'semibold' | 'light';
};

export function Typography({
  children,
  style,
  variant = 'body1',
  color = 'primary',
  align = 'left',
  weight = 'normal',
  ...rest
}: TypographyProps) {
  // Get theme colors
  const textColor = useThemeColor({}, 'text');
  const secondaryTextColor = useThemeColor({}, 'secondaryText');
  const accentColor = useThemeColor({}, 'accent');
  const errorColor = useThemeColor({}, 'error');
  const warningColor = useThemeColor({}, 'warning');
  const successColor = useThemeColor({}, 'success');

  // Determine text color based on color prop
  let textColorValue;
  switch (color) {
    case 'secondary' = secondaryTextColor;
      break;
    case 'accent' = accentColor;
      break;
    case 'error' = errorColor;
      break;
    case 'warning' = warningColor;
      break;
    case 'success' = successColor;
      break;
    default: // primary
      textColorValue = textColor;
  }

  // Determine font weight
  let fontWeightValue;
  switch (weight) {
    case 'bold' = '700';
      break;
    case 'semibold' = '600';
      break;
    case 'light' = '300';
      break;
    default: // normal
      fontWeightValue = '400';
  }

  return (
    
      {children}
    
  );
}

const styles = StyleSheet.create({
  h1: {
    fontSize,
    lineHeight,
  },
  h2: {
    fontSize,
    lineHeight,
  },
  h3: {
    fontSize,
    lineHeight,
  },
  subtitle1: {
    fontSize,
    lineHeight,
  },
  subtitle2: {
    fontSize,
    lineHeight,
  },
  body1: {
    fontSize,
    lineHeight,
  },
  body2: {
    fontSize,
    lineHeight,
  },
  caption: {
    fontSize,
    lineHeight,
  },
  button: {
    fontSize,
    lineHeight,
    textTransform: 'uppercase',
  },
  overline: {
    fontSize,
    lineHeight,
    textTransform: 'uppercase',
    letterSpacing.5,
  },
});
