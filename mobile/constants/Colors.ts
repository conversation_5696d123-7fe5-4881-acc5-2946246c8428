/**
 * Color palette for the Tailor Management App.
 * The app primarily uses a dark green theme with accent colors.
 */

// Primary colors
const primaryColor = '#0E3B31'; // Dark green background
const primaryLightColor = '#1A5A4A'; // Lighter green for cards
const accentColor = '#00A884'; // Green accent for buttons and highlights
const textColor = '#FFFFFF'; // White text
const secondaryTextColor = '#A0B0AD'; // Lighter text for secondary information

// Status colors
const successColor = '#4CAF50'; // Green for success states
const warningColor = '#FFC107'; // Yellow for warnings
const errorColor = '#F44336'; // Red for errors

export const Colors = {
  light: {
    // We'll keep light theme for flexibility, but the app primarily uses dark theme
    text: '#11181C',
    secondaryText: '#687076',
    background: '#FFFFFF',
    card: '#F5F5F5',
    primary: '#1A5A4A',
    accent: '#00A884',
    tint: accentColor,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: accentColor,
    success: successColor,
    warning: warningColor,
    error: errorColor,
    border: '#E0E0E0',
  },
  dark: {
    // Main theme based on the reference images
    text: textColor,
    secondaryText: secondaryTextColor,
    background: primaryColor,
    card: primaryLightColor,
    primary: primaryColor,
    accent: accentColor,
    tint: accentColor,
    icon: secondaryTextColor,
    tabIconDefault: secondaryTextColor,
    tabIconSelected: accentColor,
    success: successColor,
    warning: warningColor,
    error: errorColor,
    border: '#2C5A4E',
  },
};
