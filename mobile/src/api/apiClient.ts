import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Define API base URL based on environment
const getApiBaseUrl = () => {
  // For web browser
  if (Platform.OS === 'web') {
    return 'http://localhost:8000/api';
  }

  // For iOS devices and simulators
  if (Platform.OS === 'ios') {
    return 'http://*************:8000/api'; // Your actual IP address
  }

  // For Android devices and emulators
  if (Platform.OS === 'android') {
    // ******** is the special IP for Android emulator to access host machine
    // But sometimes it doesn't work, so we use the actual IP as fallback
    try {
      return 'http://********:8000/api';
    } catch (error) {
      return 'http://*************:8000/api'; // Your actual IP address
    }
  }

  // Default fallback
  return 'http://*************:8000/api'; // Your actual IP address
};

// Create axios instance
const apiClient = axios.create({
  baseURL: getApiBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout (increased from 10 seconds)
});

// Add request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // Clear token and redirect to login
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user');

      // You can add navigation to login screen here if needed

      return Promise.reject(error);
    }

    return Promise.reject(error);
  }
);

export default apiClient;
