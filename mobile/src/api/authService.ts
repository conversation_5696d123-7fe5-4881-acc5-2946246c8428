import apiClient from './apiClient';
import expoApi<PERSON>lient, { createDirectIpClient, getLocalIpAddress } from './expoApiClient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { Platform } from 'react-native';

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  phone?: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  role: string;
  language: string;
  theme: string;
  profile_photo?: string;
  is_active: boolean;
}

// Get the appropriate API client based on platform
const getApiClient = () => {
  // Use expoApiClient for mobile platforms
  if (Platform.OS === 'ios' || Platform.OS === 'android') {
    return expoApiClient;
  }

  // Use regular apiClient for web
  return apiClient;
};

const authService = {
  login: async (data: LoginData) => {
    try {
      const client = getApiClient();
      console.log('Sending login request to:', client.defaults.baseURL + '/login');
      console.log('Login data:', { email: data.email, passwordLength: data.password?.length || 0 });
      console.log('Platform:', Platform.OS);

      const response = await client.post('/login', data);
      console.log('Login response:', response.data);

      if (response.data.token) {
        await AsyncStorage.setItem('auth_token', response.data.token);
        await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
      } else {
        console.warn('No token received in login response');
      }

      return response.data;
    } catch (error) {
      console.error('Login service error:', error);
      throw error;
    }
  },

  // Login with direct IP address as fallback
  loginWithDirectIp: async (data: LoginData) => {
    try {
      // Get the local IP address
      const localIp = getLocalIpAddress();
      const directClient = createDirectIpClient(localIp);

      console.log('Sending direct IP login request to:', directClient.defaults.baseURL + '/login');
      console.log('Login data:', { email: data.email, passwordLength: data.password?.length || 0 });
      console.log('Platform:', Platform.OS);

      const response = await directClient.post('/login', data);
      console.log('Direct IP login response:', response.data);

      if (response.data.token) {
        await AsyncStorage.setItem('auth_token', response.data.token);
        await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
      } else {
        console.warn('No token received in direct IP login response');
      }

      return response.data;
    } catch (error) {
      console.error('Direct IP login service error:', error);

      // Try with hardcoded IP as last resort
      try {
        console.log('Trying hardcoded IP as last resort');
        const hardcodedUrl = 'http://*************:8000/api/login';

        const response = await axios.post(hardcodedUrl, data, {
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }
        });

        console.log('Hardcoded IP login response:', response.data);

        if (response.data.token) {
          await AsyncStorage.setItem('auth_token', response.data.token);
          await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
        }

        return response.data;
      } catch (finalError) {
        console.error('All login attempts failed:', finalError);
        throw finalError;
      }
    }
  },

  register: async (data: RegisterData) => {
    try {
      const client = getApiClient();
      console.log('Sending register request to:', client.defaults.baseURL + '/register');
      console.log('Register data:', {
        name: data.name,
        email: data.email,
        passwordLength: data.password?.length || 0,
        passwordConfirmationLength: data.password_confirmation?.length || 0
      });
      console.log('Platform:', Platform.OS);

      const response = await client.post('/register', data);
      console.log('Register response:', response.data);

      if (response.data.token) {
        await AsyncStorage.setItem('auth_token', response.data.token);
        await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
      } else {
        console.warn('No token received in register response');
      }

      return response.data;
    } catch (error) {
      console.error('Register service error:', error);

      // Try with direct IP as fallback
      try {
        // Get the local IP address
        const localIp = getLocalIpAddress();
        const directClient = createDirectIpClient(localIp);

        console.log('Trying direct IP for register:', directClient.defaults.baseURL + '/register');
        const response = await directClient.post('/register', data);

        if (response.data.token) {
          await AsyncStorage.setItem('auth_token', response.data.token);
          await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
        }

        return response.data;
      } catch (directError) {
        console.error('Direct IP register failed:', directError);
        throw directError;
      }
    }
  },

  logout: async () => {
    try {
      const client = getApiClient();
      console.log('Sending logout request to:', client.defaults.baseURL + '/logout');
      console.log('Platform:', Platform.OS);

      await client.post('/logout');
    } catch (error) {
      console.error('Logout error:', error);

      // Try with direct IP as fallback
      try {
        const localIp = getLocalIpAddress();
        const directClient = createDirectIpClient(localIp);

        console.log('Trying direct IP for logout:', directClient.defaults.baseURL + '/logout');
        await directClient.post('/logout');
      } catch (directError) {
        console.error('Direct IP logout failed:', directError);
      }
    } finally {
      // Always clear local storage regardless of API success
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user');
    }
  },

  getCurrentUser: async (): Promise<User | null> => {
    try {
      const userJson = await AsyncStorage.getItem('user');
      return userJson ? JSON.parse(userJson) : null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  },

  updateProfile: async (data: Partial<User>) => {
    try {
      const client = getApiClient();
      console.log('Sending update profile request to:', client.defaults.baseURL + '/user');
      console.log('Platform:', Platform.OS);

      const response = await client.put('/user', data);
      console.log('Update profile response:', response.data);

      if (response.data.user) {
        await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
      }

      return response.data;
    } catch (error) {
      console.error('Update profile error:', error);

      // Try with direct IP as fallback
      try {
        const localIp = getLocalIpAddress();
        const directClient = createDirectIpClient(localIp);

        console.log('Trying direct IP for update profile:', directClient.defaults.baseURL + '/user');
        const response = await directClient.put('/user', data);

        if (response.data.user) {
          await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
        }

        return response.data;
      } catch (directError) {
        console.error('Direct IP update profile failed:', directError);
        throw directError;
      }
    }
  },

  updatePassword: async (data: { current_password: string; password: string; password_confirmation: string }) => {
    try {
      const client = getApiClient();
      console.log('Sending update password request to:', client.defaults.baseURL + '/user/password');
      console.log('Platform:', Platform.OS);

      const response = await client.put('/user/password', data);
      console.log('Update password response:', response.data);

      return response.data;
    } catch (error) {
      console.error('Update password error:', error);

      // Try with direct IP as fallback
      try {
        const localIp = getLocalIpAddress();
        const directClient = createDirectIpClient(localIp);

        console.log('Trying direct IP for update password:', directClient.defaults.baseURL + '/user/password');
        const response = await directClient.put('/user/password', data);

        return response.data;
      } catch (directError) {
        console.error('Direct IP update password failed:', directError);
        throw directError;
      }
    }
  },

  isAuthenticated: async (): Promise<boolean> => {
    const token = await AsyncStorage.getItem('auth_token');
    return !!token;
  }
};

export default authService;
