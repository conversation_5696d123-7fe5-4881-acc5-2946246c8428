import apiClient from './apiClient';

export interface Customer {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CustomerFormData {
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  notes?: string;
}

const customerService = {
  getCustomers: async (page = 1) => {
    const response = await apiClient.get(`/customers?page=${page}`);
    return response.data;
  },
  
  getCustomer: async (id: number) => {
    const response = await apiClient.get(`/customers/${id}`);
    return response.data;
  },
  
  createCustomer: async (data: CustomerFormData) => {
    const response = await apiClient.post('/customers', data);
    return response.data;
  },
  
  updateCustomer: async (id: number, data: CustomerFormData) => {
    const response = await apiClient.put(`/customers/${id}`, data);
    return response.data;
  },
  
  deleteCustomer: async (id: number) => {
    const response = await apiClient.delete(`/customers/${id}`);
    return response.data;
  },
  
  getCustomerMeasurements: async (customerId: number) => {
    const response = await apiClient.get(`/customers/${customerId}/measurements`);
    return response.data;
  },
  
  getCustomerOrders: async (customerId: number) => {
    const response = await apiClient.get(`/customers/${customerId}/orders`);
    return response.data;
  }
};

export default customerService;
