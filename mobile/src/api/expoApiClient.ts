import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

// Get the local IP address for Expo
const getLocalIpAddress = (): string => {
  try {
    // Try to get the IP address from Expo Constants
    const expoHost = Constants.expoConfig?.hostUri || '';
    const ipMatch = expoHost.match(/^([0-9.]+):/);
    if (ipMatch && ipMatch[1]) {
      return ipMatch[1];
    }
  } catch (error) {
    console.error('Error getting IP address from Expo:', error);
  }
  
  // Fallback to hardcoded IP
  return '*************';
};

// Define API base URL based on environment
const getApiBaseUrl = () => {
  // For web browser
  if (Platform.OS === 'web') {
    return 'http://localhost:8000/api';
  }
  
  // For iOS devices and simulators
  if (Platform.OS === 'ios') {
    const localIp = getLocalIpAddress();
    return `http://${localIp}:8000/api`;
  }
  
  // For Android devices and emulators
  if (Platform.OS === 'android') {
    const localIp = getLocalIpAddress();
    
    // Try both options for Android
    try {
      // For emulators, use ******** (special IP for Android emulator to access host)
      if (__DEV__) {
        return 'http://********:8000/api';
      } else {
        return `http://${localIp}:8000/api`;
      }
    } catch (error) {
      return `http://${localIp}:8000/api`;
    }
  }
  
  // Default fallback
  return `http://${getLocalIpAddress()}:8000/api`;
};

// Create axios instance
const expoApiClient = axios.create({
  baseURL: getApiBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

// Add request interceptor to add auth token
expoApiClient.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting token from AsyncStorage:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for handling common errors
expoApiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    if (error.response && error.response.status === 401) {
      // Unauthorized, clear token and user
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user');
    }
    return Promise.reject(error);
  }
);

// Direct IP client for fallback
const createDirectIpClient = (ip: string = '*************') => {
  return axios.create({
    baseURL: `http://${ip}:8000/api`,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    timeout: 30000, // 30 seconds timeout
  });
};

export { expoApiClient as default, createDirectIpClient, getLocalIpAddress };
