import apiClient from './apiClient';
import { Order } from './orderService';

export interface Invoice {
  id: number;
  order_id: number;
  invoice_number: string;
  invoice_date: string;
  due_date?: string;
  total_amount: number;
  paid_amount: number;
  status: 'paid' | 'partial' | 'unpaid';
  notes?: string;
  order?: Order;
  created_at: string;
  updated_at: string;
}

export interface InvoiceFormData {
  order_id: number;
  invoice_date: string;
  due_date?: string;
  total_amount: number;
  paid_amount: number;
  status: 'paid' | 'partial' | 'unpaid';
  notes?: string;
}

export interface InvoiceUpdateData {
  due_date?: string;
  paid_amount?: number;
  status?: 'paid' | 'partial' | 'unpaid';
  notes?: string;
}

const invoiceService = {
  getInvoices: async (page = 1, status?: string, fromDate?: string, toDate?: string) => {
    let url = `/invoices?page=${page}`;
    
    if (status) {
      url += `&status=${status}`;
    }
    
    if (fromDate && toDate) {
      url += `&from_date=${fromDate}&to_date=${toDate}`;
    }
    
    const response = await apiClient.get(url);
    return response.data;
  },
  
  getInvoice: async (id: number) => {
    const response = await apiClient.get(`/invoices/${id}`);
    return response.data;
  },
  
  createInvoice: async (data: InvoiceFormData) => {
    const response = await apiClient.post('/invoices', data);
    return response.data;
  },
  
  updateInvoice: async (id: number, data: InvoiceUpdateData) => {
    const response = await apiClient.put(`/invoices/${id}`, data);
    return response.data;
  },
  
  deleteInvoice: async (id: number) => {
    const response = await apiClient.delete(`/invoices/${id}`);
    return response.data;
  },
  
  getOrderInvoice: async (orderId: number) => {
    const response = await apiClient.get(`/orders/${orderId}/invoice`);
    return response.data;
  }
};

export default invoiceService;
