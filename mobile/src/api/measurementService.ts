import apiClient from './apiClient';
import { Customer } from './customerService';

export interface Measurement {
  id: number;
  customer_id: number;
  name?: string;
  chest?: number;
  waist?: number;
  hip?: number;
  shoulder?: number;
  sleeve_length?: number;
  neck?: number;
  inseam?: number;
  outseam?: number;
  thigh?: number;
  calf?: number;
  arm_length?: number;
  back_width?: number;
  height?: number;
  notes?: string;
  customer?: Customer;
  created_at: string;
  updated_at: string;
}

export interface MeasurementFormData {
  customer_id: number;
  name?: string;
  chest?: number;
  waist?: number;
  hip?: number;
  shoulder?: number;
  sleeve_length?: number;
  neck?: number;
  inseam?: number;
  outseam?: number;
  thigh?: number;
  calf?: number;
  arm_length?: number;
  back_width?: number;
  height?: number;
  notes?: string;
}

const measurementService = {
  getMeasurements: async (page = 1, customerId?: number) => {
    let url = `/measurements?page=${page}`;
    
    if (customerId) {
      url += `&customer_id=${customerId}`;
    }
    
    const response = await apiClient.get(url);
    return response.data;
  },
  
  getMeasurement: async (id: number) => {
    const response = await apiClient.get(`/measurements/${id}`);
    return response.data;
  },
  
  createMeasurement: async (data: MeasurementFormData) => {
    const response = await apiClient.post('/measurements', data);
    return response.data;
  },
  
  updateMeasurement: async (id: number, data: Partial<MeasurementFormData>) => {
    const response = await apiClient.put(`/measurements/${id}`, data);
    return response.data;
  },
  
  deleteMeasurement: async (id: number) => {
    const response = await apiClient.delete(`/measurements/${id}`);
    return response.data;
  },
  
  getCustomerMeasurements: async (customerId: number) => {
    const response = await apiClient.get(`/customers/${customerId}/measurements`);
    return response.data;
  }
};

export default measurementService;
