import apiClient from './apiClient';
import { Customer } from './customerService';
import { Product } from './productService';
import { User } from './authService';

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  notes?: string;
  product?: Product;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: number;
  customer_id: number;
  user_id: number;
  order_number: string;
  order_date: string;
  delivery_date?: string;
  total_amount: number;
  paid_amount: number;
  discount: number;
  status: 'pending' | 'processing' | 'completed' | 'delivered' | 'cancelled';
  notes?: string;
  customer?: Customer;
  user?: User;
  order_items?: OrderItem[];
  created_at: string;
  updated_at: string;
}

export interface OrderFormData {
  customer_id: number;
  order_date: string;
  delivery_date?: string;
  total_amount: number;
  paid_amount: number;
  discount?: number;
  status: 'pending' | 'processing' | 'completed' | 'delivered' | 'cancelled';
  notes?: string;
  order_items: {
    product_id: number;
    quantity: number;
    unit_price: number;
    total_price: number;
    notes?: string;
  }[];
}

export interface OrderUpdateData {
  delivery_date?: string;
  paid_amount?: number;
  status?: 'pending' | 'processing' | 'completed' | 'delivered' | 'cancelled';
  notes?: string;
}

const orderService = {
  getOrders: async (page = 1, status?: string, fromDate?: string, toDate?: string) => {
    let url = `/orders?page=${page}`;
    
    if (status) {
      url += `&status=${status}`;
    }
    
    if (fromDate && toDate) {
      url += `&from_date=${fromDate}&to_date=${toDate}`;
    }
    
    const response = await apiClient.get(url);
    return response.data;
  },
  
  getOrder: async (id: number) => {
    const response = await apiClient.get(`/orders/${id}`);
    return response.data;
  },
  
  createOrder: async (data: OrderFormData) => {
    const response = await apiClient.post('/orders', data);
    return response.data;
  },
  
  updateOrder: async (id: number, data: OrderUpdateData) => {
    const response = await apiClient.put(`/orders/${id}`, data);
    return response.data;
  },
  
  deleteOrder: async (id: number) => {
    const response = await apiClient.delete(`/orders/${id}`);
    return response.data;
  },
  
  getDashboardStats: async () => {
    const response = await apiClient.get('/dashboard/stats');
    return response.data;
  },
  
  getRecentOrders: async () => {
    const response = await apiClient.get('/dashboard/recent-orders');
    return response.data;
  },
  
  getOrdersReport: async (fromDate?: string, toDate?: string, status?: string) => {
    let url = '/reports/orders';
    const params = new URLSearchParams();
    
    if (fromDate) params.append('from_date', fromDate);
    if (toDate) params.append('to_date', toDate);
    if (status) params.append('status', status);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    const response = await apiClient.get(url);
    return response.data;
  },
  
  getRevenueReport: async (fromDate?: string, toDate?: string) => {
    let url = '/reports/revenue';
    const params = new URLSearchParams();
    
    if (fromDate) params.append('from_date', fromDate);
    if (toDate) params.append('to_date', toDate);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    const response = await apiClient.get(url);
    return response.data;
  }
};

export default orderService;
