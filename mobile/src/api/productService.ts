import apiClient from './apiClient';

export interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  quantity: number;
  sku?: string;
  category?: string;
  image_path?: string;
  created_at: string;
  updated_at: string;
}

export interface ProductFormData {
  name: string;
  description?: string;
  price: number;
  quantity: number;
  sku?: string;
  category?: string;
  image_path?: string;
}

const productService = {
  getProducts: async (page = 1, category?: string, search?: string) => {
    let url = `/products?page=${page}`;
    
    if (category) {
      url += `&category=${category}`;
    }
    
    if (search) {
      url += `&search=${search}`;
    }
    
    const response = await apiClient.get(url);
    return response.data;
  },
  
  getProduct: async (id: number) => {
    const response = await apiClient.get(`/products/${id}`);
    return response.data;
  },
  
  createProduct: async (data: ProductFormData) => {
    const response = await apiClient.post('/products', data);
    return response.data;
  },
  
  updateProduct: async (id: number, data: ProductFormData) => {
    const response = await apiClient.put(`/products/${id}`, data);
    return response.data;
  },
  
  deleteProduct: async (id: number) => {
    const response = await apiClient.delete(`/products/${id}`);
    return response.data;
  },
  
  getInventorySummary: async () => {
    const response = await apiClient.get('/dashboard/inventory-summary');
    return response.data;
  }
};

export default productService;
