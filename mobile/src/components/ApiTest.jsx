import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Platform } from 'react-native';
import axios from 'axios';

// Define API base URL based on environment
const getApiBaseUrl = () => {
  // For web browser
  if (Platform.OS === 'web') {
    return 'http://localhost/api';
  }

  // For iOS devices and simulators
  if (Platform.OS === 'ios') {
    return 'http://*************/api'; // Your actual IP address
  }

  // For Android devices and emulators
  if (Platform.OS === 'android') {
    // Try both options for Android
    return 'http://*************/api'; // Your actual IP address
  }

  // Default fallback
  return 'http://*************/api'; // Your actual IP address
};

const API_URL = getApiBaseUrl();

const ApiTest = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const testApi = async () => {
    setLoading(true);
    setResult(null);
    setError(null);

    try {
      console.log('Testing API connection to:', `${API_URL}/test`);
      console.log('Platform:', Platform.OS);

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await axios.get(`${API_URL}/test?_=${timestamp}`, {
        timeout, // 30 seconds timeout
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });

      console.log('API test response:', response.data);
      setResult(JSON.stringify(response.data, null, 2));
    } catch (err) {
      console.error('API test error:', err);

      let errorMessage = 'Unknown error';

      if (err.response) {
        // The request w and the server responded with a status code
        // that falls out of the range of 2xx
        errorMessage = `Error ${err.response.status}: ${JSON.stringify(err.response.data)}`;
        console.error('Error response:', {
          data.response.data,
          status.response.status,
          headers.response.headers,
        });
      } else if (err.request) {
        // The request w but no response w
        errorMessage = `Network Error response received. Check if the server is running and accessible.`;
        console.error('Error request:', err.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = `Error: ${err.message}`;
        console.error('Error message:', err.message);
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    setResult(null);
    setError(null);

    try {
      console.log('Testing login API:', `${API_URL}/login`);
      console.log('Platform:', Platform.OS);

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await axios.post(`${API_URL}/login?_=${timestamp}`, {
        email: '<EMAIL>',
        password: 'password'
      }, {
        timeout, // 30 seconds timeout
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });

      console.log('Login test response:', response.data);
      setResult(JSON.stringify(response.data, null, 2));
    } catch (err) {
      console.error('Login test error:', err);

      let errorMessage = 'Unknown error';

      if (err.response) {
        // The request w and the server responded with a status code
        // that falls out of the range of 2xx
        errorMessage = `Error ${err.response.status}: ${JSON.stringify(err.response.data)}`;
        console.error('Error response:', {
          data.response.data,
          status.response.status,
          headers.response.headers,
        });
      } else if (err.request) {
        // The request w but no response w
        errorMessage = `Network Error response received. Check if the server is running and accessible.`;
        console.error('Error request:', err.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = `Error: ${err.message}`;
        console.error('Error message:', err.message);
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Test with direct IP address
  const testDirectIp = async () => {
    setLoading(true);
    setResult(null);
    setError(null);

    try {
      const directUrl = 'http://*************/api/test';
      console.log('Testing direct IP connection to:', directUrl);

      const response = await axios.get(directUrl, {
        timeout,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });

      console.log('Direct IP test response:', response.data);
      setResult(`Direct IP test successful: ${JSON.stringify(response.data, null, 2)}`);
    } catch (err) {
      console.error('Direct IP test error:', err);
      setError(`Direct IP test failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    
      API Connection Test

      
        Test API Connection
      

      
        Test Login API
      

      
        Test Direct IP Connection
      

      {loading && (
        
          
          Testing API...
        
      )}

      {result && (
        
          Success:
          {result}
        
      )}

      {error && (
        
          Error:
          {error}
        
      )}
    
  );
};

const styles = StyleSheet.create({
  container: {
    padding,
    backgroundColor: '#f5f5f5',
    borderRadius,
    margin,
  },
  title: {
    fontSize,
    fontWeight: 'bold',
    marginBottom,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#6200ee',
    padding,
    borderRadius,
    alignItems: 'center',
    marginBottom,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  loadingContainer: {
    marginTop,
    alignItems: 'center',
  },
  loadingText: {
    marginTop,
    color: '#666',
  },
  resultContainer: {
    marginTop,
    padding,
    backgroundColor: '#e6f7e6',
    borderRadius,
  },
  resultTitle: {
    fontWeight: 'bold',
    color: '#2e7d32',
    marginBottom,
  },
  resultText: {
    fontFamily: 'monospace',
  },
  errorContainer: {
    marginTop,
    padding,
    backgroundColor: '#ffebee',
    borderRadius,
  },
  errorTitle: {
    fontWeight: 'bold',
    color: '#c62828',
    marginBottom,
  },
  errorText: {
    fontFamily: 'monospace',
  },
});

export default ApiTest;
