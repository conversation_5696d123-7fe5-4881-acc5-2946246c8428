import React from 'react';
import { StyleSheet, TouchableOpacity, Text, ActivityIndicator, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '../../context/ThemeContext';



const Button.FC = ({
  title,
  onPress,
  type = 'primary',
  loading = false,
  disabled = false,
  style,
  textStyle,
  icon
}) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  const getButtonStyle = () => {
    switch (type) {
      case 'primary':
        return {
          backgroundColor ? '#6200ee' : '#6200ee',
          borderColor: 'transparent'
        };
      case 'secondary':
        return {
          backgroundColor ? '#292929' : '#e0e0e0',
          borderColor: 'transparent'
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor ? '#6200ee' : '#6200ee'
        };
      case 'danger':
        return {
          backgroundColor ? '#cf6679' : '#b00020',
          borderColor: 'transparent'
        };
      default {
          backgroundColor ? '#6200ee' : '#6200ee',
          borderColor: 'transparent'
        };
    }
  };

  const getTextStyle = () => {
    switch (type) {
      case 'primary':
        return { color: '#ffffff' };
      case 'secondary':
        return { color ? '#ffffff' : '#000000' };
      case 'outline':
        return { color ? '#6200ee' : '#6200ee' };
      case 'danger':
        return { color: '#ffffff' };
      default { color: '#ffffff' };
    }
  };

  return (
    
      {loading ? (
        
      ) : (
        <>
          {icon && icon}
          {title}
        
      )}
    
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding,
    borderRadius,
    borderWidth,
    minWidth,
  },
  text: {
    fontSize,
    fontWeight: '600',
    textAlign: 'center',
    marginLeft,
  },
  disabled: {
    opacity.5,
  },
});

export default Button;
