import React from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { useTheme } from '../../context/ThemeContext';



const Card.FC = ({ children, style }) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    
      {children}
    
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius,
    padding,
    marginVertical,
    shadowOffset: {
      width,
      height,
    },
    shadowOpacity.1,
    shadowRadius,
    elevation,
  },
});

export default Card;
