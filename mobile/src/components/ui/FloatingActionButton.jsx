import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View, Text, Animated, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useTranslation } from 'react-i18next';





const FloatingActionButton.FC = ({ actions }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { theme } = useTheme();
  const { t } = useTranslation();
  const isDark = theme === 'dark';

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleActionPress = (action) => {
    setIsOpen(false);
    action.onPress();
  };

  return (
    <>
       setIsOpen(false)}
      >
         setIsOpen(false)}
        >
          
            {actions.map((action, index) => (
               handleActionPress(action)}
              >
                
                
                  {action.label}
                
              
            ))}
          
        
      

      
        
      
    
  );
};

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    bottom,
    right,
    width,
    height,
    borderRadius,
    justifyContent: 'center',
    alignItems: 'center',
    elevation,
    shadowColor: '#000',
    shadowOffset: { width, height },
    shadowOpacity.3,
    shadowRadius,
    zIndex,
  },
  modalOverlay: {
    flex,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    padding,
  },
  actionsContainer: {
    marginBottom,
    alignItems: 'flex-end',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding,
    borderRadius,
    marginBottom,
    elevation,
    shadowColor: '#000',
    shadowOffset: { width, height },
    shadowOpacity.2,
    shadowRadius.5,
  },
  actionLabel: {
    marginLeft,
    fontSize,
    fontWeight: '500',
  },
});

export default FloatingActionButton;
