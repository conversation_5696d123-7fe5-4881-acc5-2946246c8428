import React, { useState } from 'react';
import { StyleSheet, TextInput, View, Text, TextInputProps, ViewStyle, TouchableOpacity } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
}

const Input.FC = ({
  label,
  error,
  containerStyle,
  rightIcon,
  onRightIconPress,
  secureTextEntry,
  ...props
}) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [showPassword, setShowPassword] = useState(false);

  // Handle password visibility toggle
  const isPassword = secureTextEntry !== undefined;
  const actualSecureTextEntry = isPassword ? !showPassword : secureTextEntry;

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    
      {label && (
        
          {label}
        
      )}
      
        

        {isPassword && (
          
            
          
        )}

        {rightIcon && !isPassword && (
          
            {rightIcon}
          
        )}
      
      {error && {error}}
    
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom,
  },
  label: {
    fontSize,
    marginBottom,
    fontWeight: '500',
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    flex,
    height,
    borderWidth,
    borderRadius,
    paddingHorizontal,
    fontSize,
  },
  rightIconContainer: {
    position: 'absolute',
    right,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  error: {
    color: '#b00020',
    fontSize,
    marginTop,
  },
});

export default Input;
