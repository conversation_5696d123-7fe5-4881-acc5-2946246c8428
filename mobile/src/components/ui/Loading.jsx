import React from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import { useTranslation } from 'react-i18next';



const Loading.FC = ({ message, fullScreen = false }) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const isDark = theme === 'dark';

  return (
    
      
      {message && (
        
          {message || t('loading')}
        
      )}
    
  );
};

const styles = StyleSheet.create({
  container: {
    padding,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius,
  },
  fullScreen: {
    flex,
    position: 'absolute',
    top,
    left,
    right,
    bottom,
    zIndex,
  },
  message: {
    marginTop,
    fontSize,
    textAlign: 'center',
  },
});

export default Loading;
