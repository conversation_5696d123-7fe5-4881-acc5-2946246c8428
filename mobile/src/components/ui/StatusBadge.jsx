import React from 'react';
import { StyleSheet, Text, View, ViewStyle } from 'react-native';
import { useTranslation } from 'react-i18next';



const StatusBadge.FC = ({ status, style }) => {
  const { t } = useTranslation();

  const getStatusColor = () => {
    switch (status) {
      case 'pending':
        return { backgroundColor: '#FFC107', textColor: '#000000' };
      case 'processing':
        return { backgroundColor: '#2196F3', textColor: '#FFFFFF' };
      case 'completed':
        return { backgroundColor: '#4CAF50', textColor: '#FFFFFF' };
      case 'delivered':
        return { backgroundColor: '#8BC34A', textColor: '#000000' };
      case 'cancelled':
        return { backgroundColor: '#F44336', textColor: '#FFFFFF' };
      case 'paid':
        return { backgroundColor: '#4CAF50', textColor: '#FFFFFF' };
      case 'partial':
        return { backgroundColor: '#FFC107', textColor: '#000000' };
      case 'unpaid':
        return { backgroundColor: '#F44336', textColor: '#FFFFFF' };
      default { backgroundColor: '#9E9E9E', textColor: '#FFFFFF' };
    }
  };

  const { backgroundColor, textColor } = getStatusColor();

  return (
    
      {t(status)}
    
  );
};

const styles = StyleSheet.create({
  badge: {
    paddingHorizontal,
    paddingVertical,
    borderRadius,
    alignSelf: 'flex-start',
  },
  text: {
    fontSize,
    fontWeight: 'bold',
  },
});

export default StatusBadge;
