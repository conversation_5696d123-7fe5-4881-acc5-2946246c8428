import React, { createContext, useState, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import authService, { User, LoginData, RegisterData } from '../api/authService';



const AuthContext = createContext({
  user,
  isLoading,
  isAuthenticated,
  isAdmin,
  userRole,
  login () => {},
  register () => {},
  logout () => {},
  updateUser () => {},
  hasPermission: () => false,
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider.FC = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [userRole, setUserRole] = useState(null);

  // Update admin status and role when user changes
  useEffect(() => {
    if (user) {
      setIsAdmin(user.role === 'admin');
      setUserRole(user.role);
    } else {
      setIsAdmin(false);
      setUserRole(null);
    }
  }, [user]);

  useEffect(() => {
    // Load user from AsyncStorage on app start
    const loadUser = async () => {
      try {
        const userJson = await AsyncStorage.getItem('user');
        const token = await AsyncStorage.getItem('auth_token');

        if (userJson && token) {
          const parsedUser = JSON.parse(userJson);
          setUser(parsedUser);
          setIsAuthenticated(true);
          setIsAdmin(parsedUser.role === 'admin');
          setUserRole(parsedUser.role);
        }
      } catch (error) {
        console.error('Error loading user:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  const login = async (data) => {
    try {
      setIsLoading(true);
      console.log('AuthContext login with:', { email.email });

      // Try direct IP approach if normal login fails
      try {
        const response = await authService.login(data);
        console.log('AuthContext successful');
        setUser(response.user);
        setIsAuthenticated(true);
        setIsAdmin(response.user.role === 'admin');
        setUserRole(response.user.role);
      } catch (firstError) {
        console.error('AuthContext login attempt failed, trying direct IP:', firstError);

        // Try with direct IP 
        const directResponse = await authService.loginWithDirectIp(data);
        console.log('AuthContext IP login successful');
        setUser(directResponse.user);
        setIsAuthenticated(true);
        setIsAdmin(directResponse.user.role === 'admin');
        setUserRole(directResponse.user.role);
      }
    } catch (error) {
      console.error('AuthContext login attempts failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data) => {
    try {
      setIsLoading(true);
      const response = await authService.register(data);
      setUser(response.user);
      setIsAuthenticated(true);
      setIsAdmin(response.user.role === 'admin');
      setUserRole(response.user.role);
    } catch (error) {
      console.error('Register error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserRole(null);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user h specific permission
  const hasPermission = (permission) => {
    // For now, we'll use a simple role-based check
    // In the future, this could be expanded to more granular permissions
    if (!user) return false;

    switch (permission) {
      case 'admin.access':
        return user.role === 'admin';
      case 'user.access':
        return user.role === 'user' || user.role === 'admin';
      case 'orders.create':
      case 'customers.create':
      case 'measurements.create':
        return true; // Both admin and users can create these
      case 'users.manage':
      case 'settings.system':
        return user.role === 'admin';
      default false;
    }
  };

  const updateUser = async (data) => {
    try {
      setIsLoading(true);
      const response = await authService.updateProfile(data);
      setUser(response.user);

      // Update role-related state if role h
      if (response.user.role !== userRole) {
        setIsAdmin(response.user.role === 'admin');
        setUserRole(response.user.role);
      }
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    
      {children}
    
  );
};
