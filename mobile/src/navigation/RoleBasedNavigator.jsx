import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import AdminTabNavigator from './AdminTabNavigator';
import UserTabNavigator from './UserTabNavigator';

/**
 * Role-Based Navigator Component
 * 
 * This component renders different navigation stacks based on the user's role.
 * - Admin users see the AdminTabNavigator
 * - Regular users see the UserTabNavigator
 * - Shows a loading indicator while authentication state is being determined
 */
export default function RoleBasedNavigator() {
  const { isAdmin, isLoading, isAuthenticated, userRole } = useAuth();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // Show loading indicator while determining auth state
  if (isLoading) {
    return (
      
        
        
          Loading...
        
      
    );
  }
  
  // If not authenticated, this component shouldn't be rendered
  // The app's root navigator should handle redirecting to login
  if (!isAuthenticated) {
    return null;
  }
  
  // Render the appropriate navigator based on user role
  return isAdmin ?  : ;
}
