import React from 'react';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useTranslation } from 'react-i18next';

/**
 * Tab Navigator for Regular users
 * This navigator provides access to user-specific screens
 */
export default function UserTabNavigator() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const isDark = theme === 'dark';
  
  // Colors based on theme
  const activeColor = isDark ? '#00A884' : '#0E3B31';
  const inactiveColor = isDark ? '#A0B0AD' : '#A0B0AD';
  const backgroundColor = isDark ? '#121212' : '#FFFFFF';
  
  return (
    
       (
            
          ),
        }}
      />
      
       (
            
          ),
        }}
      />
      
       (
            
          ),
        }}
      />
      
       (
            
          ),
        }}
      />
      
       (
            
          ),
        }}
      />
    
  );
}
