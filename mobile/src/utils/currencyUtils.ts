export const formatCurrency = (
  amount: number,
  currency: string = '৳',
  locale: string = 'en-US'
): string => {
  if (amount === null || amount === undefined) return '';
  
  try {
    // Format the number with commas
    const formattedNumber = new Intl.NumberFormat(locale, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
    
    // Add the currency symbol
    return `${currency} ${formattedNumber}`;
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `${currency} ${amount}`;
  }
};

export const calculateTotal = (items: { total_price: number }[]): number => {
  return items.reduce((sum, item) => sum + (item.total_price || 0), 0);
};

export const calculateTotalWithDiscount = (
  total: number,
  discount: number = 0
): number => {
  return Math.max(0, total - discount);
};
