import { format, parseISO } from 'date-fns';
import { enUS } from 'date-fns/locale/en-US';
import { bn } from 'date-fns/locale/bn';

export const formatDate = (
  date: string | Date,
  formatStr: string = 'PPP',
  locale: string = 'en'
): string => {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const localeObj = locale === 'bn' ? bn : enUS;

  try {
    return format(dateObj, formatStr, { locale: localeObj });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

export const formatDateTime = (
  date: string | Date,
  formatStr: string = 'PPp',
  locale: string = 'en'
): string => {
  return formatDate(date, formatStr, locale);
};

export const getCurrentDate = (formatStr: string = 'yyyy-MM-dd'): string => {
  return format(new Date(), formatStr);
};

export const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

export const formatDateForAPI = (date: Date): string => {
  return format(date, 'yyyy-MM-dd');
};
